{"name": "next-base", "version": "1.0.0", "description": "", "keywords": [], "author": "", "license": "MIT", "engines": {"node": ">=22.0.0"}, "scripts": {"predev": "npm run i18n:prepare", "dev": "next dev", "dev:light": "next dev", "prebuild": "npm run i18n:prepare", "build": "next build", "postbuild": "next-sitemap --config next-sitemap.config.js", "export": "next export", "start": "next start", "prepare": "husky install || true", "update": "npx npm-check-updates -u", "i18n:prepare": "npm run i18n:compile && npm run i18n:transpile-locales && npm run i18n:export-urls", "i18n:transpile-locales": "tsc --p tsconfig-locales.json", "i18n:export-urls": "ts-node --project tsconfig-locales.json src/i18n/exportLocalizedUrls.tsx", "i18n:extract": "npx @formatjs/cli extract \"src/**/*.ts*\" --ignore=\"**/*.d.ts\" --id-interpolation-pattern [sha512:contenthash:base64:6] --out-file src/i18n/extracted/cs.json --format src/i18n/format.js", "i18n:compile": "npx @formatjs/cli compile-folder --ast src/i18n/translated src/i18n/compiled --format src/i18n/compile.js", "prei18n:upload": "npm run i18n:extract", "i18n:upload": "node ./src/i18n/upload.mjs", "i18n:download": "node ./src/i18n/download.mjs", "posti18n:download": "npm run i18n:compile", "analyze": "cross-env ANALYZE=true next build", "analyze:server": "cross-env BUNDLE_ANALYZE=server next build", "analyze:browser": "cross-env BUNDLE_ANALYZE=browser next build"}, "lint-staged": {"*.scss": ["stylelint --fix --cache"], "*.{ts,tsx}": ["tsc-files --project tsconfig.json --noEmit --incremental false src/types/global.ts", "eslint --cache --fix"]}, "dependencies": {"@mapbox/mapbox-gl-draw": "^1.5.0", "@next/bundle-analyzer": "^12.0.10", "@reach/dialog": "^0.16.2", "@reach/portal": "^0.16.2", "@turf/turf": "^6.5.0", "clsx": "^1.1.1", "cross-env": "^7.0.3", "embla-carousel-react": "^7.0.3", "front-matter": "^4.0.2", "js-base64": "^3.7.2", "mapbox-gl": "^3.15.0", "marked": "^4.0.10", "next": "^12.0.8", "react": "^17.0.2", "react-dom": "^17.0.2", "react-hook-form": "^7.24.0", "react-input-range": "^1.3.0", "react-intl": "^5.24.2", "react-share": "^4.4.0", "uuid": "^8.3.2"}, "devDependencies": {"@formatjs/cli": "^4.7.1", "@superkoders/prettier-config": "0.2.6", "@superkoders/stylelint-config": "2.1.0", "@types/glob": "^7.2.0", "@types/mapbox__mapbox-gl-draw": "^1.4.9", "@types/marked": "^4.0.1", "@types/node": "^17.0.8", "@types/react": "^17.0.38", "@types/react-dom": "^17.0.11", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.9.1", "babel-plugin-formatjs": "^10.3.15", "dotenv": "^16.0.0", "eslint": "^8.6.0", "eslint-config-next": "^12.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "glob": "^8.0.1", "husky": "^7.0.4", "install": "^0.13.0", "lint-staged": "^12.1.7", "next-sitemap": "^2.5.20", "node-fetch": "^3.2.0", "npm": "^9.1.2", "prettier": "^2.5.1", "react-number-format": "^4.9.1", "sass": "^1.48.0", "stylelint": "^14.2.0", "ts-node": "^10.7.0", "tsc-files": "^1.1.3", "tsconfig-paths": "^3.14.1", "typescript": "^4.5.4"}, "volta": {"node": "22.14.0", "npm": "10.9.2"}}