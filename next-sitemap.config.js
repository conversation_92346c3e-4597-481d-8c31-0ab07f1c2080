// @ts-check
/* eslint-disable @typescript-eslint/no-var-requires */
/**
 * @type {import('./src/i18n/exportLocalizedUrls').LocalizedUrls}
 */
const rewritesMap = require('./src/i18n/localizedUrls.json');

const getBaseUrl = () => {
	const protocol = `http${process.env.VERCEL_ENV === 'production' || process.env.NEXT_PUBLIC_VERCEL_ENV === 'production' ? 's' : ''}`;
	const host =
		process.env.PRODUCTION_URL ??
		process.env.NEXT_PUBLIC_PRODUCTION_URL ??
		process.env.VERCEL_URL ??
		process.env.NEXT_PUBLIC_VERCEL_URL;

	return `${protocol}://${host}`;
};

module.exports = {
	siteUrl: getBaseUrl(),
	changefreq: undefined,
	priority: undefined,
	generateRobotsTxt: true,
	autoLastmod: false,
	exclude: ['/moje-filtry', '/muj-profil', '/administrace*', '/dekujeme', '/autor/*', '/inzerce*', '/lp-*', '/en/*'],
	robotsTxtOptions: {
		policies: [
			{
				userAgent: '*',
				allow: '/',
				disallow: [
					'/moje-filtry',
					'/muj-profil',
					'/administrace',
					'/dekujeme',
					'/en/my-filters',
					'/en/my-profile',
					'/en/administration',
					'/en/thank-you',
				],
			},
		],
	},
	additionalPaths: async (config) => {
		const result = [];
		const excludePatterns = [
			/\[.*]/,
			/lp-.*/,
			/\/cs\//,
			/\/inzerce/,
			/\/autor/,
			/\/thank-you/,
			/404/,
			/\/admin/,
			/\/dekujeme/,
			/\/moje-filtry/,
			/\/muj-profil/,
			/\/my-filters/,
			/\/my-profile/,
		];
		rewritesMap.flatMap(({ name, urls }) => {
			const items = [name, ...Object.entries(urls).map((parts) => '/' + parts.join('/').replace(/\/\//g, '/'))];
			result.push(...items);
		});
		return Promise.all(
			result.filter((url) => !excludePatterns.some((regex) => url.match(regex))).map((item) => config.transform(config, item)),
		);
	},
};
