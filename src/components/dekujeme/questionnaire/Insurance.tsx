import { FC } from 'react';
import { SpecificQuestionProps, QuestionnaireInterest } from 'types/questionnaire';
import { addUserInterest } from 'utils/questionnaire';
import { Question } from './Question';
import { QuestionnaireUserDetails } from './userDetailsForm/QuestionnaireUserDetails';

export const Insurance: FC<SpecificQuestionProps> = ({
	moveToNextStep,
	addNotification,
	hasConfirmedUserDetails,
	confirmUserDetails,
	userDetails,
	questionnaire,
}) => {
	const onInterestExpression = () => {
		addUserInterest({ interest: QuestionnaireInterest.INSURANCE, addNotification, questionnaireId: questionnaire.id });

		if (hasConfirmedUserDetails) {
			moveToNextStep();
		}
	};

	return (
		<Question
			questionStep={3}
			onInterestRefusal={moveToNextStep}
			onInterestExpression={onInterestExpression}
			skipMoreDetails={hasConfirmedUserDetails}
			skipQuestion={moveToNextStep}
		>
			<QuestionnaireUserDetails
				questionnaire={questionnaire}
				moveToNextStep={moveToNextStep}
				addNotification={addNotification}
				variant="full-form"
				confirmUserDetails={confirmUserDetails}
				defaultValues={userDetails}
			/>
		</Question>
	);
};
