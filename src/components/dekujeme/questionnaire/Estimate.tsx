import { FC } from 'react';
import { SpecificQuestionProps, QuestionnaireInterest } from 'types/questionnaire';
import { addUserInterest } from 'utils/questionnaire';
import { EstimateForm } from './estimateForm/EstimateForm';
import { Question } from './Question';

type Props = SpecificQuestionProps & { userEmail: string };

export const Estimate: FC<Props> = ({
	moveToNextStep,
	userEmail,
	addNotification,
	hasConfirmedUserDetails,
	confirmUserDetails,
	userDetails,
	questionnaire,
}) => {
	const onInterestExpression = () =>
		addUserInterest({ interest: QuestionnaireInterest.ESTIMATE, addNotification, questionnaireId: questionnaire.id });

	return (
		<Question
			questionStep={2}
			onInterestRefusal={moveToNextStep}
			onInterestExpression={onInterestExpression}
			skipQuestion={moveToNextStep}
		>
			<EstimateForm
				email={userEmail}
				addNotification={addNotification}
				moveToNextStep={moveToNextStep}
				hasConfirmedUserDetails={hasConfirmedUserDetails}
				confirmUserDetails={confirmUserDetails}
				userDetails={userDetails}
				questionnaire={questionnaire}
			/>
		</Question>
	);
};
