import { fetchQuestionnairePatch } from 'api/questionnaire/fetchQuestionnairePatch';
import { FC } from 'react';
import { OfferType } from 'types/offer';
import { SpecificQuestionProps, QuestionnaireInterest, QuestionnaireFilter } from 'types/questionnaire';
import { addUserInterest } from 'utils/questionnaire';
import { Question } from './Question';
import { QuestionnaireUserDetails } from './userDetailsForm/QuestionnaireUserDetails';

type Props = SpecificQuestionProps &
	QuestionnaireFilter & {
		isPartialQuestionnaire: boolean;
		stopQuestionnaire: () => void;
	};

export const Mortgage: FC<Props> = ({
	moveToNextStep,
	addNotification,
	filterType,
	confirmUserDetails,
	userDetails,
	isPartialQuestionnaire,
	stopQuestionnaire,
	questionnaire,
}) => {
	const interestToAdd = filterType === OfferType.SALE ? QuestionnaireInterest.MORTGAGE : QuestionnaireInterest.RENTING_OWN_HOUSE;

	const onInterestExpression = () => addUserInterest({ interest: interestToAdd, addNotification, questionnaireId: questionnaire.id });

	const onInterestRefusal = () => {
		fetchQuestionnairePatch({}, questionnaire.id);
		moveToNextStep();
	};

	return (
		<Question
			questionStep={1}
			onInterestRefusal={onInterestRefusal}
			onInterestExpression={onInterestExpression}
			filterType={filterType}
			skipQuestion={moveToNextStep}
		>
			<>
				<p className="u-mb-sm">Pro získání nejvýhodnější úrokové sazby zadejte kontaktní údaje</p>
				<QuestionnaireUserDetails
					questionnaire={questionnaire}
					moveToNextStep={moveToNextStep}
					addNotification={addNotification}
					variant="full-form"
					confirmUserDetails={confirmUserDetails}
					defaultValues={userDetails}
					isPartialQuestionnaire={isPartialQuestionnaire}
					stopQuestionnaire={stopQuestionnaire}
				/>
			</>
		</Question>
	);
};
