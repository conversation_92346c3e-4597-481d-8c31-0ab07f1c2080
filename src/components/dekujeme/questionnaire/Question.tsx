import { FC, useState } from 'react';
import { OfferType } from 'types/offer';
import { QuestionnaireFilterType, QuestionProps, QuestionType, Step } from 'types/questionnaire';
import { QuestionWrapper } from './QuestionWrapper';

const getQuestionDetails = (step: Step, filterType: QuestionnaireFilterType = OfferType.SALE) => {
	const isRentFilter = filterType === OfferType.RENT;

	const questionsMap: Record<Step, QuestionType> = {
		1: {
			heading: isRentFilter
				? 'Zajímala by vás možnost bydlet ve vlastním za cenu nájmu?'
				: '<PERSON><PERSON><PERSON> zájem o hypotéku s nejvýhodnějším úrokem?',
		},
		2: { heading: 'Chtěli byste získat odhad ceny své nemovitosti?', subHeading: 'Odhad zabere maximálně 5 minut' },
		3: { heading: '<PERSON><PERSON><PERSON> z<PERSON>je<PERSON> o nejlep<PERSON> poji<PERSON>t<PERSON><PERSON><PERSON>mo<PERSON>?' },
	};

	return questionsMap[step];
};

export const Question: FC<QuestionProps> = ({
	onInterestExpression,
	onInterestRefusal,
	questionStep,
	filterType = OfferType.SALE,
	children,
	skipMoreDetails = false,
	skipQuestion,
}) => {
	const [canShowMoreDetails, setCanShowMoreDetails] = useState(false);

	const onConfirm = () => {
		onInterestExpression();
		if (!skipMoreDetails) {
			setCanShowMoreDetails(true);
		}
	};

	const questionDetails = getQuestionDetails(questionStep, filterType);

	return (
		<QuestionWrapper question={questionDetails} showSubHeading={canShowMoreDetails} skipQuestion={skipQuestion}>
			{canShowMoreDetails ? children : <YesNoButtons onInterestExpression={onConfirm} onInterestRefusal={onInterestRefusal} />}
		</QuestionWrapper>
	);
};

const YesNoButtons: FC<Pick<QuestionProps, 'onInterestExpression' | 'onInterestRefusal'>> = ({
	onInterestExpression,
	onInterestRefusal,
}) => {
	return (
		<div className="u-mb-xs">
			<div className="grid grid--center grid--x-xs">
				<div className="grid__cell size--auto">
					<a href="#" className="btn btn--md btn--miw-100" onClick={onInterestExpression}>
						<span className="btn__text">Ano</span>
					</a>
				</div>
				<div className="grid__cell size--auto">
					<a href="#" className="btn btn--md btn--miw-100" onClick={onInterestRefusal}>
						<span className="btn__text">Ne</span>
					</a>
				</div>
			</div>
		</div>
	);
};
