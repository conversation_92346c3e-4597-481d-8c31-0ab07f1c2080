import clsx from 'clsx';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { filterInltMessages } from 'components/ui/filter/filterIntlMessages';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { ChangeEvent, FC } from 'react';
import { useFormContext } from 'react-hook-form';
import InputRange from 'react-input-range';
import { FormattedMessage, useIntl } from 'react-intl';
import { checkFormError } from 'utils/checkFormError';
import { formatNumber } from 'utils/formats';
import { sanitizeInputNumber } from 'utils/sanitizeInputNumber';

type Props = {
	variant: 'real-estate' | 'land';
	required?: boolean;
};

const MIN = 0;

export const AreaSlider: FC<Props> = ({ variant, required = false }) => {
	const {
		setValue,
		watch,
		register,
		formState: { errors },
	} = useFormContext();
	const { areaSqmIntlString, landareaSqmIntlString } = filterInltMessages;
	const isRealEstateArea = variant === 'real-estate';
	const labelIntl = isRealEstateArea ? areaSqmIntlString : landareaSqmIntlString;
	const maxValue = isRealEstateArea ? 1000 : 10000;
	const valueToWatch = isRealEstateArea ? 'estimateData.area' : 'estimateData.landArea';
	const watchedArea = watch(valueToWatch);
	const hasError = checkFormError(valueToWatch, errors);

	const formatLabel = (area: number) => `${area.toString()} m²`;
	const { formatMessage } = useIntl();
	const { minimumAreaIntlString } = commonInltMessages;
	const minimumAreaMessage = formatMessage(minimumAreaIntlString);

	const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
		const formattedValue = formatInputValue(event.target.value);

		setValue(valueToWatch, formattedValue);
	};

	const formatInputValue = (value: string | number | null) => {
		if (!value) return MIN;
		const sanitized = sanitizeInputNumber(value);

		switch (true) {
			case isNaN(sanitized) || sanitized <= MIN:
				return MIN;

			case sanitized >= maxValue:
				return maxValue;

			default:
				return sanitized;
		}
	};

	return (
		<div className={clsx('inp-row', hasError && 'has-error')}>
			<Label required={required}>
				<FormattedMessage {...labelIntl} />
			</Label>
			<span className="inp-fix">
				<input type="text" id={valueToWatch} className="inp-text" value={formatNumber(watchedArea)} onChange={handleInputChange} />
			</span>
			{required ? (
				<>
					<FormError name={valueToWatch} />
					<input
						type="hidden"
						{...register(valueToWatch, {
							min: {
								value: 5,
								message: minimumAreaMessage,
							},
						})}
					/>
				</>
			) : null}
			<InputRange
				minValue={MIN}
				maxValue={maxValue}
				value={watchedArea}
				onChange={(value) => setValue(valueToWatch, value)}
				step={5}
				formatLabel={formatLabel}
			/>
		</div>
	);
};
