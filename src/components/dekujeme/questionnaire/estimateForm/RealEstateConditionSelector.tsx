import { CheckboxOrRadioGroup } from 'components/ui/core/CheckboxOrRadioGroup';
import { FC } from 'react';
import { ConditionType } from 'types/offer';

type Props = {
	required?: boolean;
};

export const RealEstateConditionSelector: FC<Props> = ({ required = false }) => {
	return (
		<CheckboxOrRadioGroup
			items={[
				{ value: ConditionType.NEW, label: 'Nový' },
				{ value: ConditionType.GOOD, label: '<PERSON><PERSON>r<PERSON>' },
				{ value: ConditionType.BAD, label: 'Špatný' },
				{ value: ConditionType.IN_PROGRESS, label: 'Rekonstruovaný' },
			]}
			inputType="radio"
			label="Stav nemovitosti"
			name="estimateData.condition"
			required={required}
		/>
	);
};
