import { CheckboxOrRadioGroup } from 'components/ui/core/CheckboxOrRadioGroup';
import { FC } from 'react';
import { TerraceType } from 'types/offer';

export const TerraceTypeSelector: FC = () => {
	return (
		<CheckboxOrRadioGroup
			items={[
				{ value: TerraceType.TERRACE, label: 'Terrace' },
				{ value: TerraceType.LOGGIA, label: 'Loggia' },
				{ value: TerraceType.BALCONY, label: 'Balcony' },
			]}
			inputType="radio"
			label="Typ terasy"
			name="estimateData.terraceType"
		/>
	);
};
