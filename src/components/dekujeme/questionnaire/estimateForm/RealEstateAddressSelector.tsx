import clsx from 'clsx';
import { FormError } from 'components/ui/core/FormError';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { FC } from 'react';
import { useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { checkFormError } from 'utils/checkFormError';

export const RealEstateAddressSelector: FC = () => {
	const {
		register,
		formState: { errors },
	} = useFormContext();
	const { formatMessage } = useIntl();
	const { commonRequiredFieldIntlString } = commonInltMessages;
	const commonRequiredMessage = formatMessage(commonRequiredFieldIntlString);
	const fieldLabel = 'estimateData.address';
	const hasError = checkFormError(fieldLabel, errors);

	return (
		<div className={clsx('inp-row', hasError && 'has-error')}>
			<span className="inp-row__top">
				<div className="tooltip tooltip--right">
					<span className="tooltip__handle">
						<label className="inp-label inp-label--required" htmlFor="firstname">
							Zadejte adresu nemovitosti
						</label>
					</span>
				</div>
			</span>
			<span className="inp-fix">
				<input type="text" id="address" className="inp-text" {...register(fieldLabel, { required: commonRequiredMessage })} />
			</span>
			<FormError name="estimateData.address" />
		</div>
	);
};
