import { Form } from 'components/ui/core/Form';
import { DispositionSelector } from 'components/ui/filter/DispositionSelector';
import { HouseTypeSelector } from 'components/ui/filter/HouseTypeSelector';
import { RealEstateTypeSelector } from 'components/ui/filter/RealEstateTypeSelector';
import { useSelectIcons } from 'hooks/useSelectIcons';
import { FC, useEffect, useMemo } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { AddNotification, SelectItem } from 'types/common';
import { RealEstateType } from 'types/filter';
import { RealEstateAreaSlider } from './RealEstateAreaSlider';
import { RealEstateAddressSelector } from './RealEstateAddressSelector';
import { RealEstateConditionSelector } from './RealEstateConditionSelector';
import { LandAreaSlider } from './LandAreaSlider';
import { TerraceTypeSelector } from './TerraceTypeSelector';
import { QuestionnaireUserDetails } from '../userDetailsForm/QuestionnaireUserDetails';
import { Questionnaire, QuestionnaireEstimateFormInputs, QuestionnaireUserData, UserDetailsHandling } from 'types/questionnaire';
import { LandTypeSelector } from 'components/ui/filter/LandTypeSelector';
import { useIntl } from 'react-intl';
import { QUESTIONNAIRE_NOTIFICATION_TIMEOUT } from 'utils/questionnaire';
import { fetchQuestionnairePatch } from 'api/questionnaire/fetchQuestionnairePatch';

type Props = AddNotification &
	UserDetailsHandling & {
		questionnaire: Questionnaire;
		email: string;
		moveToNextStep: () => void;
		userDetails: QuestionnaireUserData;
	};

export const EstimateForm: FC<Props> = ({
	email,
	addNotification,
	moveToNextStep,
	hasConfirmedUserDetails,
	confirmUserDetails,
	userDetails,
	questionnaire,
}) => {
	const selectIcons: SelectItem[] = useSelectIcons({ fullSet: true });
	const defaultValues = useMemo(() => {
		return {
			estimateData: {
				address: '',
				condition: null,
				realEstateType: RealEstateType.APARTMENT,
				disposition: null,
				houseType: null,
				landType: null,
				area: 0,
				landArea: 0,
				email,
				terraceType: null,
			},
		};
	}, [email]);

	const formContext = useForm<QuestionnaireEstimateFormInputs>({ defaultValues });
	const { watch, reset, setValue } = formContext;
	const { formatMessage } = useIntl();
	const watchRealEstateType = watch('estimateData.realEstateType');
	const isApartment = watchRealEstateType === RealEstateType.APARTMENT;
	const isHouse = watchRealEstateType === RealEstateType.HOUSE;
	const isLand = watchRealEstateType === RealEstateType.LAND;
	const realeEstateSelectorLabel = formatMessage({
		description: 'questionnaire-chooseType',
		defaultMessage: 'Zadejte typ své nemovitosti',
	});

	const onFail = () => addNotification({ message: 'Nastala chyba', timeout: QUESTIONNAIRE_NOTIFICATION_TIMEOUT });

	const onSubmit: SubmitHandler<QuestionnaireEstimateFormInputs> = async (data) => {
		try {
			const { estimateData, userData } = data;

			const res = await fetchQuestionnairePatch({ estimateData, userData }, questionnaire.id);

			if (res.statusCode === 200) {
				addNotification({ message: 'Váš zájem byl uložen', timeout: QUESTIONNAIRE_NOTIFICATION_TIMEOUT });
				confirmUserDetails();
				moveToNextStep();
			} else {
				onFail();
			}
		} catch (error) {
			onFail();
		}
	};

	useEffect(() => {
		reset({ ...defaultValues });
		setValue('estimateData.realEstateType', watchRealEstateType);
	}, [defaultValues, reset, setValue, watchRealEstateType]);

	return (
		<Form<QuestionnaireEstimateFormInputs> context={formContext} onSubmit={onSubmit}>
			<div className="u-mw-8-12 u-mx-auto u-mt-sm">
				<div className="u-mb-sm">
					<div className="u-text-left">
						<RealEstateAddressSelector />
						<RealEstateTypeSelector
							selectIcons={selectIcons}
							label={realeEstateSelectorLabel}
							name={'estimateData.realEstateType'}
						/>
						{!isLand ? <RealEstateConditionSelector required /> : null}
						{isApartment ? <DispositionSelector variant="single-choice" required name="estimateData.disposition" /> : null}
						{isHouse ? <HouseTypeSelector variant="single-choice" required name="estimateData.houseType" /> : null}
						{isLand ? <LandTypeSelector variant="single-choice" required name="estimateData.landType" /> : null}
						{isApartment || isHouse ? <RealEstateAreaSlider /> : null}
						{isLand || isHouse ? (
							<LandAreaSlider realEstateType={isHouse ? RealEstateType.HOUSE : RealEstateType.LAND} />
						) : null}
						{isApartment || isHouse ? <TerraceTypeSelector /> : null}
					</div>
					{!hasConfirmedUserDetails ? (
						<div className="u-mt-sm">
							<QuestionnaireUserDetails
								questionnaire={questionnaire}
								variant="inputs-only"
								confirmUserDetails={confirmUserDetails}
								defaultValues={userDetails}
							/>
						</div>
					) : null}
				</div>

				<p className="u-mt-xs u-mb-xs">
					<button type="submit" className="btn btn--lg">
						<span className="btn__text">Zaslat ocenění na e-mail</span>
					</button>
				</p>
			</div>
		</Form>
	);
};
