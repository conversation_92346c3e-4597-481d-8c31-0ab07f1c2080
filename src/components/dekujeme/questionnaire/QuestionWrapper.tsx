import { Icon } from 'components/ui/core/Icon';
import { FC } from 'react';
import { QuestionType } from 'types/questionnaire';

type Props = {
	question: QuestionType;
	showSubHeading: boolean;
	skipQuestion: () => void;
};

export const QuestionWrapper: FC<Props> = ({ children, question, showSubHeading, skipQuestion }) => {
	const { heading, subHeading } = question;
	return (
		<>
			{/* u-mb-xs */}
			<div className="u-mb-xs f-filter">
				<div className="f-filter__bg">
					<Icon name="rays" />
				</div>
				<div className="f-filter__inner f-filter__inner--extend u-text-center">
					<h2 className="h3">{heading}</h2>
					{!showSubHeading && subHeading != null ? <p>{subHeading}</p> : null}
					{children}
				</div>
			</div>

			<p className="u-mb-xl u-text-right">
				<a href="#" onClick={skipQuestion}>
					Přeskočit otázku
				</a>
			</p>
		</>
	);
};
