import { Estimate } from './Estimate';
import { FC, useEffect, useState } from 'react';
import { Insurance } from './Insurance';
import { Mortgage } from './Mortgage';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { Questionnaire, QuestionnaireFilter, QuestionnaireUserData } from 'types/questionnaire';

type Props = {
	userEmail: string;
	notifyQuestionnaireCompletion: () => void;
	userDetails: QuestionnaireUserData;
	isPartialQuestionnaire: boolean;
	stopQuestionnaire: () => void;
	questionnaire: Questionnaire;
} & QuestionnaireFilter;

export const AfterFilterQuestionnaire: FC<Props> = ({
	questionnaire,
	userEmail,
	filterType,
	notifyQuestionnaireCompletion,
	userDetails,
	isPartialQuestionnaire,
	stopQuestionnaire,
}) => {
	const [step, setStep] = useState(1);
	const [hasConfirmedUserDetails, setHasConfirmedUserDetails] = useState(false);
	const { addNotification } = useNotifications();

	const moveToNextStep = () => {
		setStep((currentStep) => currentStep + 1);
		window?.scrollTo(0, 0);
	};

	const confirmUserDetails = () => setHasConfirmedUserDetails(true);

	const commonProps = {
		questionnaire,
		moveToNextStep,
		userEmail,
		addNotification,
		hasConfirmedUserDetails,
		confirmUserDetails,
		userDetails,
	};

	const renderStep = () => {
		switch (step) {
			case 1:
				return (
					<Mortgage
						{...commonProps}
						filterType={filterType}
						isPartialQuestionnaire={isPartialQuestionnaire}
						stopQuestionnaire={stopQuestionnaire}
					/>
				);

			case 2:
				return <Estimate {...commonProps} />;

			case 3:
				return <Insurance {...commonProps} />;

			default:
				return null;
		}
	};

	useEffect(() => {
		if (step > 3) {
			notifyQuestionnaireCompletion();
		}
	}, [notifyQuestionnaireCompletion, step]);

	return renderStep();
};
