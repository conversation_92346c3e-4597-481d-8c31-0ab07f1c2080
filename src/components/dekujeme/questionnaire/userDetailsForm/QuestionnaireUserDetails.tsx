import { fetchQuestionnairePatch } from 'api/questionnaire/fetchQuestionnairePatch';
import clsx from 'clsx';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { TelephoneInput } from 'components/ui/core/TelephoneInput';
import { formValidationInltMessages } from 'i18n/commonIntlMessages/formValidation';
import { FC } from 'react';
import { SubmitHandler, useForm, useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { AddNotification } from 'types/common';
import { Questionnaire, QuestionnaireUserData, UserDetailsHandling } from 'types/questionnaire';
import { checkFormError } from 'utils/checkFormError';
import { QUESTIONNAIRE_NOTIFICATION_TIMEOUT } from 'utils/questionnaire';

type Props = Partial<AddNotification> &
	Pick<UserDetailsHandling, 'confirmUserDetails'> & {
		questionnaire: Questionnaire;
		moveToNextStep?: () => void;
		variant: 'full-form' | 'inputs-only';
		defaultValues: QuestionnaireUserData;
		isPartialQuestionnaire?: boolean;
		stopQuestionnaire?: () => void;
	};

export const QuestionnaireUserDetails: FC<Props> = ({
	questionnaire,
	variant,
	addNotification,
	moveToNextStep,
	confirmUserDetails,
	defaultValues,
	isPartialQuestionnaire = false,
	stopQuestionnaire,
}) => {
	const context = useForm<QuestionnaireUserData>({ defaultValues });

	const handleSubmit: SubmitHandler<QuestionnaireUserData> = (data) => {
		fetchQuestionnairePatch({ userData: data.userData }, questionnaire.id)
			.then((response) => {
				if (response.statusCode === 200) {
					addNotification?.({ message: 'Kontaktní údaje uloženy', timeout: QUESTIONNAIRE_NOTIFICATION_TIMEOUT });
					confirmUserDetails();

					if (isPartialQuestionnaire && stopQuestionnaire) {
						stopQuestionnaire();
					} else {
						moveToNextStep?.();
					}
				}
			})

			.catch((e) => {
				console.error(e);
				if (addNotification) {
					addNotification({ message: e.messase, timeout: QUESTIONNAIRE_NOTIFICATION_TIMEOUT });
				}
			});
	};

	const bareInputs = <BareUserDetailsInputs defaultValues={defaultValues} />;

	if (variant === 'inputs-only') return bareInputs;

	return (
		<Form<QuestionnaireUserData> context={context} onSubmit={handleSubmit}>
			<div className="u-mw-8-12 u-mx-auto">
				{bareInputs}
				<p className="u-mt-xs u-mb-xs">
					<button type="submit" className="btn btn--lg">
						<span className="btn__text">Získat nejvýhodnější úrok</span>
					</button>
				</p>
			</div>
		</Form>
	);
};

const BareUserDetailsInputs: FC<{ defaultValues: QuestionnaireUserData }> = ({ defaultValues }) => {
	const {
		register,
		formState: { errors },
	} = useFormContext();
	const { formatMessage } = useIntl();
	const { requiredNameIntlString, requiredSurnameIntlString } = formValidationInltMessages;

	const firstNameLabel = 'userData.firstName';
	const lastNameLabel = 'userData.lastName';
	const telephoneLabel = 'userData.telephone';

	const firstNameError = checkFormError(firstNameLabel, errors);
	const lastNameError = checkFormError(lastNameLabel, errors);
	const telephoneError = checkFormError(telephoneLabel, errors);

	return (
		<div className="grid grid--y-0">
			<div className="grid__cell size--6-12@md">
				<div className={clsx('inp-row', firstNameError && 'has-error')}>
					<span className="inp-row__top">
						<div className="tooltip tooltip--right">
							<span className="tooltip__handle">
								<label className="inp-label inp-label--required" htmlFor="firstname">
									Jméno
								</label>
							</span>
						</div>
					</span>
					<span className="inp-fix">
						<input
							type="text"
							id="name"
							className="inp-text"
							defaultValue={defaultValues.userData.firstName ?? ''}
							{...register(firstNameLabel, {
								required: formatMessage(requiredNameIntlString),
								value: defaultValues.userData.firstName,
							})}
						/>
						<FormError name="userData.firstName" />
					</span>
				</div>
			</div>
			<div className="grid__cell size--6-12@md">
				<div className={clsx('inp-row', lastNameError && 'has-error')}>
					<span className="inp-row__top">
						<div className="tooltip tooltip--right">
							<span className="tooltip__handle">
								<label className="inp-label inp-label--required" htmlFor="lastName">
									Příjmení
								</label>
							</span>
						</div>
					</span>
					<span className="inp-fix">
						<input
							type="text"
							id="lastName"
							className="inp-text"
							defaultValue={defaultValues.userData.lastName ?? ''}
							{...register(lastNameLabel, { required: formatMessage(requiredSurnameIntlString) })}
						/>
						<FormError name="userData.lastName" />
					</span>
				</div>
			</div>
			<div className="grid__cell size--6-12@md">
				<div className={clsx('inp-row', telephoneError && 'has-error')}>
					<span className="inp-row__top">
						<div className="tooltip tooltip--right">
							<span className="tooltip__handle">
								<label className="inp-label inp-label--required" htmlFor="telephone">
									Telefonní číslo
								</label>
							</span>
						</div>
					</span>
					<span className="inp-fix">
						<TelephoneInput name={telephoneLabel} defaultValue={defaultValues.userData.telephone ?? ''} required />
						<FormError name="userData.telephone" />
					</span>
				</div>
			</div>
			<div className="grid__cell size--6-12@md">
				<div className="inp-row">
					<span className="inp-row__top">
						<div className="tooltip tooltip--right">
							<span className="tooltip__handle">
								<label className="inp-label inp-label--required" htmlFor="email">
									Váš e‑mail
								</label>
							</span>
						</div>
					</span>
					<span className="inp-fix">
						<input
							type="email"
							inputMode="email"
							id="email"
							className="inp-text"
							placeholder="@"
							name="email"
							autoComplete="email"
							value={defaultValues.userData.email}
							disabled
						/>
					</span>
				</div>
			</div>
		</div>
	);
};
