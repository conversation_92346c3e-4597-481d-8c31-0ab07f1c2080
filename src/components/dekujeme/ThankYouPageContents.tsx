import { fetchFilterOffers } from 'api/filter/fetchFilterOffers';
import { PremiumDialogsProvider } from 'components/dialog/PremiumDialogsProvider';
import { PageSeo } from 'components/meta/PageSeo';
import { PremiumTariffsCompare } from 'components/premium/PremiumTariffsCompare';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Button } from 'components/ui/core/Button';
import { Row } from 'components/ui/core/Row';
import { AuthForm, useAuth } from 'components/user/AuthProvider';
import { useUser } from 'components/user/UserProvider';
import { FC, useEffect, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { ScraperData } from 'types/admin';
import { ApiResponse, Filter, FilterState } from 'types/filter';
import { Offer, OfferResponse } from 'types/offer';
import { Questionnaire } from 'types/questionnaire';
import { ActivatedFilterContents } from './ActivatedFilterContents';

type Props = {
	questionnaire: Questionnaire | null;
	data: Filter;
	questionnaireLoading: boolean;
	scrapers: ScraperData[];
	isSubscribed?: boolean;
};

export const ThankYouPageContents: FC<Props> = ({ data, scrapers, isSubscribed = false }) => {
	const { formatMessage } = useIntl();
	const { loggedIn } = useUser();
	const { openAuthForm } = useAuth();
	const openLogin = () => openAuthForm(AuthForm.LOGIN);

	// const { servicesToShow } = useServices(data.id);

	const [, setLastOffers] = useState<Offer[]>([]);

	useEffect(() => {
		fetchFilterOffers(data.id).then((result: ApiResponse<OfferResponse>) => {
			setLastOffers(result.data.realEstates);
		});
	}, [data, setLastOffers]);

	const filterActivatedIntlString = formatMessage({
		description: 'thankPage-filterActive',
		defaultMessage: 'Váš hlídací pes je téměr funkční',
	});

	const afterQuestionsHeading = (
		<FormattedMessage
			description={'thankPage-filterActive'}
			defaultMessage={'<strong>Váš hlídací pes</strong> je téměr funkční'}
			values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
		/>
	);

	const userHitLimit = data?.state === FilterState.DISABLED;
	const shouldBeMoreThanMini = data?.ownershipType?.length || data?.agent.length || data?.withTerraceOnly || data?.elevator;

	return (
		<>
			<PageSeo title={filterActivatedIntlString} />
			{!isSubscribed ? (
				<>
					<Row lg>
						<ArticleIntro title={afterQuestionsHeading} margin="sm">
							<p>
								Už jenom krok a nemusíte kontrolovat realitní servery.
								<br /> Na nové nemovitosti vás ihned upozorníme e&#8209;mailem.
							</p>
						</ArticleIntro>
					</Row>

					{data && (
						<>
							<Row lg>
								<section className="u-mb-sm b-pricing">
									<div className="u-mb-sm">
										<h2>
											Aktivaci dokončíte <strong className="u-color-secondary">výběrem tarifu</strong>
										</h2>
										<p className="u-font-lg">
											<strong>Prvních 7 dní máte zdarma</strong>. Platba probíhá přes zabezpečenou platební bránu
											Stripe.
											<br /> V případě, že preferujete platbu na základě faktury, kontaktujte nás na e-mailu{' '}
											<a href="mailto:<EMAIL>"><EMAIL></a>.
										</p>
									</div>
								</section>
							</Row>

							<PremiumDialogsProvider scrapers={scrapers}>
								<PremiumTariffsCompare count={scrapers.length} data={data} disableMini={!!shouldBeMoreThanMini} />
							</PremiumDialogsProvider>
						</>
					)}
					<CtaCommon />
				</>
			) : userHitLimit ? (
				<>
					<Row lg>
						<ArticleIntro title={afterQuestionsHeading} margin="sm">
							<p>
								Už jenom krok a nemusíte kontrolovat realitní servery.
								<br /> Na nové nemovitosti vás ihned upozorníme e&#8209;mailem.
							</p>
						</ArticleIntro>
					</Row>

					{loggedIn ? (
						<>
							<Row lg>
								<section className="u-mb-lg b-pricing">
									<div className="u-mb-sm">
										<h2>
											Aktivaci dokončíte <strong className="u-color-secondary">povýšením tarifu</strong>
										</h2>
										<p className="u-font-lg">Váš tarif neumožňuje mít více aktivních filtrů.</p>
									</div>
								</section>
							</Row>
							<PremiumDialogsProvider scrapers={scrapers}>
								<PremiumTariffsCompare count={scrapers.length} data={data} />
							</PremiumDialogsProvider>
						</>
					) : (
						<>
							<Row lg>
								<section className="u-mb-lg b-pricing">
									<div className="u-mb-sm">
										<h2>
											Aktivaci dokončíte <strong className="u-color-secondary">povýšením tarifu</strong>
										</h2>
										<p className="u-font-lg">
											Váš tarif neumožňuje mít více aktivních filtrů. <br />
											Přihlašte se a upravte si počet aktivních filtrů nebo povyšte váš tarif.
										</p>
										<p>
											<Button onClick={openLogin} size="lg" full className="u-mw-4-12">
												Přihlásit se
											</Button>
										</p>
									</div>
								</section>
							</Row>
						</>
					)}

					<CtaCommon />
				</>
			) : (
				<ActivatedFilterContents data={data} />
			)}
		</>
	);
};
