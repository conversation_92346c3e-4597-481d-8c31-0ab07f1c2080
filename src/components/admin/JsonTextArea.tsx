import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { PropsWithChildren, useEffect, useMemo } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { ManualUploadRequest } from 'types/admin';
import { fromatJsonInput } from 'utils/formatJsonInput';
import { isValidJson } from 'utils/isValidJson';

type AnySchema = Record<string, unknown>;

type Props<StartingData extends AnySchema | AnySchema[]> = {
	startingData: StartingData;
	onSubmit: SubmitHandler<ManualUploadRequest>;
	buttonLabel: string;
	hasSubmitted: boolean;
	onInputChange?: () => void;
};

export const JsonTextArea = <StartingData extends AnySchema | AnySchema[]>({
	startingData,
	onSubmit,
	onInputChange,
	buttonLabel,
	hasSubmitted,
	children,
}: PropsWithChildren<Props<StartingData>>) => {
	const stringifiedStartingData = useMemo(() => fromatJsonInput(startingData), [startingData]);

	const defaultValues: ManualUploadRequest = {
		stringifiedSchema: stringifiedStartingData,
	};

	const context = useForm<ManualUploadRequest>({ defaultValues });
	const {
		formState: { errors, isSubmitting },
		setValue,
		register,
	} = context;

	useEffect(() => {
		if (hasSubmitted) {
			setValue('stringifiedSchema', stringifiedStartingData);
		}
	}, [hasSubmitted, setValue, stringifiedStartingData]);

	return (
		<>
			<Form<ManualUploadRequest> context={context} onSubmit={onSubmit}>
				<div className={clsx('inp-row', errors.stringifiedSchema && 'has-error')}>
					<span className="inp-row__top">
						<Label id="stringifiedSchema">Enter the required details:</Label>
					</span>
					<span className="inp-fix">
						<textarea
							id="stringifiedSchema"
							className="inp-text"
							rows={40}
							{...register('stringifiedSchema', {
								required: 'Enter a JSON schema!',
								validate: {
									checkJson: (jsonString) => isValidJson(jsonString) || 'Invalid JSON!',
								},
							})}
							onChange={onInputChange}
						/>
					</span>
					<FormError name="stringifiedSchema" />
				</div>
				<div className="form__bottom-container">
					<p className="btn__wrapper">
						<Button
							type="submit"
							className={clsx('btn-loader', isSubmitting && 'is-loading')}
							disabled={isSubmitting}
							text={buttonLabel}
						/>
						{children}
					</p>
				</div>
			</Form>
		</>
	);
};
