import { fetchContactUpdate } from 'api/admin/fetchContactUpdate';
import { contactTypes } from 'components/admin/ContactList';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { Select } from 'components/ui/core/Select';
import { useUser } from 'components/user/UserProvider';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ContactPatchRequest, ContactType } from 'types/admin';

type Props = {
	contactId: string;
	defaultValue: ContactType;
};

export const ContactForm: FC<Props> = ({ contactId, defaultValue }) => {
	const { token } = useUser();
	const defaultValues = {
		contactType: defaultValue ?? ContactType.UNKNOWN,
	};
	const context = useForm<ContactPatchRequest>({ defaultValues });
	const handleSubmit: SubmitHandler<ContactPatchRequest> = async (values) => {
		await fetchContactUpdate(contactId, values, token);
	};

	return (
		<Form<ContactPatchRequest> context={context} onSubmit={handleSubmit}>
			<Select name="contactType" label="Změna typu" items={contactTypes} />
			<Button type="submit">Odeslat</Button>
		</Form>
	);
};
