import { fetchEmailTestCopy } from 'api/admin/fetchEmailTestCopy';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { EmailInput } from 'components/ui/core/EmailInput';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { EmailTestCopyRequest } from 'types/admin';
import { TEN_SECONDS } from 'utils/constants';

type Props = {
	id: string;
	onSubmit?: () => void;
};

export const EmailTestCopyForm: FC<Props> = ({ id, onSubmit }) => {
	const { token } = useUser();
	const { addNotification } = useNotifications();
	const defaultValues = {
		email: '',
	};
	const context = useForm<EmailTestCopyRequest>({ defaultValues });
	const {
		formState: { isSubmitting, errors },
	} = context;

	const handleSubmit: SubmitHandler<EmailTestCopyRequest> = async (data) => {
		const response = await fetchEmailTestCopy(id, data, token);
		if (response.statusCode === 200) {
			addNotification({ message: 'Testovací e-mail zařazen do fronty k odeslání', timeout: TEN_SECONDS });
			onSubmit && onSubmit();
		} else {
			addNotification({ message: 'Chybný požadavek, zkontrolujte parametry', timeout: TEN_SECONDS });
		}
	};

	return (
		<Form<EmailTestCopyRequest> context={context} onSubmit={handleSubmit}>
			<div className={clsx('inp-row', errors.email && 'has-error')}>
				<span className="inp-row__top">
					<Label id="email" required>
						E-mail
					</Label>
				</span>
				<span className="inp-fix">
					<EmailInput />
				</span>
				<FormError name="email" />
			</div>

			<div className="form__bottom-container">
				<p className="btn__wrapper">
					<Button
						type="submit"
						className={clsx('btn-loader', isSubmitting && 'is-loading')}
						disabled={isSubmitting}
						iconAfter="share"
						text="Odeslat"
					/>
				</p>
			</div>
		</Form>
	);
};

EmailTestCopyForm.displayName = 'EmailTestCopyForm';
