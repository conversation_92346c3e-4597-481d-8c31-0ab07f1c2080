import { Dialog } from 'components/ui/dialog/Dialog';
import { OfferDetailNonPublic } from 'components/ui/offer/OfferDetailNonPublic';
import { OfferNonPublicContactsBox } from 'components/ui/offer/OfferNonPublicContactsBox';
import { FC } from 'react';
import { Offer } from 'types/offer';

type Props = {
	previewIsOpen: boolean;
	closePreview: () => void;
	offerToPreview: Offer;
};

export const PreviewModal: FC<Props> = ({ previewIsOpen, closePreview, offerToPreview }) => {
	return (
		<Dialog isOpen={previewIsOpen} close={closePreview}>
			<OfferDetailNonPublic {...offerToPreview} />
			<OfferNonPublicContactsBox realEstate={offerToPreview} />
		</Dialog>
	);
};
