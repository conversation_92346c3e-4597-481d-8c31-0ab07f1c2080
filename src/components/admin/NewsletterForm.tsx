import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { Label } from 'components/ui/core/Label';
import { Tooltip } from 'components/ui/tooltip/Tooltip';
import { FC } from 'react';
import { DefaultValues, SubmitHandler, useForm } from 'react-hook-form';

type Props = {
	onSubmit: (campaign: string) => void;
};

type Inputs = {
	campaign: string;
};

const defaultValues: DefaultValues<Inputs> = {
	campaign: '',
};

export const NewsletterForm: FC<Props> = ({ onSubmit }) => {
	const context = useForm<Inputs>({ defaultValues });
	const {
		register,
		formState: { isSubmitting, errors },
	} = context;

	const handleSubmit: SubmitHandler<Inputs> = (data) => onSubmit(data.campaign);

	return (
		<Form<Inputs> context={context} onSubmit={handleSubmit}>
			<div className={clsx('inp-row', errors.campaign && 'has-error')}>
				<span className="inp-row__top">
					<Label id="campaign">Označení kampaně</Label>
					<Tooltip forInput>Vloží ke všem odkazům utm_campaign.</Tooltip>
				</span>
				<div className="inp-fix">
					<input id="campaign" className="inp-text" {...register('campaign', { required: true })} />
				</div>
			</div>
			<div>
				<Button
					type="submit"
					className={clsx('btn-loader', isSubmitting && 'is-loading')}
					disabled={isSubmitting}
					iconAfter="arrow-right"
					text="Rozeslat newsletter"
				/>
			</div>
		</Form>
	);
};

NewsletterForm.displayName = 'NewsletterForm';
