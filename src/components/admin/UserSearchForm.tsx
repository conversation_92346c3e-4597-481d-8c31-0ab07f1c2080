import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { Select } from 'components/ui/core/Select';
import { FC, useCallback, useEffect, useRef } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { AllOrNothing } from 'types/common';
import { TelephoneSource, UserRole, UserState } from 'types/user';

type Inputs = {
	email: string;
	telephoneSources?: string;
	roles?: string;
	roleExpiration?: string;
	userState?: string;
};

type Props = {
	onSubmit: (query: string, telephoneSources?: string, roles?: string, roleExpiration?: string, userState?: string) => void;
};

export const UserSearchForm: FC<Props> = ({ onSubmit }) => {
	const firstLoadRef = useRef(true);

	const defaultValues = {
		email: '',
		telephoneSources: AllOrNothing.NOTHING,
		roles: AllOrNothing.NOTHING,
		userState: AllOrNothing.NOTHING,
	};
	const context = useForm<Inputs>({ defaultValues });

	const {
		register,
		formState: { isSubmitting, errors },
		watch,
		getValues,
	} = context;

	const telephoneSources = watch('telephoneSources');
	const roles = watch('roles');
	const roleExpiration = watch('roleExpiration');
	const userState = watch('userState');

	const handleSubmit: SubmitHandler<Inputs> = useCallback(
		(data) => {
			onSubmit(data.email.toLowerCase(), data.telephoneSources, data.roles, data.roleExpiration, data.userState);
		},
		[onSubmit],
	);

	useEffect(() => {
		// Avoid refetch on first load
		if (firstLoadRef.current) {
			firstLoadRef.current = false;
			return;
		}

		handleSubmit({ email: getValues('email'), telephoneSources, roles, roleExpiration });
	}, [handleSubmit, getValues, telephoneSources, onSubmit, roles, roleExpiration, userState]);

	return (
		<Form<Inputs> context={context} onSubmit={handleSubmit}>
			<div className="grid">
				<div className="grid__cell size--6-12">
					<div className={clsx('inp-row', errors.email && 'has-error')}>
						<span className="inp-row__top">
							<Label id="email">E-mail</Label>
						</span>
						<span className="inp-fix">
							<input type="text" id="email" className="inp-text" {...register('email')} />
						</span>
						<FormError name="email" />
					</div>
				</div>

				<div className="grid__cell size--6-12">
					<div className="form__bottom-container">
						<span className="inp-row__top">
							<Label>&nbsp;</Label>
						</span>
						<p className="btn__wrapper">
							<Button
								type="submit"
								className={clsx('btn-loader', isSubmitting && 'is-loading')}
								disabled={isSubmitting}
								iconAfter="arrow-right"
								text="Hledat"
							/>
						</p>
					</div>
				</div>
			</div>

			<div className="inp-row">
				<Select
					name={'telephoneSources'}
					label={'Filter by telephone'}
					items={[
						{ label: "Don't filter by phone", value: AllOrNothing.NOTHING },
						{ label: 'All users with telephone number', value: AllOrNothing.ALL },
						{ label: 'Saved on CTA Box', value: TelephoneSource.CTA_BOX },
						{ label: 'Saved on registration', value: TelephoneSource.REGISTRATION },
						{ label: 'Saved on email landing page', value: TelephoneSource.LANDING_PAGE },
					]}
					allowFilterMode
				/>
				<Select
					name={'roles'}
					label={'Uživatelská role'}
					items={[
						{ label: 'Ignorovat', value: `${AllOrNothing.NOTHING}` },
						{ label: 'Žádná', value: `${UserRole.NONE}` },
						{ label: 'Registrovaný', value: `${UserRole.USER}` },
						{ label: 'Mini', value: `${UserRole.MINI}` },
						{ label: 'Standard', value: `${UserRole.STANDARD}` },
						{ label: 'Premium', value: `${UserRole.PREMIUM}` },
						{ label: 'Profi', value: `${UserRole.PROFI}` },
						{ label: 'Enterprise', value: `${UserRole.ENTERPRISE}` },
						{ label: 'Pokročilý', value: `${UserRole.ADVANCED}` },
						{ label: 'Administrátor', value: `${UserRole.ADMIN}` },
					]}
					allowFilterMode
				/>
				<Select
					name={'roleExpiration'}
					label={'Platnost role'}
					items={[
						{ label: 'Ignorovat', value: AllOrNothing.NOTHING },
						{ label: 'Časově omezené', value: 'LIMITED' },
						{ label: 'Neomezené', value: 'UNLIMITED' },
					]}
					allowFilterMode
				/>
				<Select
					name={'userState'}
					label={'Stav uživatele'}
					items={[
						{ label: 'Ignorovat', value: AllOrNothing.NOTHING },
						{ label: 'Aktivní', value: UserState.ACTIVE },
						{ label: 'Zablokovaný', value: UserState.BLOCKED },
						{ label: 'Vytvořený', value: UserState.CREATED },
						{ label: 'Smazaný', value: UserState.DELETED },
						{ label: 'Nový', value: UserState.NEW },
					]}
					allowFilterMode
				/>
			</div>
		</Form>
	);
};

UserSearchForm.displayName = 'UserSearchForm';
