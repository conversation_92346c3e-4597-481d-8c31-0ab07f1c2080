import { fetchUsers } from 'api/admin/fetchUsers';
import { UserSearchForm } from 'components/admin/UserSearchForm';
import { Link } from 'components/Link';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Icon } from 'components/ui/core/Icon';
import { Message } from 'components/ui/core/Message';
import { NextPrev } from 'components/ui/core/NextPrev';
import { useUser } from 'components/user/UserProvider';
import { useNextPrev } from 'hooks/useNextPrev';
import { FC, useCallback, useEffect, useState } from 'react';
import { UsersResponse } from 'types/admin';
import { UserRole } from 'types/user';
import { formatDate } from 'utils/formats';

export const UsersList: FC = () => {
	const { token } = useUser();
	const [query, setQuery] = useState('');
	const [data, setData] = useState<UsersResponse>({ users: [], count: { inactive: 0, active: 0, blocked: 0, total: 0 } });
	const [loading, setLoading] = useState(false);
	const [telephoneSources, setTelephoneSources] = useState('');
	const [roles, setRoles] = useState('');
	const [roleExpiration, setRoleExpiration] = useState('');
	const [userState, setUserState] = useState('');

	const [page, onPageChange] = useNextPrev();

	const fetchData = useCallback(async () => {
		if (!token) return;

		try {
			const response = await fetchUsers(token, query, page, telephoneSources, roles, roleExpiration, userState);
			setData(response.data);
		} catch (e) {
			console.error(e);
		}
		setLoading(false);
	}, [token, query, page, telephoneSources, roles, roleExpiration, userState]);

	const onEmailInputSubmit = useCallback(
		(query: string, telephoneSources?: string, roles?: string, roleExpiration?: string, userState?: string) => {
			setQuery(query);
			if (telephoneSources) {
				setTelephoneSources(telephoneSources);
			}
			if (roles) {
				setRoles(roles);
			}

			if (roleExpiration) {
				setRoleExpiration(roleExpiration);
			}

			if (userState) {
				setUserState(userState);
			}
		},
		[],
	);

	useEffect(() => {
		setLoading(true);
		fetchData();
	}, [page, fetchData]);

	return (
		<BlockLoader loading={loading}>
			<div className="grid grid--bottom">
				<div className="grid__cell size--6-12@md">
					<UserSearchForm onSubmit={onEmailInputSubmit} />
				</div>

				<div className="grid__cell size--6-12@md">
					<p className="u-text-right">
						<abbr className="u-color-success" title="Aktivních">
							{data.count.active}
						</abbr>{' '}
						|{' '}
						<abbr className="u-color-grey" title="Neaktivních">
							{data.count.inactive}
						</abbr>{' '}
						|{' '}
						<abbr className="u-color-error" title="Zablokovaných">
							{data.count.blocked}
						</abbr>{' '}
						|{' '}
						<abbr className="u-font-bold" title="Celkem">
							{data.count.total}
						</abbr>{' '}
						uživatelů
					</p>
				</div>
			</div>

			{data.users.length ? (
				<>
					{data.users.map((user) => (
						<div className="b-product c-products__item" key={user.id}>
							<div className="b-product__header">
								<div className="b-annot-product">
									<div className="b-annot-product__content">
										<p className="b-annot-product__caption">Uživatel</p>
										<h2 className="b-annot-product__title">
											<Link href="/administrace/uzivatel/[id]" as={`/administrace/uzivatel/${user.id}`}>
												{user.name && user.surname ? `${user.name} ${user.surname}` : user.id}
											</Link>
										</h2>
									</div>
								</div>
							</div>
							<div className="b-product__content">
								<div className="b-product__content-left">
									<div className="b-product__description">
										<div className="b-description">
											<ul className="b-description__list">
												<li className="b-description__item">
													<span className="u-font-medium">E-mail:</span>{' '}
													<a href={`mailto:${user.email}`}>{user.email}</a>
												</li>
												{user.telephone && (
													<li className="b-description__item">
														<span className="u-font-medium">Telefon:</span>{' '}
														<a href={`tel:${user.telephone.replace(/\s/g, '')}`}>{user.telephone}</a>
													</li>
												)}
												{user.adminInfo?.telephoneSource && (
													<li className="b-description__item">
														<span className="u-font-medium">Telephone source:</span>{' '}
														{user.adminInfo?.telephoneSource}
													</li>
												)}
												{user.adminInfo?.createdAt && (
													<li className="b-description__item">
														<span className="u-font-medium">Vytvořen:</span>{' '}
														{formatDate(new Date(user.adminInfo?.createdAt))}
													</li>
												)}
											</ul>
										</div>
									</div>
								</div>
							</div>
							<div className="b-product__bottom">
								<div className="b-product__bottom-left">
									<span className="flag flag--purple">
										<span className="flag__inner">
											<span className="flag__icon">
												<Icon name="tip" />
											</span>
											<span className="flag__text">{user.state}</span>
										</span>
									</span>
									<span className="flag flag--green">
										<span className="flag__inner">
											<span className="flag__icon">
												<Icon name="tip" />
											</span>
											<span className="flag__text">
												{UserRole[user.role]}
												{user.roleExpiresAt && <> do {new Date(user.roleExpiresAt).toLocaleString()}</>}
											</span>
										</span>
									</span>
								</div>
							</div>
						</div>
					))}
				</>
			) : (
				<Message icon="notification">Žádné záznamy</Message>
			)}
			<NextPrev page={page} onChange={onPageChange} showNext={data.users.length >= 10} />
		</BlockLoader>
	);
};

UsersList.displayName = 'UsersList';
