import { fetchContactDetail } from 'api/admin/fetchContactDetail';
import { fetchContactRealEstates } from 'api/admin/fetchContactRealEstates';
import { ContactForm } from 'components/admin/ContactForm';
import { contactTypes } from 'components/admin/ContactList';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Message } from 'components/ui/core/Message';
import { NextPrev } from 'components/ui/core/NextPrev';
import { Title } from 'components/ui/core/Title';
import { OfferBox } from 'components/ui/offer/OfferBox';
import { useUser } from 'components/user/UserProvider';
import { useNextPrev } from 'hooks/useNextPrev';
import { FC, useCallback, useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { ContactPayload } from 'types/admin';
import { Offer } from 'types/offer';
import { formatDate } from 'utils/formats';

type Props = {
	id: string;
};

export const ContactDetail: FC<Props> = ({ id }) => {
	const { token } = useUser();
	const [contactLoading, setContactLoading] = useState(false);
	const [contact, setContact] = useState<ContactPayload>();
	const [realEstatesLoading, setRealEstatesLoading] = useState(false);
	const [realEstates, setRealEstates] = useState<Offer[]>([]);
	const [page, onPageChange] = useNextPrev();

	const fetchContact = useCallback(async () => {
		try {
			const response = await fetchContactDetail(token, id);
			setContact(response.data.contact);
		} catch (e) {
			console.error(e);
		}
		setContactLoading(false);
	}, [id, token]);

	const fetchRealEstates = useCallback(async () => {
		try {
			const response = await fetchContactRealEstates(token, id);
			setRealEstates(response.data.realEstates);
		} catch (e) {
			console.error(e);
		}
		setRealEstatesLoading(false);
	}, [id, token]);

	useEffect(() => {
		setContactLoading(true);
		fetchContact();
	}, [fetchContact]);

	useEffect(() => {
		setRealEstatesLoading(true);
		fetchRealEstates();
	}, [fetchRealEstates]);

	return (
		<>
			<BlockLoader loading={contactLoading}>
				{contact && (
					<div className="b-product c-products__item b-product--online">
						<div className="b-product__header">
							<div className="b-annot-product">
								<div className="b-annot-product__content">
									<p className="b-annot-product__caption">Kontakt</p>
									<h2 className="b-annot-product__title">
										{[contact.agencyName, contact.name].filter(Boolean).join(' | ')}
									</h2>
								</div>
							</div>
						</div>
						<div className="b-product__content">
							<div className="b-product__content-left">
								<div className="b-product__description">
									<div className="b-description">
										<ul className="b-description__list">
											<li className="b-description__item">
												<span className="u-font-medium">Typ:</span>{' '}
												{contactTypes.find(({ value }) => value === contact.contactType)?.label ??
													contact.contactType}
											</li>
											<li className="b-description__item">
												<span className="u-font-medium">E-mail:</span>{' '}
												<a href={`mailto:${contact.email}`}>{contact.email}</a>
											</li>
											<li className="b-description__item">
												<span className="u-font-medium">Telefon:</span>{' '}
												<a href={`mailto:${contact.telephone}`}>{contact.telephone}</a>
											</li>
										</ul>
									</div>
								</div>
							</div>
							<div className="b-product__content-right u-text-right">
								<ContactForm contactId={contact.id} defaultValue={contact.contactType} />
							</div>
						</div>
						<div className="b-product__bottom">
							<div className="b-product__bottom-left">
								<div className="b-dates">
									<ul className="b-dates__list">
										<li className="b-dates__item">
											<p>
												Datum vytvoření:{' '}
												<span className="u-font-medium">{formatDate(new Date(contact.createdAt))}</span>
											</p>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				)}
			</BlockLoader>

			<BlockLoader loading={realEstatesLoading}>
				{realEstates.length > 0 ? (
					<section className="c-crossroad u-mb-md">
						<Title tagName="h2" margin="md" align="center">
							<FormattedMessage description={'thankPage-lastOffers'} defaultMessage={'Poslední nalezené nabídky'} />
						</Title>
						<div className="c-crossroad__list grid">
							{realEstates.map((realEstate) => (
								<div
									className="c-crossroad__item grid__cell grid__cell--eq size--6-12@sm size--4-12@md"
									key={realEstate.id}
								>
									<OfferBox offer={realEstate} />
								</div>
							))}
						</div>
						<NextPrev page={page} onChange={onPageChange} showNext={realEstates.length >= 21} />
					</section>
				) : (
					<Message icon="notification">Žádné záznamy</Message>
				)}
			</BlockLoader>
		</>
	);
};

ContactDetail.displayName = 'ContactDetail';
