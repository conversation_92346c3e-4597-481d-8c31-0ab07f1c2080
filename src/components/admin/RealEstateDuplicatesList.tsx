import { fetchDuplicates } from 'api/admin/fetchDuplicates';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Message } from 'components/ui/core/Message';
import { NextPrev } from 'components/ui/core/NextPrev';
import { OfferBox } from 'components/ui/offer/OfferBox';
import { useUser } from 'components/user/UserProvider';
import { useNextPrev } from 'hooks/useNextPrev';
import { FC, Fragment, useCallback, useEffect, useState } from 'react';
import { Offer } from 'types/offer';

export const RealEstateDuplicatesList: FC = () => {
	const { token } = useUser();
	const [items, setItems] = useState<Offer[][]>([]);
	const [loading, setLoading] = useState(false);
	const [page, onPageChange] = useNextPrev();

	const fetchData = useCallback(async () => {
		if (!token) return;

		try {
			const response = await fetchDuplicates(token, page);
			setItems(response.data.realEstates);
		} catch (e) {
			console.error(e);
		}
		setLoading(false);
	}, [page, token]);

	useEffect(() => {
		setLoading(true);
		fetchData();
	}, [page, fetchData]);

	return (
		<BlockLoader loading={loading}>
			{items.length ? (
				items.map((item, index) => (
					<Fragment
						key={item
							.map(({ id }) => id)
							.sort((a, b) => a.localeCompare(b))
							.join('')}
					>
						{index > 0 && <hr className="u-mb-md u-mt-md" />}
						<div className="grid grid--scroll">
							{item.map((offer) => (
								<div key={offer.id} className="c-crossroad__item grid__cell grid__cell--eq size--6-12@sm size--4-12@md">
									<OfferBox offer={offer} />
								</div>
							))}
						</div>
					</Fragment>
				))
			) : (
				<Message icon="notification">Žádné záznamy</Message>
			)}
			<div className="u-mt-md">
				<NextPrev page={page} onChange={onPageChange} showNext={items.length >= 1} />
			</div>
		</BlockLoader>
	);
};

RealEstateDuplicatesList.displayName = 'RealEstateDuplicatesList';
