import { fetchAdminUserDelete } from 'api/admin/fetchAdminUserDelete';
import { fetchUser } from 'api/admin/fetchUser';
import { fetchUserFilters } from 'api/admin/fetchUserFilters';
import { UserEditForm } from 'components/admin/UserEditForm';
import { UserNotificationForm } from 'components/admin/UserNotificationForm';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Button } from 'components/ui/core/Button';
import { Icon } from 'components/ui/core/Icon';
import { ConfirmDialog } from 'components/ui/dialog/ConfirmDialog';
import { Dialog } from 'components/ui/dialog/Dialog';
import { FilterBox } from 'components/ui/filter/FilterBox';
import { FilterFormDialog } from 'components/ui/filter/FilterFormDialog';
import { useUser } from 'components/user/UserProvider';
import { useRouter } from 'next/router';
import { FC, useCallback, useEffect, useState } from 'react';
import { EmailStatsPayload, UserPayload } from 'types/admin';
import { Filter } from 'types/filter';
import { UserRole } from 'types/user';
import { AdminNoteTextArea } from './AdminNoteTextArea';
import { formatDate } from 'utils/formats';

type Props = {
	id: string;
};

enum SelectDialog {
	NONE,
	EDIT,
	DELETE,
	NOTIFICATION,
	FILTER_EDIT,
}

export const UserDetail: FC<Props> = ({ id }) => {
	const router = useRouter();
	const { token } = useUser();
	const [loading, setLoading] = useState(false);
	const [user, setUser] = useState<UserPayload | undefined>();
	const [emailStats, setEmailStats] = useState<EmailStatsPayload | undefined>();
	const [filters, setFilters] = useState<Filter[]>([]);
	const [editedFilter, setEditedFilter] = useState<Filter>();
	const [dialogOpen, setDialogOpen] = useState<SelectDialog>(SelectDialog.NONE);
	const openDialog = (dialog: SelectDialog) => () => setDialogOpen(dialog);
	const closeDialog = () => setDialogOpen(SelectDialog.NONE);

	const fetchData = useCallback(async () => {
		try {
			const [userResponse, filterResponse] = await Promise.all([fetchUser(token, id), fetchUserFilters(token, id)]);
			setUser(userResponse.data.user);
			setEmailStats(userResponse.data.emails);
			setFilters(filterResponse.data.filters);
		} catch (e) {
			console.error(e);
		}
		setLoading(false);
	}, [token, id]);

	const updateFilter = (filter: Filter) => {
		setEditedFilter(filter);
		openDialog(SelectDialog.FILTER_EDIT)();
	};

	const onFilterUpdate = (filter: Filter) => {
		setFilters((filters) => filters.map((item) => (item.id === filter.id ? filter : item)));
	};

	const handleDelete = (id: string) => async () => {
		await fetchAdminUserDelete(token, id);
		await router.push('/administrace');
	};

	const onEdit = (user: UserPayload) => {
		setUser(user);
		closeDialog();
	};

	useEffect(() => {
		setLoading(true);
		fetchData();
	}, [fetchData, id]);

	return (
		<BlockLoader loading={loading}>
			{user && (
				<>
					<div className="b-product">
						<div className="b-product__header">
							<div className="b-annot-product">
								<div className="b-annot-product__content">
									<p className="b-annot-product__caption">Uživatel</p>
									<h2 className="b-annot-product__title">
										{user.name} {user.surname}
									</h2>
								</div>
							</div>
						</div>
						<div className="b-product__content">
							<div className="b-product__content-left">
								<div className="b-product__description">
									<div className="b-description">
										<ul className="b-description__list">
											<li className="b-description__item">
												<span className="u-font-medium">E-mail:</span>{' '}
												<a href={`mailto:${user.email}`}>{user.email}</a>
											</li>
											{user.telephone && (
												<li className="b-description__item">
													<span className="u-font-medium">Telefon:</span>{' '}
													<a href={`tel:${user.telephone.replace(/\s/g, '')}`}>{user.telephone}</a>
												</li>
											)}
											{user.adminInfo?.createdAt && (
												<li className="b-description__item">
													<span className="u-font-medium">Vytvořen:</span>{' '}
													{formatDate(new Date(user.adminInfo?.createdAt))}
												</li>
											)}
										</ul>
									</div>
								</div>
								<AdminNoteTextArea userPayload={user} />
							</div>
							{emailStats && (
								<div className="b-product__content-right">
									<div className="b-product__description">
										<div className="b-description">
											<p>Statistiky e-mailů za posledních 7 dní</p>
											<table>
												<tbody>
													<tr>
														<td>Odeslané</td>
														<td>{emailStats.sent}</td>
													</tr>
													<tr>
														<td>Doručené</td>
														<td>{emailStats.delivered}</td>
													</tr>
													<tr>
														<td>Odmítnuté</td>
														<td>{emailStats.rejected}</td>
													</tr>
													<tr>
														<td>Otevřené</td>
														<td>{emailStats.opened}</td>
													</tr>
													<tr>
														<td>Interakce</td>
														<td>{emailStats.interacted}</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							)}
						</div>
						<div className="b-product__bottom">
							<div className="b-product__bottom-left">
								<span className="flag flag--purple">
									<span className="flag__inner">
										<span className="flag__icon">
											<Icon name="tip" />
										</span>
										<span className="flag__text">{user.state}</span>
									</span>
								</span>
								<span className="flag flag--green">
									<span className="flag__inner">
										<span className="flag__icon">
											<Icon name="tip" />
										</span>
										<span className="flag__text">{UserRole[user.role]}</span>
									</span>
								</span>
							</div>
							<div className="b-product__bottom-right">
								<Button variant="outline" onClick={openDialog(SelectDialog.NOTIFICATION)}>
									Poslat notifikaci
								</Button>
								<Button onClick={openDialog(SelectDialog.EDIT)}>Upravit</Button>
								<Button variant="secondary" onClick={openDialog(SelectDialog.DELETE)}>
									Smazat uživatele
								</Button>
							</div>
						</div>
					</div>

					<hr className="u-mb-md u-mt-md" />

					{filters.map((filter) => (
						<div className="c-products__item" key={filter.id}>
							<FilterBox {...filter} onAdminEdit={updateFilter} onAdminEditFinish={onFilterUpdate} />
						</div>
					))}

					<ConfirmDialog
						question="Chcete opravdu smazat tohoto uživatele? Akce je nevratná!"
						onConfirm={handleDelete(user.id)}
						onCancel={closeDialog}
						isOpen={dialogOpen === SelectDialog.DELETE}
						close={closeDialog}
					/>
					<Dialog isOpen={dialogOpen === SelectDialog.EDIT} close={closeDialog}>
						<UserEditForm user={user} onSubmit={onEdit} />
					</Dialog>
					<Dialog isOpen={dialogOpen === SelectDialog.NOTIFICATION} close={closeDialog}>
						<UserNotificationForm id={user.id} onSubmit={closeDialog} />
					</Dialog>
					<FilterFormDialog filterValues={editedFilter} close={closeDialog} isOpen={dialogOpen === SelectDialog.FILTER_EDIT} />
				</>
			)}
		</BlockLoader>
	);
};

UserDetail.displayName = 'UserDetail';
