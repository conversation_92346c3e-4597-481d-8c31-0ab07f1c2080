import { fetchContacts } from 'api/admin/fetchContacts';
import { ContactFilterForm } from 'components/admin/ContactFilterForm';
import { ContactForm } from 'components/admin/ContactForm';
import { Link } from 'components/Link';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Message } from 'components/ui/core/Message';
import { NextPrev } from 'components/ui/core/NextPrev';
import { useUser } from 'components/user/UserProvider';
import { useNextPrev } from 'hooks/useNextPrev';
import { FC, useCallback, useEffect, useState } from 'react';
import { ContactsResponse, ContactType } from 'types/admin';
import { formatDate } from 'utils/formats';

export const contactTypes = [
	{ label: 'Real. kancelář', value: ContactType.AGENT },
	{ label: 'Developer', value: ContactType.DEVELOPER },
	{ label: 'K prověření', value: ContactType.IN_REVIEW },
	{ label: 'Obecn<PERSON>', value: ContactType.MUNICIPALITY },
	{ label: 'Soukromý', value: ContactType.PRIVATE },
	{ label: 'Neznámý', value: ContactType.UNKNOWN },
];

export const ContactList: FC = () => {
	const { token } = useUser();
	const [data, setData] = useState<ContactsResponse>({ contacts: [] });
	const [loading, setLoading] = useState(false);
	const [page, onPageChange] = useNextPrev();
	const [contactType, setContactType] = useState<ContactType>();
	const [query, setQuery] = useState('');

	const fetchData = useCallback(async () => {
		setLoading(true);
		if (!token) return;

		try {
			const response = await fetchContacts(token, contactType, query, page);
			setData(response.data);
		} catch (e) {
			console.error(e);
		}
		setLoading(false);
	}, [contactType, page, query, token]);

	useEffect(() => {
		setLoading(true);
		fetchData();
	}, [fetchData]);

	const handleFilterSubmit = (query: string, contactType: ContactType | '') => {
		setQuery(query);
		if (contactType) setContactType(contactType);
	};

	return (
		<>
			<ContactFilterForm onSubmit={handleFilterSubmit} />
			<BlockLoader loading={loading}>
				{data.contacts.length > 0 ? (
					data.contacts.map((contact) => (
						<div className="b-product c-products__item b-product--online" key={contact.id}>
							<div className="b-product__header">
								<div className="b-annot-product">
									<div className="b-annot-product__content">
										<p className="b-annot-product__caption">Kontakt</p>
										<h2 className="b-annot-product__title">
											<Link href="/administrace/kontakt/[id]" as={`/administrace/kontakt/${contact.id}`}>
												{[contact.agencyName, contact.name].filter(Boolean).join(' | ')}
											</Link>
										</h2>
									</div>
								</div>
							</div>
							<div className="b-product__content">
								<div className="b-product__content-left">
									<div className="b-product__description">
										<div className="b-description">
											<ul className="b-description__list">
												<li className="b-description__item">
													<span className="u-font-medium">Typ:</span>{' '}
													{contactTypes.find(({ value }) => value === contact.contactType)?.label ??
														contact.contactType}
												</li>
												<li className="b-description__item">
													<span className="u-font-medium">E-mail:</span>{' '}
													<a href={`mailto:${contact.email}`}>{contact.email}</a>
												</li>
												<li className="b-description__item">
													<span className="u-font-medium">Telefon:</span>{' '}
													<a href={`mailto:${contact.telephone}`}>{contact.telephone}</a>
												</li>
											</ul>
										</div>
									</div>
								</div>
								<div className="b-product__content-right u-text-right">
									<ContactForm contactId={contact.id} defaultValue={contact.contactType} />
								</div>
							</div>
							<div className="b-product__bottom">
								<div className="b-product__bottom-left">
									<div className="b-dates">
										<ul className="b-dates__list">
											<li className="b-dates__item">
												<p>
													Datum vytvoření:{' '}
													<span className="u-font-medium">{formatDate(new Date(contact.createdAt))}</span>
												</p>
											</li>
										</ul>
									</div>
								</div>
							</div>
						</div>
					))
				) : (
					<Message icon="notification">Žádné záznamy</Message>
				)}
				<NextPrev page={page} onChange={onPageChange} showNext={data.contacts.length >= 10} />
			</BlockLoader>
		</>
	);
};

ContactList.displayName = 'ContactList';
