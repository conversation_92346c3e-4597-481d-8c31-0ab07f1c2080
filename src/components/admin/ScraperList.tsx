import { fetchScraperDelete } from 'api/admin/fetchScraperDelete';
import { fetchScrapers } from 'api/admin/fetchScrapers';
import { fetchScraperUpdate } from 'api/admin/fetchScraperUpdate';
import clsx from 'clsx';
import { HydrateForm } from 'components/admin/HydrateForm';
import { ScraperForm } from 'components/admin/ScraperForm';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Button } from 'components/ui/core/Button';
import { Flag } from 'components/ui/core/Flag';
import { Icon } from 'components/ui/core/Icon';
import { IconButton } from 'components/ui/core/IconButton';
import { Message } from 'components/ui/core/Message';
import { NextPrev } from 'components/ui/core/NextPrev';
import { Dialog } from 'components/ui/dialog/Dialog';
import { useUser } from 'components/user/UserProvider';
import { useNextPrev } from 'hooks/useNextPrev';
import { useShortLocale } from 'i18n/useShortLocale';
import { FC, useCallback, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { ScraperPayload, ScrapersResponse, ScraperState } from 'types/admin';
import { RealEstateType } from 'types/filter';
import { formatDate, formatNumber } from 'utils/formats';
import { getFiltersLocalizedStrings } from 'utils/getFiltersLocalizedStrings';

const icons = {
	[RealEstateType.APARTMENT]: 'sofa',
	[RealEstateType.HOUSE]: 'house-1',
	[RealEstateType.LAND]: 'signpost',
	[RealEstateType.COMMERCIAL]: 'building',
	[RealEstateType.OTHER]: 'garage',
};

export const ScraperList: FC = () => {
	const { token } = useUser();
	const shortLocale = useShortLocale();
	const intl = useIntl();
	const [data, setData] = useState<ScrapersResponse>({ scrapers: [], origins: [] });
	const [selectedItem, setSelectedItem] = useState<ScraperPayload>();
	const [selectedOrigin, setSelectedOrigin] = useState('');
	const [isEditDialogOpen, setEditDialogOpen] = useState(false);
	const [isHydrateDialogOpen, setHydrateDialogOpen] = useState(false);
	const [loading, setLoading] = useState(false);
	const { generateLabel } = getFiltersLocalizedStrings(intl, shortLocale);

	const [page, onPageChange] = useNextPrev();

	const fetchData = useCallback(async () => {
		if (!token) return;

		try {
			const response = await fetchScrapers(token, page, selectedOrigin);
			setData(response.data);
		} catch (e) {
			console.error(e);
		}
		setLoading(false);
	}, [page, selectedOrigin, token]);

	const handleEdit = (id?: string) => () => {
		if (id) {
			setSelectedItem(data.scrapers.find((item) => item.id === id));
		} else {
			setSelectedItem(undefined);
		}
		setEditDialogOpen(true);
	};

	const handleHydrate = (id?: string) => () => {
		if (id) {
			setSelectedItem(data.scrapers.find((item) => item.id === id));
		} else {
			setSelectedItem(undefined);
		}
		setHydrateDialogOpen(true);
	};

	const handleSwitch = (scraper: ScraperPayload) => async () => {
		const response = await fetchScraperUpdate(
			scraper.id,
			{ ...scraper, state: scraper.state === ScraperState.ENABLED ? ScraperState.DISABLED : ScraperState.ENABLED },
			token,
		);
		const newScraper = response.data.scraper;
		setData({
			...data,
			scrapers: [newScraper, ...data.scrapers.filter((item) => item.id !== scraper.id)].sort(
				(a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
			),
		});
	};

	const handleDelete = (id: string) => async () => {
		await fetchScraperDelete(id, token);
		setData({ ...data, scrapers: data.scrapers.filter((item) => item.id === id) });
	};

	const handleCreate = (scraper: ScraperPayload) => {
		const found = data.scrapers.findIndex((item) => item.id === scraper.id);
		if (~found) {
			setData({ ...data, scrapers: [...data.scrapers.slice(0, found), scraper, ...data.scrapers.slice(found + 1)] });
		} else {
			setData({ ...data, scrapers: [scraper, ...data.scrapers] });
		}
		setEditDialogOpen(false);
	};

	const handleSelectOrigin = (origin: string) => () => {
		setSelectedOrigin(origin);
		onPageChange(1);
	};

	useEffect(() => {
		setLoading(true);
		fetchData();
	}, [page, selectedOrigin, fetchData]);

	return (
		<>
			<div className="u-mb-sm u-text-right">
				<Button variant="secondary" iconBefore="plus" text="Přidat nový" onClick={handleEdit()} />
			</div>
			<BlockLoader loading={loading}>
				{data.origins.length > 0 && (
					<div className="scraper-list">
						<ul className="scraper-list__list">
							{data.origins.map(({ origin, friendlyName }) => (
								<li key={origin} className="scraper-list__item">
									<Button
										className="scraper-list__button"
										onClick={handleSelectOrigin(origin)}
										disabled={selectedOrigin === origin}
									>
										{friendlyName}
									</Button>
								</li>
							))}
						</ul>
						{selectedOrigin && (
							<ul className="scraper-list__list">
								<li className="scraper-list__item">
									<Button className="scraper-list__button" onClick={handleSelectOrigin('')}>
										Zrušit filtr
									</Button>
								</li>
							</ul>
						)}
					</div>
				)}
				{data.scrapers.length ? (
					data.scrapers.map((scraper) => (
						<div
							className={clsx(
								'b-product c-products__item',
								scraper.state === ScraperState.ENABLED ? 'b-product--online' : 'b-product--disabled',
							)}
							key={scraper.id}
						>
							<div className="b-product__header">
								<div className="b-annot-product">
									<div className="b-annot-product__icon">
										<Icon name={icons[scraper.realEstateType]} />
									</div>
									<div className="b-annot-product__content">
										<p className="b-annot-product__caption">Scraper</p>
										<h2 className="b-annot-product__title">
											{generateLabel({
												adType: scraper.adType,
												realEstateType: scraper.realEstateType,
											})}{' '}
											{scraper.friendlyName}
										</h2>
									</div>
									<div className="b-annot-product__btn">
										<div className="switch">
											<input
												type="checkbox"
												className="switch__btn"
												checked={scraper.state === ScraperState.ENABLED}
												onChange={handleSwitch(scraper)}
											/>
										</div>
									</div>
								</div>
							</div>
							<div className="b-product__content">
								<div className="b-product__content-left">
									<div className="b-product__description">
										<div className="b-description">
											<ul className="b-description__list">
												<li className="b-description__item">
													<span className="u-font-medium">Url:</span>{' '}
													<a
														href={scraper.url.replace('%%PAGE%%', scraper.startFromZero ? '0' : '1')}
														target="_blank"
														rel="noreferrer noopener"
														title={scraper.url}
													>
														{scraper.url.substring(0, 50) + '…'}
													</a>
												</li>
												<li className="b-description__item">
													<span className="u-font-medium">Stránkuje od nuly:</span>{' '}
													{scraper.startFromZero ? 'Ano' : 'Ne'}
												</li>
												<li className="b-description__item">
													<span className="u-font-medium">Stránkuje po:</span> {scraper.pageMultiplier}
												</li>
												<li className="b-description__item">
													<span className="u-font-medium">Plán:</span>{' '}
													{scraper.schedule.map(({ gte, lte }) => `${gte}–${lte}`).join(', ')}
												</li>
											</ul>
										</div>
									</div>
								</div>
								<div className="b-product__content-right">
									<div className="b-counter">
										<ul className="b-counter__list">
											<li className="b-counter__item">
												<span className="b-counter__value">{formatNumber(scraper.realEstates)}</span>
												<span className="b-counter__text">nemovitostí v databázi</span>
											</li>
										</ul>
									</div>
								</div>
							</div>
							<div className="b-product__bottom">
								<div className="b-product__bottom-left">
									<div className="b-dates">
										<ul className="b-dates__list">
											<li className="b-dates__item">
												<p>
													Datum vytvoření:{' '}
													<span className="u-font-medium">{formatDate(new Date(scraper.createdAt))}</span>
												</p>
											</li>
										</ul>
									</div>
									<span className="b-product__flags">
										<Flag name={scraper.fetchType} color="purple" />
									</span>
								</div>
								<div className="b-product__bottom-right">
									<div className="b-actions">
										<ul className="b-actions__list">
											<li className="b-actions__item">
												<IconButton
													className="b-actions__btn"
													icon="plus"
													text="Hydratovat"
													withTooltip
													onClick={handleHydrate(scraper.id)}
												/>
											</li>
											<li className="b-actions__item">
												<IconButton
													className="b-actions__btn"
													icon="filter"
													text="Upravit parametry"
													withTooltip
													onClick={handleEdit(scraper.id)}
												/>
											</li>
											<li className="b-actions__item">
												<IconButton
													className="b-actions__btn"
													icon="trashcan"
													text="Vymazat"
													withTooltip
													onClick={handleDelete(scraper.id)}
												/>
											</li>
										</ul>
									</div>
								</div>
							</div>
						</div>
					))
				) : (
					<Message icon="notification">Žádné záznamy</Message>
				)}
				<NextPrev page={page} onChange={onPageChange} showNext={data.scrapers.length >= 10} />
			</BlockLoader>
			<Dialog isOpen={isEditDialogOpen} close={() => setEditDialogOpen(false)}>
				<ScraperForm scraper={selectedItem} onSubmit={handleCreate} />
			</Dialog>
			<Dialog isOpen={isHydrateDialogOpen} close={() => setHydrateDialogOpen(false)}>
				{selectedItem && <HydrateForm scraper={selectedItem} />}
			</Dialog>
		</>
	);
};

ScraperList.displayName = 'ScraperList';
