import { fetchEmailTestNew } from 'api/admin/fetchEmailTestNew';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { EmailInput } from 'components/ui/core/EmailInput';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { EmailTestNewRequest } from 'types/admin';
import { TEN_SECONDS } from 'utils/constants';

type Props = {
	onSubmit?: () => void;
};

export const EmailTestNewForm: FC<Props> = ({ onSubmit }) => {
	const { token } = useUser();
	const { addNotification } = useNotifications();
	const defaultValues = {
		email: '',
		subject: '',
		html: '',
	};
	const context = useForm<EmailTestNewRequest>({ defaultValues });
	const {
		register,
		formState: { isSubmitting, errors },
	} = context;

	const handleSubmit: SubmitHandler<EmailTestNewRequest> = async (data) => {
		const response = await fetchEmailTestNew(data, token);
		if (response.statusCode === 200) {
			addNotification({ message: 'Testovací e-mail zařazen do fronty k odeslání', timeout: TEN_SECONDS });
			onSubmit && onSubmit();
		} else {
			addNotification({ message: 'Chybný požadavek, zkontrolujte parametry', timeout: TEN_SECONDS });
		}
	};

	return (
		<Form<EmailTestNewRequest> context={context} onSubmit={handleSubmit}>
			<div className={clsx('inp-row', errors.email && 'has-error')}>
				<span className="inp-row__top">
					<Label id="email" required>
						E-mail
					</Label>
				</span>
				<span className="inp-fix">
					<EmailInput />
				</span>
				<FormError name="email" />
			</div>

			<div className={clsx('inp-row', errors.subject && 'has-error')}>
				<span className="inp-row__top">
					<Label id="subject" required>
						Předmět
					</Label>
				</span>
				<span className="inp-fix">
					<input type="text" id="subject" className="inp-text" {...register('subject', { required: true })} />
				</span>
				<FormError name="subject" />
			</div>

			<div className={clsx('inp-row', errors.html && 'has-error')}>
				<span className="inp-row__top">
					<Label id="html" required>
						Předmět
					</Label>
				</span>
				<span className="inp-fix">
					<textarea id="html" className="inp-text" rows={10} {...register('html', { required: true })} />
				</span>
				<FormError name="html" />
			</div>

			<div className="form__bottom-container">
				<p className="btn__wrapper">
					<Button
						type="submit"
						className={clsx('btn-loader', isSubmitting && 'is-loading')}
						disabled={isSubmitting}
						iconAfter="share"
						text="Odeslat"
					/>
				</p>
			</div>
		</Form>
	);
};

EmailTestNewForm.displayName = 'EmailTestNewForm';
