import React, { <PERSON> } from 'react';
import { Message } from 'components/ui/core/Message';

type Props = {
	recipients: string[] | null;
};

export const SchemaSuccessBox: FC<Props> = ({ recipients }) => {
	return (
		<Message variant="ok" icon="check">
			JSON schema successfully validated.{' '}
			{recipients != null ? (
				<>
					This offer will be received by <strong>{recipients.length} users</strong>:
					<br />
					<br />
					{recipients.map((recipient, idx) => (
						<React.Fragment key={idx}>
							{recipient} <br />
						</React.Fragment>
					))}
				</>
			) : (
				''
			)}
		</Message>
	);
};
