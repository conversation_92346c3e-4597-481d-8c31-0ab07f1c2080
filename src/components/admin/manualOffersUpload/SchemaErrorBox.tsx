import { FC } from 'react';
import { Message } from 'components/ui/core/Message';
import { JsonSchemaIssue } from 'types/admin';

type Props = {
	validationError: string | null;
	validationIssuesMessages: JsonSchemaIssue[] | null;
};

export const SchemaErrorBox: FC<Props> = ({ validationError, validationIssuesMessages }) => {
	return (
		<Message variant="error" icon="exclamation">
			{validationError ?? 'Something went wrong'}
			{validationIssuesMessages?.map((issue, idx) => (
				<SchemaValidationMessage issue={issue} key={idx} />
			))}
		</Message>
	);
};

const SchemaValidationMessage: FC<{ issue: JsonSchemaIssue }> = ({ issue }) => {
	return (
		<>
			<br />
			<strong>{issue.path.join('/')}</strong>: {issue.code} - {issue.message};
		</>
	);
};
