import { FC, useState } from 'react';
import { fetchNonPublicRealEstatePost } from 'api/admin/fetchNonPublicRealEstatePost';
import { Button } from 'components/ui/core/Button';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { SubmitHandler } from 'react-hook-form';
import { TEN_SECONDS } from 'utils/constants';
import { NonPublicOfferSchema, Offer, OfferType } from 'types/offer';
import { RealEstateType } from 'types/filter';
import { ContactType, JsonSchemaIssue, ManualUploadRequest } from 'types/admin';
import { ErrorMessage } from 'components/ui/core/ErrorMessage';
import { fetchNonPublicRealEstateValidate } from 'api/admin/fetchNonPublicRealEstateValidate';
import { SchemaErrorBox } from './SchemaErrorBox';
import { SchemaSuccessBox } from './SchemaSuccessBox';
import { PreviewModal } from '../PreviewModal';
import { prepareManualOfferForPreview } from 'utils/prepareManualOfferForPreview';
import { JsonTextArea } from '../JsonTextArea';
import { useHasSubmitted } from 'hooks/useHasSubmitted';

enum Status {
	DRAFT,
	INVALID,
	VALIDATED,
	PREVIEWED,
}

type StatusMapItem = { label: string; action: SubmitHandler<ManualUploadRequest> };

type StatusMap = Record<Status, StatusMapItem>;

const startingSchema: NonPublicOfferSchema = {
	realEstate: {
		adType: OfferType.SALE,
		agent: ContactType.AGENT,
		area: 0,
		basement: null,
		buildingType: null,
		conditionType: null,
		description: null,
		disposition: null,
		elevator: null,
		floor: null,
		gps: null,
		houseType: null,
		imageUrl: null,
		landArea: null,
		landType: null,
		landWidth: null,
		location: null,
		origin: null,
		ownershipType: null,
		parkingType: null,
		price: null,
		priceNote: null,
		priceText: null,
		realEstateType: RealEstateType.APARTMENT,
		terraceType: null,
		title: null,
		url: null,
	},
	contact: {
		agencyName: null,
		contactType: ContactType.AGENT,
		email: '',
		name: null,
		telephone: null,
	},
};

export const ManualRealEstateUploadForm: FC = () => {
	const [status, setStatus] = useState<Status>(Status.DRAFT);
	const [error, setError] = useState<null | string>(null);
	const [validationError, setValidationError] = useState<null | string>(null);
	const [recipients, setRecipients] = useState<string[] | null>(null);
	const [validationIssuesMessages, setValidationIssuesMessages] = useState<JsonSchemaIssue[] | null>(null);
	const [offerToPreview, setOfferToPreview] = useState<Offer | null>(null);
	const [previewIsOpen, setPreviewIsOpen] = useState(false);

	const { hasSubmitted, notifySubmission } = useHasSubmitted();
	const { token } = useUser();
	const { addNotification } = useNotifications();

	const handleValidationAndRecipientsNumber: SubmitHandler<ManualUploadRequest> = async (data) => {
		try {
			const res = await fetchNonPublicRealEstateValidate(data, token);

			if (res.statusCode !== 200) {
				setStatus(Status.INVALID);
				if (res.data?.issues) {
					setValidationIssuesMessages(res.data.issues);
					setValidationError('JSON schema is not valid - Check the issues below:');
				} else {
					setValidationError(res.message);
				}
				return;
			}

			if (res.data.isValid) setStatus(Status.VALIDATED);

			if (res.data.users != null) setRecipients(res.data.users);
		} catch (error) {
			console.error(error);
		}
	};

	const handlePreview: SubmitHandler<ManualUploadRequest> = async (data) => {
		const parsedOffer = JSON.parse(data.stringifiedSchema);
		const offerForPreview = prepareManualOfferForPreview(parsedOffer);

		setOfferToPreview(offerForPreview);
		setStatus(Status.PREVIEWED);
		openPreview();
	};

	const handleSubmit: SubmitHandler<ManualUploadRequest> = async (data) => {
		setError(null);

		const response = await fetchNonPublicRealEstatePost(data.stringifiedSchema, token);
		if (response.statusCode === 200) {
			addNotification({ message: 'Offer uploaded', timeout: TEN_SECONDS });
			notifySubmission();
			revertToDraft();
		} else {
			setError(response.message);
		}
	};

	const openPreview = () => setPreviewIsOpen(true);
	const closePreview = () => setPreviewIsOpen(false);
	const revertToDraft = () => setStatus(Status.DRAFT);

	const initialStatus = { label: 'Validate & check recipients number', action: handleValidationAndRecipientsNumber };
	const statusMap: StatusMap = {
		0: initialStatus,
		1: initialStatus,
		2: { label: 'Preview', action: handlePreview },
		3: { label: 'Odeslat', action: handleSubmit },
	};

	const currentStatus = statusMap[status];

	return (
		<>
			{error && <ErrorMessage>{error}</ErrorMessage>}
			<JsonTextArea<NonPublicOfferSchema>
				startingData={startingSchema}
				onSubmit={currentStatus.action}
				onInputChange={revertToDraft}
				buttonLabel={currentStatus.label}
				hasSubmitted={hasSubmitted}
			>
				<>
					{status >= Status.PREVIEWED ? (
						<Button
							variant="outline"
							disabled={previewIsOpen}
							text={'Preview'}
							style={{ marginLeft: 10 }}
							onClick={openPreview}
						/>
					) : null}
				</>
			</JsonTextArea>

			{status === Status.INVALID ? (
				<SchemaErrorBox validationError={validationError} validationIssuesMessages={validationIssuesMessages} />
			) : null}

			{status >= Status.VALIDATED ? <SchemaSuccessBox recipients={recipients} /> : null}

			{offerToPreview ? (
				<PreviewModal closePreview={closePreview} offerToPreview={offerToPreview} previewIsOpen={previewIsOpen} />
			) : null}
		</>
	);
};

ManualRealEstateUploadForm.displayName = 'ManualRealEstateUpload';
