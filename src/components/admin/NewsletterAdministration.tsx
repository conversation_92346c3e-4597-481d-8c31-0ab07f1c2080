import { fetchNewsletterPost } from 'api/admin/fetchNewsletterPost';
import { fetchNewsletterPreview } from 'api/admin/fetchNewsletterPreview';
import { NewsletterForm } from 'components/admin/NewsletterForm';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { FC, useCallback, useEffect, useState } from 'react';
import { EmailPreviewPayload } from 'types/admin';

export const NewsletterAdministration: FC = () => {
	const { token } = useUser();
	const { addNotification } = useNotifications();
	const [preview, setPreview] = useState<EmailPreviewPayload | undefined>();
	const [count, setCount] = useState(0);
	const [loading, setLoading] = useState(false);

	const fetchData = useCallback(async () => {
		if (!token) return;

		try {
			const response = await fetchNewsletterPreview(token);
			setPreview(response.data.email);
			setCount(response.data.users);
		} catch (e) {
			console.error(e);
		}
		setLoading(false);
	}, [token]);

	const handleSubmit = async (campaign: string) => {
		try {
			await fetchNewsletterPost({ campaign }, token);
			addNotification({ message: 'Newsletter se rozešle uživatelům...' });
		} catch (e) {
			addNotification({ message: 'Došlo k chybě, newsletter nelze rozeslat.' });
		}
	};

	useEffect(() => {
		setLoading(true);
		fetchData();
	}, [fetchData]);

	return (
		<>
			<BlockLoader loading={loading}>
				<div className="b-dialog b-dialog--preview u-mx-auto">
					<p className="u-text-center">
						<strong>{preview?.subject}</strong>
					</p>
					{preview && <iframe title="Preview" id="preview" srcDoc={preview.html} sandbox="allow-same-origin" />}
				</div>
			</BlockLoader>
			<div className="grid">
				<div className="grid__cell size--6-12@md">
					<p>
						Tento newsletter bude odeslán <strong>{count} uživatelům</strong>.
					</p>
				</div>
				<div className="grid__cell size--6-12@md">
					<NewsletterForm onSubmit={handleSubmit} />
				</div>
			</div>
		</>
	);
};

NewsletterAdministration.displayName = 'NewsletterAdministration';
