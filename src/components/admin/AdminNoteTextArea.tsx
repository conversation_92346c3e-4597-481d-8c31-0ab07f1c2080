import { fetchUserUpdate } from 'api/admin/fetchUserUpdate';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { Label } from 'components/ui/core/Label';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { AdminNoteRequest, UserPayload } from 'types/admin';
import { TEN_SECONDS } from 'utils/constants';

type Props = {
	userPayload: UserPayload;
};

export const AdminNoteTextArea: FC<Props> = ({ userPayload }) => {
	const { token } = useUser();
	const { addNotification } = useNotifications();
	const defaultValues = {
		note: userPayload.adminInfo?.adminNote || '',
	};
	const context = useForm<AdminNoteRequest>({ defaultValues });
	const {
		formState: { isSubmitting },
		register,
	} = context;

	const handleSubmit: SubmitHandler<AdminNoteRequest> = async (data) => {
		const response = await fetchUserUpdate(
			userPayload.id,
			// eslint-disable-next-line @typescript-eslint/ban-ts-comment
			// @ts-ignore
			{ ...userPayload, telephone: userPayload.telephone || '', adminNote: data.note },
			token,
		);
		if (response.statusCode === 200) {
			// TODO: customise messages in CZ (but no need for Intl since it's not user-facing)
			addNotification({ message: 'Admin note successfully updated', timeout: TEN_SECONDS });
		} else {
			addNotification({ message: 'An error occurred', timeout: TEN_SECONDS });
		}
	};

	return (
		<Form<AdminNoteRequest> context={context} onSubmit={handleSubmit}>
			<div className="inp-row">
				<span className="inp-row__top">
					<Label id="html">Poznámky administrátora:</Label>
				</span>
				<span className="inp-fix">
					<textarea id="html" className="inp-text" rows={6} {...register('note')} />
				</span>
			</div>
			<div className="form__bottom-container">
				<p className="btn__wrapper">
					<Button
						type="submit"
						className={clsx('btn-loader', isSubmitting && 'is-loading')}
						disabled={isSubmitting}
						text="Odeslat"
					/>
				</p>
			</div>
		</Form>
	);
};
