import { fetchScraperCreate } from 'api/admin/fetchScraperCreate';
import { fetchScraperUpdate } from 'api/admin/fetchScraperUpdate';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { Checkbox } from 'components/ui/core/Checkbox';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { Select } from 'components/ui/core/Select';
import { Tooltip } from 'components/ui/tooltip/Tooltip';
import { useUser } from 'components/user/UserProvider';
import { FC, MouseEventHandler } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FetchType, ScraperPayload, ScraperRequest, ScraperState } from 'types/admin';
import { RealEstateType } from 'types/filter';
import { OfferType } from 'types/offer';

type Props = {
	scraper?: ScraperPayload;
	onSubmit?: (scraper: ScraperPayload) => void;
};

export const ScraperForm: FC<Props> = ({ scraper, onSubmit }) => {
	const { token } = useUser();
	const defaultValues = {
		adType: scraper?.adType ?? OfferType.SALE,
		checkUpdates: scraper?.checkUpdates ?? true,
		fetchType: scraper?.fetchType ?? FetchType.STATIC,
		pageMultiplier: scraper?.pageMultiplier ?? 1,
		realEstateType: scraper?.realEstateType ?? RealEstateType.APARTMENT,
		schedule: scraper?.schedule ?? [
			{ gte: '00:00:00', lte: '02:29:59' },
			{ gte: '05:00:00', lte: '23:59:59' },
		],
		startFromZero: scraper?.startFromZero ?? false,
		state: scraper?.state ?? ScraperState.DISABLED,
		url: scraper?.url ?? '',
	};

	const context = useForm<ScraperRequest>({ defaultValues });
	const {
		register,
		formState: { isSubmitting, errors },
		watch,
		setValue,
	} = context;
	const scheduleWatch = watch('schedule');

	const handleSubmit: SubmitHandler<ScraperRequest> = async (values) => {
		const data = { ...defaultValues, ...values };
		if (scraper) {
			const response = await fetchScraperUpdate(scraper.id, data, token);
			onSubmit && onSubmit(response.data.scraper);
		} else {
			const response = await fetchScraperCreate(data, token);
			onSubmit && onSubmit(response.data.scraper);
		}
	};

	const handleAddScheduleItem: MouseEventHandler<HTMLAnchorElement> = (event) => {
		event.preventDefault();
		setValue('schedule', [...scheduleWatch, { gte: '', lte: '' }]);
	};

	const handleDeleteScheduleItem = (index: number) => () => {
		setValue(
			'schedule',
			scheduleWatch.filter((_, key) => index !== key),
		);
	};

	return (
		<Form<ScraperRequest> context={context} onSubmit={handleSubmit}>
			<Select
				name="adType"
				label="Typ inzerátu"
				items={[
					{ label: 'Prodej', value: OfferType.SALE },
					{ label: 'Pronájem', value: OfferType.RENT },
				]}
				required
			/>
			<Select
				name="fetchType"
				label="Typ scrapu"
				items={[
					{ label: 'Statický (fetch)', value: FetchType.STATIC },
					{ label: 'Dynamický (Puppeteer)', value: FetchType.DYNAMIC },
				]}
				required
			/>
			<Select
				name="realEstateType"
				label="Typ nemovitosti"
				items={[
					{ label: 'Byt', value: RealEstateType.APARTMENT },
					{ label: 'Dům', value: RealEstateType.HOUSE },
					{ label: 'Pozemek', value: RealEstateType.LAND },
				]}
				required
			/>
			<div className="grid">
				<div className="grid__cell size--6-12">
					<Checkbox label="Stránkování začíná od nuly" name="startFromZero" />
				</div>
				<div className="grid__cell size--6-12">
					<div className={clsx('inp-row', errors.pageMultiplier && 'has-error')}>
						<span className="inp-row__top">
							<Label id="pageMultiplier" required>
								Násobič stránkování
							</Label>
							<Tooltip forInput>
								Změň pouze v případě web nestránkuje po jedné (1, 2, 3...), ale po více číslech (20, 40, 60...)
							</Tooltip>
						</span>
						<span className="inp-fix">
							<input
								type="number"
								id="pageMultiplier"
								className="inp-text"
								{...register('pageMultiplier', {
									required: 'Násobič stránkování je povinná položka',
									min: 1,
									valueAsNumber: true,
								})}
							/>
						</span>
						<FormError name="pageMultiplier" />
					</div>
				</div>
			</div>
			<div className={clsx('inp-row', errors.url && 'has-error')}>
				<span className="inp-row__top">
					<Label id="url" required>
						Url
					</Label>
					<Tooltip forInput>Url adresa musí obsahovat řetězec `%%PAGE%%` pro identifikaci stránkování</Tooltip>
				</span>
				<span className="inp-fix">
					<input
						type="url"
						id="url"
						className="inp-text"
						placeholder="https://..."
						{...register('url', {
							required: 'Url scraperu je povinná položka',
							pattern: {
								value: /%%PAGE%%/,
								message: 'Url adresa musí obsahovat řetězec `%%PAGE%%` pro identifikaci stránkování',
							},
						})}
					/>
				</span>
				<FormError name="url" />
			</div>

			<span className="inp-label">Plánování</span>
			{scheduleWatch.map((_value, index) => (
				<div key={index} className="grid grid--y-0">
					{(['gte', 'lte'] as ['gte', 'lte']).map((constraint) => (
						<div key={constraint} className="grid__cell size--5-12">
							<div
								className={clsx(
									'inp-row',
									errors.schedule && errors.schedule[index] && errors.schedule[index][constraint] && 'has-error',
								)}
							>
								<span className="inp-row__top">
									<Label id={`schedule.${index}.${constraint}`}>{constraint === 'gte' ? 'Od' : 'Do'}</Label>
								</span>
								<span className="inp-fix">
									<input
										type="text"
										id={`schedule.${index}.${constraint}`}
										className="inp-text"
										placeholder="00:00:00"
										{...register(`schedule.${index}.${constraint}`, {
											pattern: {
												value: /\d\d:\d\d:\d\d/,
												message: 'Zadejte čas ve formátu hh:mm:ss',
											},
										})}
									/>
								</span>
								<FormError name={`schedule.${index}.${constraint}`} />
							</div>
						</div>
					))}
					<div className="grid__cell size--2-12">
						<div>
							<Label>&nbsp;</Label>
						</div>
						<Button iconOnly="close" variant="secondary" onClick={handleDeleteScheduleItem(index)} />
					</div>
				</div>
			))}
			<div className="u-mb-xs u-text-right">
				<a href="#" role="button" onClick={handleAddScheduleItem}>
					+ Přidat další časový úsek
				</a>
			</div>

			<Checkbox label="Hlídat změny u inzerátů" name="checkUpdates" />

			<Select
				label="Stav scraperu"
				name="state"
				items={[
					{ label: 'Aktivní', value: ScraperState.ENABLED },
					{ label: 'Vypnutý', value: ScraperState.DISABLED },
				]}
			/>
			<div className="form__bottom-container">
				<p className="btn__wrapper">
					<Button
						type="submit"
						className={clsx('btn-loader', isSubmitting && 'is-loading')}
						disabled={isSubmitting}
						iconAfter="arrow-right"
						text={scraper ? 'Upravit scraper' : 'Nastavit scrapování'}
					/>
				</p>
			</div>
		</Form>
	);
};

ScraperForm.displayName = 'ScraperForm';
