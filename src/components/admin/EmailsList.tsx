import { fetchEmailPreview } from 'api/admin/fetchEmailPreview';
import { fetchEmails } from 'api/admin/fetchEmails';
import clsx from 'clsx';
import { EmailTestCopyForm } from 'components/admin/EmailTestCopyForm';
import { EmailTestNewForm } from 'components/admin/EmailTestNewForm';
import { Link } from 'components/Link';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Button } from 'components/ui/core/Button';
import { Icon } from 'components/ui/core/Icon';
import { Message } from 'components/ui/core/Message';
import { NextPrev } from 'components/ui/core/NextPrev';
import { Dialog } from 'components/ui/dialog/Dialog';
import { useUser } from 'components/user/UserProvider';
import { useNextPrev } from 'hooks/useNextPrev';
import { FC, useCallback, useEffect, useState } from 'react';
import { EmailPayload, EmailPreviewPayload } from 'types/admin';
import { formatDateTime } from 'utils/formats';

enum SelectDialog {
	NONE,
	PREVIEW,
	TEST_COPY,
	TEST_NEW,
}

export const EmailsList: FC = () => {
	const { token } = useUser();
	const [items, setItems] = useState<EmailPayload[]>([]);
	const [loading, setLoading] = useState(false);
	const [preview, setPreview] = useState<EmailPreviewPayload | undefined>();
	const [testId, setTestId] = useState<string>();

	const [dialogOpen, setDialogOpen] = useState<SelectDialog>(SelectDialog.NONE);
	const openDialog = (dialog: SelectDialog) => setDialogOpen(dialog);
	const closeDialog = () => setDialogOpen(SelectDialog.NONE);

	const [page, onPageChange] = useNextPrev();

	const fetchData = useCallback(async () => {
		if (!token) return;

		try {
			const response = await fetchEmails(token, page);
			setItems(response.data.emails);
		} catch (e) {
			console.error(e);
		}
		setLoading(false);
	}, [page, token]);

	const fetchPreview = async (id: string) => {
		if (!token) return;
		setLoading(true);

		try {
			const response = await fetchEmailPreview(token, id);
			setPreview(response.data.email);
			openDialog(SelectDialog.PREVIEW);
		} catch (e) {
			console.error(e);
		}
		setLoading(false);
	};

	const handlePreviewClick = (id: string) => async () => {
		await fetchPreview(id);
	};

	const handleTestCopyClick = (id: string) => () => {
		setTestId(id);
		openDialog(SelectDialog.TEST_COPY);
	};

	const handleTestNewClick = () => {
		openDialog(SelectDialog.TEST_NEW);
	};

	useEffect(() => {
		setLoading(true);
		fetchData();
	}, [page, fetchData]);

	return (
		<>
			<div className="u-mb-sm u-text-right">
				<Button variant="secondary" iconBefore="share" text="Otestovat HTML" onClick={handleTestNewClick} />
			</div>
			<BlockLoader loading={loading}>
				{items.length ? (
					items.map((item) => (
						<div
							className={clsx(
								'b-product',
								'c-products__item',
								item.complainedAt || (item.bouncedAt && 'u-bg-orange'),
								item.interactedAt && 'u-bg-purple',
								!item.interactedAt && !item.openedAt && 'u-bg-gray',
							)}
							key={item.id}
						>
							<div className="b-product__header">
								<div className="b-annot-product">
									<div className="b-annot-product__content">
										<p className="b-annot-product__caption">Příjemce</p>
										<h2 className="b-annot-product__title">
											<Link href="/administrace/uzivatel/[id]" as={`/administrace/uzivatel/${item.recipientId}`}>
												{item.recipientId}
											</Link>
										</h2>
									</div>
								</div>
							</div>
							<div className="b-product__content">
								<div className="b-product__content-left">
									<div className="b-product__description">
										<div className="b-description">
											<ul className="b-description__list">
												{item.createdAt && (
													<li className="b-description__item">
														<span className="u-font-medium">Vytvořeno:</span>{' '}
														{formatDateTime(new Date(item.createdAt))}
													</li>
												)}
												{item.sentAt && (
													<li className="b-description__item">
														<span className="u-font-medium">Odesláno:</span>{' '}
														{formatDateTime(new Date(item.sentAt))}
													</li>
												)}
												{item.deliveredAt && (
													<li className="b-description__item">
														<span className="u-font-medium">Doručeno:</span>{' '}
														{formatDateTime(new Date(item.deliveredAt))}
													</li>
												)}
												{item.openedAt && (
													<li className="b-description__item">
														<span className="u-font-medium">Otevřeno:</span>{' '}
														{formatDateTime(new Date(item.openedAt))}
													</li>
												)}
												{item.interactedAt && (
													<li className="b-description__item">
														<span className="u-font-medium">Proklik:</span>{' '}
														{formatDateTime(new Date(item.interactedAt))}
													</li>
												)}
												{item.bouncedAt && (
													<li className="b-description__item">
														<span className="u-font-medium">Bounce:</span>{' '}
														{formatDateTime(new Date(item.bouncedAt))}
													</li>
												)}
												{item.complainedAt && (
													<li className="b-description__item">
														<span className="u-font-medium">Stížnost:</span>{' '}
														{formatDateTime(new Date(item.complainedAt))}
													</li>
												)}
											</ul>
										</div>
									</div>
								</div>
								{item.interactions.length > 0 && (
									<div className="b-product__content-right" style={{ overflow: 'hidden' }}>
										<p>Prokliky</p>
										<div className="b-product__description">
											<div className="b-description">
												<ul className="b-description__list">
													{item.interactions.map((url) => (
														<li key={url} className="b-description__item" style={{ overflow: 'hidden' }}>
															<span
																title={url}
																style={{
																	display: 'block',
																	whiteSpace: 'nowrap',
																	overflow: 'hidden',
																	textOverflow: 'ellipsis',
																}}
															>
																{url}
															</span>
														</li>
													))}
												</ul>
											</div>
										</div>
									</div>
								)}
							</div>
							<div className="b-product__bottom">
								<div className="b-product__bottom-left">
									<span className="flag flag--purple">
										<span className="flag__inner">
											<span className="flag__icon">
												<Icon name="tip" />
											</span>
											<span className="flag__text">{item.template}</span>
										</span>
									</span>
								</div>
								<div className="b-product__bottom-right">
									<Button variant="secondary" onClick={handleTestCopyClick(item.id)}>
										Odeslat test
									</Button>
									<Button onClick={handlePreviewClick(item.id)}>Zobrazit náhled</Button>
								</div>
							</div>
						</div>
					))
				) : (
					<Message icon="notification">Žádné záznamy</Message>
				)}
				<NextPrev page={page} onChange={onPageChange} showNext={items.length >= 10} />
			</BlockLoader>
			<Dialog isOpen={dialogOpen === SelectDialog.PREVIEW} close={closeDialog} className="b-dialog--preview">
				<p className="u-text-center">{preview?.subject}</p>
				<p className="u-text-center">{preview?.to}</p>
				{preview && <iframe title="Preview" id="preview" srcDoc={preview.html} sandbox="allow-same-origin" />}
			</Dialog>
			<Dialog isOpen={dialogOpen === SelectDialog.TEST_COPY} close={closeDialog}>
				{testId && <EmailTestCopyForm id={testId} onSubmit={closeDialog} />}
			</Dialog>
			<Dialog isOpen={dialogOpen === SelectDialog.TEST_NEW} close={closeDialog}>
				<EmailTestNewForm onSubmit={closeDialog} />
			</Dialog>
		</>
	);
};

EmailsList.displayName = 'EmailsList';
