import clsx from 'clsx';
import { contactTypes } from 'components/admin/ContactList';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { Label } from 'components/ui/core/Label';
import { Select } from 'components/ui/core/Select';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ContactType } from 'types/admin';

type Props = {
	onSubmit: (query: string, contactType: ContactType | '') => void;
};

type Inputs = {
	query: string;
	contactType: ContactType | '';
};

export const ContactFilterForm: FC<Props> = ({ onSubmit }) => {
	const defaultValues = {
		query: '',
		contactType: '' as const,
	};

	const context = useForm<Inputs>({ defaultValues });
	const {
		register,
		formState: { isSubmitting },
	} = context;

	const handleSubmit: SubmitHandler<Inputs> = (data) => onSubmit(data.query, data.contactType);

	return (
		<Form<Inputs> context={context} onSubmit={handleSubmit}>
			<div className="grid">
				<div className="grid__cell size--3-12">
					<div className="inp-row">
						<span className="inp-row__top">
							<Label id="query">Vyhledávání</Label>
						</span>
						<span className="inp-fix">
							<input type="text" id="query" className="inp-text" {...register('query')} />
						</span>
					</div>
				</div>
				<div className="grid__cell size--3-12">
					<Select name="contactType" label="Typ kontaktu" items={[{ label: 'Všechny', value: '' }, ...contactTypes]} />
				</div>

				<div className="grid__cell size--3-12">
					<div className="form__bottom-container">
						<span className="inp-row__top">
							<Label>&nbsp;</Label>
						</span>
						<p className="btn__wrapper">
							<Button
								type="submit"
								className={clsx('btn-loader', isSubmitting && 'is-loading')}
								disabled={isSubmitting}
								iconAfter="arrow-right"
								text="Hledat"
							/>
						</p>
					</div>
				</div>
			</div>
		</Form>
	);
};

ContactFilterForm.displayName = 'ContactFilterForm';
