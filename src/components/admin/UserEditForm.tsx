import { fetchUserUpdate } from 'api/admin/fetchUserUpdate';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { CheckboxOrRadioGroup } from 'components/ui/core/CheckboxOrRadioGroup';
import { DateTimePicker } from 'components/ui/core/DateTimePicker';
import { EmailInput } from 'components/ui/core/EmailInput';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { Select } from 'components/ui/core/Select';
import { TelephoneInput } from 'components/ui/core/TelephoneInput';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { Tooltip } from 'components/ui/tooltip/Tooltip';
import { CheckBoxOptionsGroup } from 'components/user/checkBoxOptionsGroup/CheckBoxOptionsGroup';
import { useUser } from 'components/user/UserProvider';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { defaultLocale } from 'i18n/supportedLocales';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
import { UserPayload, UserUpdateRequest } from 'types/admin';
import { UserRole, UserState } from 'types/user';
import { ConsentOption } from 'types/userConsents';
import { TEN_SECONDS } from 'utils/constants';

type Props = {
	user: UserPayload;
	onSubmit?: (user: UserPayload) => void;
};

export const UserEditForm: FC<Props> = ({ user, onSubmit }) => {
	const { commonTelephoneIntlString } = commonInltMessages;
	const { token } = useUser();
	const { addNotification } = useNotifications();
	const { locale: currentlySelectedLocale } = useRouter();
	const userCurrentTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
	const defaultValues = {
		apiAccess: user.apiAccess ?? [],
		emailPreferences: user.emailPreferences ?? [],
		name: user.name ?? '',
		surname: user.surname ?? '',
		email: user.email,
		role: user.role ?? UserRole.USER,
		roleExpiresAt: user.roleExpiresAt ?? null,
		state: user.state ?? UserState.ACTIVE,
		locale: user.locale ?? currentlySelectedLocale ?? defaultLocale,
		timezone: user.timezone ?? userCurrentTimezone,
		telephone: user.telephone ?? '',
	};

	// eslint-disable-next-line @typescript-eslint/ban-ts-comment
	// @ts-ignore
	const context = useForm<UserUpdateRequest>({ defaultValues });
	const {
		register,
		formState: { isSubmitting, errors },
	} = context;

	const handleSubmit: SubmitHandler<UserUpdateRequest> = async (data) => {
		data.roleExpiresAt = data.roleExpiresAt ? new Date(data.roleExpiresAt).getTime() : null;

		const response = await fetchUserUpdate(user.id, data, token);
		if (response.statusCode === 200) {
			addNotification({ message: 'Informace o uživateli jsou upravené', timeout: TEN_SECONDS });
			onSubmit && onSubmit(response.data.user);
		} else {
			addNotification({ message: 'Chybný požadavek, zkontrolujte parametry', timeout: TEN_SECONDS });
		}
	};

	return (
		<Form<UserUpdateRequest> context={context} onSubmit={handleSubmit}>
			<div className={clsx('inp-row', errors.name && 'has-error')}>
				<span className="inp-row__top">
					<Label id="name">Jméno</Label>
				</span>
				<span className="inp-fix">
					<input type="text" id="name" className="inp-text" {...register('name')} />
				</span>
				<FormError name="name" />
			</div>

			<div className={clsx('inp-row', errors.surname && 'has-error')}>
				<span className="inp-row__top">
					<Label id="surname">Příjmení</Label>
				</span>
				<span className="inp-fix">
					<input type="text" id="surname" className="inp-text" {...register('surname')} />
				</span>
				<FormError name="surname" />
			</div>

			<div className={clsx('inp-row', errors.email && 'has-error')}>
				<span className="inp-row__top">
					<Label id="email" required>
						E-mail
					</Label>
					<Tooltip forInput>
						Pokud nově zadaný e-mail již existuje, dojde ke sloučení uživatelských účtů, jejich filtrů a tento účet zanikne.{' '}
						<br /> Měňte pouze v nejzazších případech.
					</Tooltip>
				</span>
				<span className="inp-fix">
					<EmailInput />
				</span>
				<FormError name="email" />
			</div>

			<div className={clsx('inp-row', errors.telephone && 'has-error')}>
				<span className="inp-row__top">
					<Label id="telephone">
						<FormattedMessage {...commonTelephoneIntlString} />
					</Label>
				</span>
				<span className="inp-fix">
					<TelephoneInput />
				</span>
				<FormError name="telephone" />
			</div>

			<Select
				name="role"
				label="Role (neměnit určně - přestane fungovat platební brána)"
				items={[
					{ label: 'Uživatel', value: UserRole.USER },
					{ label: 'Mini', value: UserRole.MINI },
					{ label: 'Standard', value: UserRole.STANDARD },
					{ label: 'Premium', value: UserRole.PREMIUM },
					{ label: 'Profi', value: UserRole.PROFI },
					{ label: 'Enterprise', value: UserRole.ENTERPRISE },
					{ label: 'Pokročilý', value: UserRole.ADVANCED },
					{ label: 'Administrátor', value: UserRole.ADMIN },
				]}
				required
			/>

			<DateTimePicker name="roleExpiresAt" label="Role vyprší" />

			<Select
				name="state"
				label="Stav"
				items={[
					{ label: 'Vytvořený', value: UserState.CREATED },
					{ label: 'Aktivní', value: UserState.ACTIVE },
					{ label: 'Zablokovaný', value: UserState.BLOCKED },
					{ label: 'Smazaný', value: UserState.DELETED },
				]}
				required
			/>

			<CheckBoxOptionsGroup
				optionsAvailable={[
					{
						value: ConsentOption.OFFER,
						label: 'Nabídky',
						tooltip:
							'Nabídky realit na základě vašich filtrů. Zrušením této položky vám budeme muset vypnout všechny vaše filtry.',
					},
					{
						value: ConsentOption.NEWSLETTER,
						label: 'Newslettery',
						tooltip: 'Občas vám budeme zasílat novinky z Visidoo',
					},
				]}
				name="emailPreferences"
				label="Předvolby zasílání e-mailů"
			/>

			<CheckboxOrRadioGroup
				label="Přístup k API"
				name="apiAccess"
				items={[
					{ label: 'GET /v1/autocomplete', value: 'GET /v1/autocomplete' },
					{ label: 'GET /v1/real-estate', value: 'GET /v1/real-estate' },
					{ label: 'POST /v1/report/{requestId}', value: 'POST /v1/report/{requestId}' },
					{ label: 'GET /v1/history', value: 'GET /v1/history' },
					{ label: 'GET /v1/history/{realEstateId}', value: 'GET /v1/history/{realEstateId}' },
				]}
				size="lg"
			/>

			<div className="form__bottom-container">
				<p className="btn__wrapper">
					<Button
						type="submit"
						className={clsx('btn-loader', isSubmitting && 'is-loading')}
						disabled={isSubmitting}
						iconAfter="arrow-right"
						text="Upravit uživatele"
					/>
				</p>
			</div>
		</Form>
	);
};

UserEditForm.displayName = 'UserEditForm';
