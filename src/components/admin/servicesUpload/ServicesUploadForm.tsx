import { fetchServicesList } from 'api/services/fetchServicesList';
import { fetchServicesPost } from 'api/admin/fetchServicesPost';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { ErrorMessage } from 'components/ui/core/ErrorMessage';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { useHasSubmitted } from 'hooks/useHasSubmitted';
import { FC, useCallback, useEffect, useState } from 'react';
import { SubmitHandler } from 'react-hook-form';
import { Service, ManualUploadRequest } from 'types/admin';
import { ONE_MONTH, TEN_SECONDS } from 'utils/constants';
import { JsonTextArea } from '../JsonTextArea';

const fallbackStartingData: Service[] = [
	{
		name: 'my-service-name',
		query: {
			bool: {
				must: [
					{
						term: {
							adType: 'Sale',
						},
					},
				],
			},
		},
		validFrom: new Date().toISOString(),
		validTo: new Date(Date.now() + ONE_MONTH).toISOString(),
	},
];

export const ServicesUploadForm: FC = () => {
	const { token } = useUser();
	const { addNotification } = useNotifications();
	const { hasSubmitted, notifySubmission } = useHasSubmitted();

	const [startingData, setStartingData] = useState<Service[] | null>(null);
	const [error, setError] = useState<null | string>(null);

	const showFallbackData = () => setStartingData(fallbackStartingData);

	const handleSubmit: SubmitHandler<ManualUploadRequest> = async (data) => {
		setError(null);
		const response = await fetchServicesPost(data.stringifiedSchema, token);
		if (response.statusCode === 200) {
			const updatedServices = response.data.services;
			addNotification({ message: 'Services uploaded', timeout: TEN_SECONDS });
			setStartingData(updatedServices);
			notifySubmission();
		} else {
			setError(response.message);
		}
	};

	const fetchServices = useCallback(async () => {
		try {
			const res = await fetchServicesList();
			if (res.data.services && res.data.services.length > 0) {
				setStartingData(res.data.services);
			} else {
				showFallbackData();
			}
		} catch (error) {
			console.error(error);
			showFallbackData();
		}
	}, []);

	useEffect(() => {
		fetchServices();
	}, [fetchServices]);

	if (!startingData) return <BlockLoader loading />;

	return (
		<>
			{error && <ErrorMessage>{error}</ErrorMessage>}
			<JsonTextArea<Service[]>
				startingData={startingData}
				onSubmit={handleSubmit}
				buttonLabel="Delete current and upload new services"
				hasSubmitted={hasSubmitted}
			/>
		</>
	);
};
