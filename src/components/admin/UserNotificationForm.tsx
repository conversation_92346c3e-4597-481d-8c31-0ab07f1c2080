import { fetchNotificationSend } from 'api/admin/fetchNotificationSend';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { NotificationPostRequest } from 'types/admin';
import { TEN_SECONDS } from 'utils/constants';

type Props = {
	id: string;
	onSubmit?: () => void;
};

export const UserNotificationForm: FC<Props> = ({ id, onSubmit }) => {
	const { token } = useUser();
	const { addNotification } = useNotifications();
	const defaultValues = {
		userId: id,
		realEstateId: '',
	};
	const context = useForm<NotificationPostRequest>({ defaultValues });
	const {
		register,
		formState: { isSubmitting, errors },
	} = context;

	const handleSubmit: SubmitHandler<NotificationPostRequest> = async (data) => {
		const response = await fetchNotificationSend(data, token);
		if (response.statusCode === 200) {
			addNotification({ message: 'Testovací e-mail zařazen do fronty k odeslání', timeout: TEN_SECONDS });
			onSubmit && onSubmit();
		} else {
			addNotification({ message: 'Chybný požadavek, zkontrolujte parametry', timeout: TEN_SECONDS });
		}
	};

	return (
		<Form<NotificationPostRequest> context={context} onSubmit={handleSubmit}>
			<div className={clsx('inp-row', errors.realEstateId && 'has-error')}>
				<span className="inp-row__top">
					<Label id="realEstateId" required>
						Id nemovitosti
					</Label>
				</span>
				<span className="inp-fix">
					<input
						type="text"
						id="realEstateId"
						className="inp-text"
						{...register('realEstateId', { required: 'Id nemovitosti je povinná položka.' })}
					/>
				</span>
				<FormError name="realEstateId" />
			</div>

			<div className="form__bottom-container">
				<p className="btn__wrapper">
					<Button
						type="submit"
						className={clsx('btn-loader', isSubmitting && 'is-loading')}
						disabled={isSubmitting}
						iconAfter="arrow-right"
						text="Odeslat notifikaci"
					/>
				</p>
			</div>
		</Form>
	);
};

UserNotificationForm.displayName = 'UserNotificationForm';
