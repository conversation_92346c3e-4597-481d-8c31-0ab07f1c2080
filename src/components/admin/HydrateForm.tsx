import { fetchScraperHydrate } from 'api/admin/fetchScraperHydrate';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { Title } from 'components/ui/core/Title';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { Tooltip } from 'components/ui/tooltip/Tooltip';
import { useUser } from 'components/user/UserProvider';
import { useShortLocale } from 'i18n/useShortLocale';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { ScraperPayload } from 'types/admin';
import { TEN_SECONDS } from 'utils/constants';
import { getFiltersLocalizedStrings } from 'utils/getFiltersLocalizedStrings';

type Props = {
	scraper: ScraperPayload;
};

type Inputs = {
	from: number;
	to: number;
};

export const HydrateForm: FC<Props> = ({ scraper }) => {
	const { token } = useUser();
	const shortLocale = useShortLocale();
	const intl = useIntl();
	const { generateLabel } = getFiltersLocalizedStrings(intl, shortLocale);
	const { addNotification } = useNotifications();
	const defaultValues = {
		from: 1,
		to: 100,
	};

	const context = useForm<Inputs>({ defaultValues });
	const {
		register,
		formState: { isSubmitting, errors },
	} = context;

	const handleSubmit: SubmitHandler<Inputs> = async (data) => {
		const response = await fetchScraperHydrate(scraper.id, data, token);
		if (response.statusCode === 200) {
			addNotification({ message: 'Hydratace začala...', timeout: TEN_SECONDS });
		} else {
			addNotification({ message: 'Chybný požadavek, zkontrolujte parametry', timeout: TEN_SECONDS });
		}
	};

	return (
		<Form<Inputs> context={context} onSubmit={handleSubmit}>
			<Title tagName="h3">
				Hydratovat {generateLabel({ realEstateType: scraper.realEstateType, adType: scraper.adType }).toLowerCase()}{' '}
				{scraper.friendlyName}
			</Title>
			<div className={clsx('inp-row', errors.from && 'has-error')}>
				<span className="inp-row__top">
					<Label id="from" required>
						Počáteční stránka
					</Label>
					<Tooltip forInput>
						Od jaké stránky začít hydratovat, hodí se pro případy, že hydratace skončí chybou před koncem.
					</Tooltip>
				</span>
				<span className="inp-fix">
					<input
						type="number"
						id="from"
						className="inp-text"
						{...register('from', {
							required: 'Počáteční stránka je povinná položka',
							min: 1,
							valueAsNumber: true,
						})}
					/>
				</span>
				<FormError name="from" />
			</div>
			<div className={clsx('inp-row', errors.from && 'has-error')}>
				<span className="inp-row__top">
					<Label id="to" required>
						Konečná stránka
					</Label>
					<Tooltip forInput>
						Konečnou stránku zjistěte z{' '}
						<a
							href={scraper.url.replace('%%PAGE%%', scraper.startFromZero ? '0' : '1')}
							target="_blank"
							rel="noopener noreferrer"
						>
							webu realitky
						</a>
					</Tooltip>
				</span>
				<span className="inp-fix">
					<input
						type="number"
						id="to"
						className="inp-text"
						{...register('to', {
							required: 'Konečná stránka je povinná položka',
							min: 1,
							valueAsNumber: true,
						})}
					/>
				</span>
				<FormError name="to" />
			</div>
			<div className="form__bottom-container">
				<p className="btn__wrapper">
					<Button
						type="submit"
						className={clsx('btn-loader', isSubmitting && 'is-loading')}
						disabled={isSubmitting}
						iconAfter="arrow-right"
						text="Hydratovat"
					/>
				</p>
			</div>
		</Form>
	);
};

HydrateForm.displayName = 'HydrateForm';
