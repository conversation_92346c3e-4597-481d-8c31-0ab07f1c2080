import { FC, ReactNode } from 'react';
import { Service } from 'components/ui/boxes/Service';
import { ServiceProvider, ServiceComponent } from 'types/admin';

type Props = {
	title?: ReactNode;
	condensed?: boolean;
	servicesToShow: Readonly<ServiceProvider[]>;
};

export const Services: FC<Props> = ({ title, condensed, servicesToShow }) => {
	let gridSize = 'c-crossroad__item grid__cell grid__cell--eq ';

	gridSize += condensed ? 'size--6-12@md' : 'size--6-12@sm size--4-12@md size--3-12@lg';

	const elementsToShow = servicesToShow.map((service) => servicesComponentsMap[service]);

	return (
		<div className="u-mb-lg">
			{title && <h2>{title}</h2>}
			<div className="c-crossroad__list grid">
				{elementsToShow.map((element, idx) => (
					<div className={gridSize} key={idx}>
						{element}
					</div>
				))}
				<div className={gridSize}>
					<Service
						icon="/img/illust/icon-lifering.svg"
						title="Potřebovali byste pomoc s něčím jiným?"
						desc="Napište nám váš požadavek, zkusíme vám poradit či najít vhodného partnera."
						url="mailto:<EMAIL>?subject=Návrh služby pro Visidoo"
					/>
				</div>
			</div>
		</div>
	);
};

const servicesComponentsMap: Record<ServiceProvider, ServiceComponent> = {
	Fingo: (
		<Service
			img="/img/illust/service-fingo-sm.jpg"
			flag="Financování"
			logo="/img/illust/logo-fingo.svg"
			logoAlt="Fingo"
			logoHeight={49}
			title="Výhodná hypotéka od&nbsp;A&nbsp;do&nbsp;Z"
			desc="Nenechte si utéct tu nejvýhodnější hypotéku jen proto, že se na trhu dokonale neorientujete. Od&nbsp;toho jsou tady odborníci FinGO – pomůžou vám najít i sjednat nejlepší úvěr."
			url="sluzby/fingo"
		/>
	),
	Direct: (
		<Service
			img="/img/illust/service-direct-pronajmy-sm.jpg"
			flag="Pojištění"
			logo="/img/illust/logo-direct.png"
			logoAlt="Direct"
			logoHeight={29}
			title="Pojištění pro nájemníky"
			desc="I když bydlíte v podnájmu nebo v městském či družstevním bytě, pojištění se hodí. Například když něco
									rozbijete nebo vám něco ukradnou."
			url="sluzby/direct-pronajmy"
		/>
	),
	DirectSale: (
		<Service
			img="/img/illust/service-direct-nemovitosti-sm.jpg"
			flag="Pojištění"
			logo="/img/illust/logo-direct.png"
			logoAlt="Direct"
			logoHeight={29}
			title="Pojištění nemovitosti"
			desc="Využijte nabídku pojištění bytu, domu, chaty nebo domácnosti. Bez limitů, jste chránění na maximu."
			url="sluzby/direct-nemovitosti"
		/>
	),
	AGP: (
		<Service
			img="/img/illust/service-agrarni-pudni-fond-sm.jpg"
			flag="Investování"
			logo="/img/illust/logo-agrarni-pudni-fond.png"
			logoAlt="Agrární půdní fond"
			logoHeight={26}
			title="Investujte do pozemků"
			desc="Investovat do zemědělské půdy se vyplatí. Ubráníte se tak inflaci a zhodnotíte své úspory."
			url="sluzby/agrarni-pudni-fond"
		/>
	),
	KontrolaSmluv: (
		<Service
			img="/img/illust/service-kontrola-smluv-sm.jpg"
			flag="Právní služby"
			logo="/img/illust/logo-kontrola-smluv.png"
			logoAlt="Kontrola smluv"
			logoHeight={35}
			title="Kontrola smlouvy online"
			desc="Svůj nový domov máte vybraný. Nechte si poradit přímo od advokátů, jestli to nemá nějaký háček. Upozornění na největší rizika smlouvy máte v e-mailu už další pracovní den. Vše online."
			url="sluzby/kontrola-smluv"
		/>
	),
	KontrolaPozemku: (
		<Service
			img="/img/illust/service-kontrola-pozemku-sm.jpg"
			flag="Pozemky"
			logo="/img/illust/logo-kontrola-pozemku.png"
			logoAlt="Kontrola pozemku"
			logoHeight={49}
			title="Kontrola pozemků v ČR"
			desc="Nákup pozemku je jedním z nejzásadnějších investičních záměrů v životě. Investujte svoje peníze s jistotou na základě předběžné nebo hloubkové analýzy reálného stavu parcely. Vyhotovení do 3 dnů."
			url="sluzby/kontrola-pozemku"
		/>
	),
	Ownest: (
		<Service
			img="/img/illust/service-ownest-sm.jpg"
			flag="Financování"
			logo="/img/illust/logo-ownest.png"
			logoAlt="Ownest"
			logoHeight={36}
			title="Bydlete ve vlastním za&nbsp;cenu nájmu"
			desc="Vysněný byt pro vás koupíme, a&nbsp;zatímco v něm budete bydlet v nájmu, na&nbsp;hypotéku si našetříte."
			url="sluzby/ownest"
		/>
	),
};
