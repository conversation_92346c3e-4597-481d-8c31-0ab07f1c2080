import { useMemo } from 'react';
import { FormattedNumber, useIntl } from 'react-intl';

// Custom hook to generate an intl price-string that can be passed as parameter
export const useFormattedPriceString = (price: number | null) => {
	const intl = useIntl();
	const fractionDigits = assignFractionDigits(price);

	return useMemo(
		() =>
			price
				? intl.formatNumber(price, {
						style: 'currency',
						currency: 'CZK',
						minimumFractionDigits: fractionDigits,
						maximumFractionDigits: fractionDigits,
				  })
				: undefined,
		[fractionDigits, intl, price],
	);
};

// Actual intl price component
export const FormattedPrice = ({ price }: { price: number }) => {
	const fractionDigits = assignFractionDigits(price);

	return (
		<FormattedNumber
			style="currency"
			currency="CZK"
			value={price}
			minimumFractionDigits={fractionDigits}
			maximumFractionDigits={fractionDigits}
		/>
	);
};

const assignFractionDigits = (price: number | null) => {
	if (!price) return;
	const priceHasDecimals = price % 1 != 0;
	return priceHasDecimals ? 1 : 0;
};
