import { FC } from 'react';
import { useIntl, FormattedMessage } from 'react-intl';
import { useFormattedPriceString } from './prices';

export const ReachEstimateFormattedMessage = ({ usersNumber, emailsNumber }: { usersNumber: number; emailsNumber: number }) => {
	const intl = useIntl();

	return (
		<FormattedMessage
			description={'insertionSimplePage-reachEstimate'}
			defaultMessage={'(cca {totalUsers} unikátních uživatelů / cca {totalEmails} otevřených e-mailů)'}
			values={{
				totalUsers: intl.formatNumber(usersNumber),
				totalEmails: intl.formatNumber(emailsNumber),
			}}
		/>
	);
};

type Props = {
	contactEmail?: string;
};

export const InsertionsContactPerson: FC<Props> = ({ contactEmail = '<EMAIL>' }) => {
	return (
		<>
			<FormattedMessage description={'insertionSimplePage-contact'} defaultMessage={'Kontakt pro inzerci:'} />{' '}
			<a href={`mailto:${contactEmail}`}>{contactEmail}</a>
		</>
	);
};

export const DiscountFormattedMessage = ({ percentage }: { percentage: number }) => {
	return (
		<FormattedMessage description={'insertionSimplePage-discount'} defaultMessage={'sleva {amount}%'} values={{ amount: percentage }} />
	);
};

export const PricePerImpressionFormattedMessage = ({ priceToDisplay }: { priceToDisplay: number }) => {
	const formattedPrice = useFormattedPriceString(priceToDisplay);

	return (
		<FormattedMessage
			description={'insertionSimplePage-pricePerImpression'}
			defaultMessage={'({price} / zobrazení)'}
			values={{
				price: formattedPrice,
			}}
		/>
	);
};
