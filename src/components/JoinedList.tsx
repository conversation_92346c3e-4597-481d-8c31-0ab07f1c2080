import { Flag } from 'components/ui/core/Flag';
import { ComponentProps, FC } from 'react';

type Props<T extends FC<any>> = {
	component: T;
	items: ComponentProps<T>[];
	separator: string;
	lastSeparator: string;
};

export const JoinedList = <T extends FC<any>>({ component: Component, items, separator, lastSeparator }: Props<T>) => {
	return (
		<>
			{items.slice(0, -1).map((props) => (
				<>
					<Component {...props} />
					{separator}{' '}
				</>
			))}
			{lastSeparator} <Component {...items[items.length - 1]} />
		</>
	);
};

export const asdf = () => (
	<JoinedList<typeof Flag> component={Flag} items={[{ name: 'actual' }, { name: 'after' }]} separator="," lastSeparator="and" />
);
