import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { EmailInput } from 'components/ui/core/EmailInput';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { Icon } from 'components/ui/core/Icon';
import { Title } from 'components/ui/core/Title';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage, useIntl } from 'react-intl';
import { LoginInputs } from 'types/Auth';
import { formValidationInltMessages } from '../../i18n/commonIntlMessages/formValidation';

type Props = {
	onSubmit: SubmitHandler<LoginInputs>;
	userEmail: string;
};

export const UserLoginForm: FC<Props> = ({ onSubmit, userEmail }) => {
	const context = useForm<LoginInputs>({ defaultValues: { email: userEmail ?? '', password: '' } });
	const { formatMessage } = useIntl();
	const {
		register,
		formState: { errors, isSubmitting },
	} = context;
	const { requiredPasswordIntlString } = formValidationInltMessages;

	return (
		<Form<LoginInputs> onSubmit={onSubmit} context={context}>
			<div className="form__content-icon">
				<div className="form__main-icon">
					<Icon name="dialog-user" />
				</div>
				<Title tagName="h3" heading="h4" margin="xs">
					<FormattedMessage description={'loginForm-heading'} defaultMessage={'Přihlášení k účtu'} />
				</Title>
				<p />
				<div className={clsx('inp-row', errors.email && 'has-error')}>
					<Label id="login-email">
						<FormattedMessage description={'loginForm-email'} defaultMessage={'E\u2011mail'} />
					</Label>
					<span className="inp-fix">
						<EmailInput id="login-email" />
					</span>
					<FormError name="email" />
				</div>

				<p className={clsx('inp-row', errors.password && 'has-error')}>
					<Label id="current-password">
						<FormattedMessage description={'loginForm-password'} defaultMessage={'Heslo'} />
					</Label>
					<span className="inp-fix">
						<input
							id="current-password"
							className="inp-text"
							type="password"
							autoComplete="current-password"
							{...register('password', { required: formatMessage(requiredPasswordIntlString) })}
						/>
					</span>
					<FormError name="password" />
				</p>

				<div className="form__bottom-container u-text-center">
					<p className="btn__wrapper">
						<Button type="submit" disabled={isSubmitting} className={clsx('btn-loader', isSubmitting && 'is-loading')}>
							<FormattedMessage description={'loginForm-button'} defaultMessage={'Přihlásit se'} />
						</Button>
					</p>
				</div>
			</div>
		</Form>
	);
};

UserLoginForm.displayName = 'UserLoginForm';
