import { fetchUserDelete } from 'api/user/fetchUserDelete';
import { Icon } from 'components/ui/core/Icon';
import { ConfirmDialog } from 'components/ui/dialog/ConfirmDialog';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { Tooltip } from 'components/ui/tooltip/Tooltip';
import { useUser } from 'components/user/UserProvider';
import { FC, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';

export const UserDeleteForm: FC = () => {
	const { addNotification } = useNotifications();
	const { logout, token, isPayingUser } = useUser();
	const [dialogOpen, setDialogOpen] = useState(false);
	const intl = useIntl();
	const openDialog = () => setDialogOpen(true);
	const closeDialog = () => setDialogOpen(false);

	const handleDelete = () => {
		fetchUserDelete(token)
			.then(() => {
				addNotification({
					message: intl.formatMessage({
						defaultMessage: 'V<PERSON>š účet byl úspěšně smazán.',
						description: 'userDeleteForm-success',
					}),
				});
				logout();
			})
			.catch((e) => {
				addNotification({ message: e.message });
			});
	};

	const deleteButton = (
		<button onClick={openDialog} className="item-icon btn a" disabled={isPayingUser}>
			<span className="item-icon__icon">
				<Icon name="trashcan"></Icon>
			</span>
			<span className="item-icon__text">
				<FormattedMessage description={'userDeleteForm-deleteAccount'} defaultMessage={'Smazat můj účet'} />
			</span>
		</button>
	);

	return (
		<div className="u-text-center u-mb-md">
			{isPayingUser ? (
				<Tooltip handle={deleteButton}>
					Máte aktivní předplatné, nemůžete smazat svůj účet dokud neskončí aktuální období...
				</Tooltip>
			) : (
				deleteButton
			)}
			<ConfirmDialog
				question={intl.formatMessage({
					description: 'userDeleteForm-confirm',
					defaultMessage: 'Chcete opravdu smazat Váš účet? Přijdete tím o všechny nastavená upozornění.',
				})}
				onConfirm={handleDelete}
				onCancel={closeDialog}
				isOpen={dialogOpen}
				close={closeDialog}
			/>
		</div>
	);
};

UserDeleteForm.displayName = 'UserDeleteForm';
