import { FC, Fragment } from 'react';

type Props = {
	emailClientSuggestions: string[];
	handleSuggestionSelection: (value: string) => void;
};

export const EmailSuggestionsBox: FC<Props> = ({ emailClientSuggestions, handleSuggestionSelection }) => {
	return (
		<div className="email-suggestions">
			<p className="email-suggestions__highlight">Zdá se nám, že v e-mailu máte překlep...</p>
			<p>
				<PERSON><PERSON><PERSON> jste na mysli{' '}
				{emailClientSuggestions.map((suggestion, index) => (
					<Fragment key={suggestion}>
						<button className="email-suggestions__option" onClick={() => handleSuggestionSelection(suggestion)}>
							{suggestion}
						</button>
						{index === emailClientSuggestions.length - 2 ? ' nebo ' : index < emailClientSuggestions.length - 1 ? ', ' : ''}
					</Fragment>
				))}
				?
			</p>
		</div>
	);
};
