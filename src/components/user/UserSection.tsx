import { ItemIcon } from 'components/ui/core/ItemIcon';
import { DropdownMenu } from 'components/ui/dropdown/DropdownMenu';
import { AuthForm, useAuth } from 'components/user/AuthProvider';
import { useUser } from 'components/user/UserProvider';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { DropdownMenuItem } from 'types/dropdown';
import { UserRole, userRoleMap } from 'types/user';

export const UserSection: FC = () => {
	const router = useRouter();
	const {
		loggedIn,
		logout,
		payload: { filters, role },
		isAdmin,
		isPayingUser,
	} = useUser();
	const { openAuthForm } = useAuth();

	const intl = useIntl();

	const MyProfileFormattedMessage = intl.formatMessage({ description: 'user-myProfile', defaultMessage: 'Můj profil' });
	const LoginFormattedMessage = intl.formatMessage({ description: 'user-signedOut', defaultMessage: 'Přihlášení' });

	const handleLogout = async () => {
		await router.push('/');
		logout();
	};

	const openLogin = () => openAuthForm(AuthForm.LOGIN);
	// const openReset = () => openAuthForm(AuthForm.RESET_LOGGED_IN);

	const toggleButtonContent = (
		<ItemIcon
			name="login"
			text={
				loggedIn ? (
					<>
						{MyProfileFormattedMessage}{' '}
						{isPayingUser && <span className="flag flag--tarif">{userRoleMap[role as UserRole]}</span>}
					</>
				) : (
					LoginFormattedMessage
				)
			}
		/>
	);
	const commonDropdownItems: DropdownMenuItem[] = [
		{
			textContent: (
				<FormattedMessage
					defaultMessage={'Moje filtry ({filters})'}
					description={'userSectionMenu-filters'}
					values={{ filters: filters }}
				/>
			),
			href: '/moje-filtry',
		},
		{
			textContent: <FormattedMessage defaultMessage={'Editace profilu'} description={'userSectionMenu-editProfile'} />,
			href: '/muj-profil',
		},
		{
			textContent: <FormattedMessage defaultMessage={'Odhlášení'} description={'userSectionMenu-logOut'} />,
			onClickHandler: handleLogout,
		},
	];
	const dropdownItems: DropdownMenuItem[] = isAdmin
		? [
				{
					textContent: <FormattedMessage defaultMessage={'Administrace'} description={'userSectionMenu-admin'} />,
					href: '/administrace',
				},
				...commonDropdownItems,
		  ]
		: commonDropdownItems;

	return (
		<DropdownMenu
			toggleButtonContent={toggleButtonContent}
			conditionToShowList={loggedIn}
			conditionLogin={true}
			fallbackToggleAction={openLogin}
			items={dropdownItems}
		/>
	);
};

UserSection.displayName = 'UserSection';
