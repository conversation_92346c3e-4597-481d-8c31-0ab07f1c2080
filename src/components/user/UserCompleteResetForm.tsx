import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { EmailInput } from 'components/ui/core/EmailInput';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { Title } from 'components/ui/core/Title';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage, useIntl } from 'react-intl';
import { CompleteResetInputs } from 'types/Auth';
import { formValidationInltMessages } from '../../i18n/commonIntlMessages/formValidation';

type Props = {
	onSubmit: SubmitHandler<CompleteResetInputs>;
};

export const UserCompleteResetForm: FC<Props> = ({ onSubmit }) => {
	const router = useRouter();
	const { formatMessage } = useIntl();
	const { invalidFormIntlString, requiredPasswordIntlString } = formValidationInltMessages;
	const { magic, email } = router.query;
	const actualEmail = email ? (Array.isArray(email) ? email[0] : email) : '';
	const context = useForm<CompleteResetInputs>({
		defaultValues: {
			magic: magic ? (Array.isArray(magic) ? magic[0] : magic) : '',
			email: actualEmail,
			password: '',
		},
	});
	const {
		register,
		formState: { errors, isSubmitting },
	} = context;

	return (
		<Form<CompleteResetInputs> onSubmit={onSubmit} context={context}>
			<Title tagName="h3">
				<FormattedMessage description={'resetForm-changeData'} defaultMessage={'Změna údajů'} />
			</Title>
			<input type="hidden" value={magic} {...register('magic', { required: formatMessage(invalidFormIntlString) })} />

			<div className={clsx('inp-row', errors.email && 'has-error')}>
				<Label id="complete-reset-email">
					<FormattedMessage description={'resetForm-newEmail'} defaultMessage={'Nový e\u2011mail'} />
				</Label>
				<EmailInput id="complete-reset-email" />

				<FormError name="email" />
			</div>

			<p className={clsx('inp-row', errors.password && 'has-error')}>
				<Label id="complete-reset-password">
					<FormattedMessage description={'resetForm-newPassword'} defaultMessage={'Nové heslo'} />
				</Label>
				<input
					type="password"
					id="complete-reset-password"
					className="inp-text"
					{...register('password', { required: formatMessage(requiredPasswordIntlString) })}
				/>
				<FormError name="password" />
			</p>

			<p className="u-mb0 u-text-center">
				<Button type="submit" disabled={isSubmitting}>
					<FormattedMessage description={'resetForm-button'} defaultMessage={'Změnit údaje'} />
				</Button>
			</p>
		</Form>
	);
};

UserCompleteResetForm.displayName = 'UserCompleteResetForm';
