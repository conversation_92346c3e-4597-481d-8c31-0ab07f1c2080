import { fetchPaymentGet } from 'api/payment/fetchPaymentGet';
import { fetchUserPatch } from 'api/user/fetchUserPatch';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Select } from 'components/ui/core/Select';
import { TelephoneInput } from 'components/ui/core/TelephoneInput';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { AuthForm, useAuth } from 'components/user/AuthProvider';
import { useUser } from 'components/user/UserProvider';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { formValidationInltMessages } from 'i18n/commonIntlMessages/formValidation';
import { defaultLocale, localeLanguageMap } from 'i18n/supportedLocales';
import { timezones } from 'i18n/timezones';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage, useIntl } from 'react-intl';
import { EditUserInputs } from 'types/Auth';
import { UserRole, userRoleMap } from 'types/user';
import { ConsentOption } from 'types/userConsents';
import { TEN_SECONDS } from 'utils/constants';
import { CheckBoxOptionsGroup } from './checkBoxOptionsGroup/CheckBoxOptionsGroup';

export const UserEditForm: FC = () => {
	const router = useRouter();
	const { addNotification } = useNotifications();
	const { formatMessage } = useIntl();
	const { commonTelephoneIntlString } = commonInltMessages;
	const { requiredNameIntlString, requiredSurnameIntlString } = formValidationInltMessages;
	const {
		login,
		token,
		payload: { name, surname, locale, timezone, id, email, emailPreferences, telephone, role, roleExpiresAt },
		isPayingUser,
	} = useUser();
	const { openAuthForm } = useAuth();
	const { locale: currentlySelectedLocale } = useRouter();
	const userCurrentTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
	const defaultValues = {
		name: name ?? '',
		surname: surname ?? '',
		emailPreferences: emailPreferences ?? [],
		locale: locale ?? currentlySelectedLocale ?? defaultLocale,
		timezone: timezone ?? userCurrentTimezone,
		telephone: telephone ?? '',
		role: role ?? '',
	};
	const context = useForm<EditUserInputs>({ defaultValues });
	const { register, setValue } = context;
	const [loading, setLoading] = useState(false);

	const handleSubmit: SubmitHandler<EditUserInputs> = (data) => {
		setLoading(true);
		fetchUserPatch(data, token)
			.then((response) => response.data?.token)
			.then(login)
			.catch((e) => {
				addNotification({ message: e.message, timeout: TEN_SECONDS });
			})
			.finally(() => setLoading(false));
	};

	const handlePaymentClick = async () => {
		setLoading(true);
		fetchPaymentGet(token)
			.then((response) => response.data.url)
			.then((url) => router.push(url))
			.catch((e) => {
				addNotification({ message: e.message, timeout: TEN_SECONDS });
			})
			.finally(() => setLoading(false));
	};

	const openReset = () => openAuthForm(AuthForm.RESET_LOGGED_IN);

	useEffect(() => {
		setValue('name', name);
		setValue('surname', surname);
		setValue('emailPreferences', emailPreferences);
		setValue('locale', locale);
		setValue('timezone', timezone);
		setValue('telephone', telephone);
		setValue('role', role);
	}, [name, surname, emailPreferences, setValue, locale, timezone, telephone, role]);

	return (
		<Form<EditUserInputs> context={context} onSubmit={handleSubmit}>
			<div className="b-product c-products__item">
				<div className="b-product__header">
					<div className="b-annot-product">
						<div className="b-annot-product__content">
							<p className="b-annot-product__caption">
								<FormattedMessage description={'userEditForm-user'} defaultMessage={'Uživatel'} />
							</p>
							<h2 className="b-annot-product__title">{name && surname ? `${name} ${surname}` : id}</h2>
						</div>
					</div>
				</div>
				<div className="b-product__content">
					<div className="b-product__content-left">
						<div className="u-mb-sm">
							<div className="grid">
								<div className="grid__cell size--4-12@md u-mb-last-0">
									<p>
										<label htmlFor="name">
											<FormattedMessage description={'userEditForm-name'} defaultMessage={'Jméno'} />
										</label>
										<input
											type="text"
											id="name"
											className="inp-text"
											defaultValue={name}
											{...register('name', { required: formatMessage(requiredNameIntlString), value: name })}
										/>
										<FormError name="name" />
									</p>
								</div>
								<div className="grid__cell size--4-12@md u-mb-last-0">
									<p>
										<label htmlFor="surname">
											<FormattedMessage description={'userEditForm-surname'} defaultMessage={'Příjmení'} />
										</label>
										<input
											type="text"
											id="surname"
											className="inp-text"
											defaultValue={surname}
											{...register('surname', { required: formatMessage(requiredSurnameIntlString), value: surname })}
										/>
										<FormError name="surname" />
									</p>
								</div>
								<div className="grid__cell size--4-12@md u-mb-last-0">
									<p>
										<label htmlFor="telephone">
											<FormattedMessage {...commonTelephoneIntlString} />
										</label>
										<TelephoneInput />
									</p>
								</div>
								<div className="grid__cell size--6-12@md u-mb-last-0">
									<Select
										name={'locale'}
										label={formatMessage({
											defaultMessage: 'Vyberte jazyk, ve kterém chcete přijímat komunikaci',
											description: 'userEditForm-locale',
										})}
										items={Object.entries(localeLanguageMap).map(([locale, language]) => ({
											value: locale,
											label: language,
										}))}
									/>
								</div>
								<div className="grid__cell size--6-12@md u-mb-last-0">
									<Select
										name={'timezone'}
										label={formatMessage({
											defaultMessage: 'Změnit své časové pásmo',
											description: 'userEditForm-timezone',
										})}
										items={timezones.map((timezone) => ({
											value: timezone,
											label: timezone,
										}))}
										allowFilterMode
									/>
								</div>
							</div>
						</div>

						<CheckBoxOptionsGroup
							optionsAvailable={[
								{
									value: ConsentOption.OFFER,
									label: formatMessage({
										defaultMessage: 'Nabídky',
										description: 'userEditForm-emailPreferences-offer-label',
									}),
									tooltip: formatMessage({
										defaultMessage:
											'Nabídky realit na základě vašich filtrů. Zrušením této položky vám budeme muset vypnout všechny vaše filtry.',
										description: 'userEditForm-emailPreferences-offer-tooltip',
									}),
								},
								{
									value: ConsentOption.NEWSLETTER,
									label: formatMessage({
										defaultMessage: 'Newslettery',
										description: 'userEditForm-emailPreferences-newsletter-label',
									}),
									tooltip: formatMessage({
										defaultMessage: 'Občas vám budeme zasílat novinky z Visidoo',
										description: 'userEditForm-emailPreferences-newsletter-tooltip',
									}),
								},
							]}
							name="emailPreferences"
							label={formatMessage({
								defaultMessage: 'Předvolby zasílání e-mailů',
								description: 'userEditForm-emailPreferences-label',
							})}
						/>

						<Button type="submit" disabled={loading}>
							<FormattedMessage description={'userEditForm-editButton'} defaultMessage={'Upravit mé údaje'} />
						</Button>
					</div>
				</div>
				<div className="b-product__middle">
					{isPayingUser ? (
						<div>
							<h2 className="h5">Aktuální tarif</h2>
							<p>
								Váš aktuální tarif je <strong>{userRoleMap[role as UserRole]}</strong> a máte ho aktivní do{' '}
								{new Date(roleExpiresAt).toLocaleString()}.<br />
								<span className="u-color-grey u-font-sm">
									Tento den, také proběhne automatické obnovení, zrušení případně skončí zkušební verze.
									<br /> Více informací či změnu provedete na odkazu „Změnit nebo zrušit tarif“.
								</span>
							</p>
							<p className="u-mb-xs">
								<Button
									onClick={handlePaymentClick}
									disabled={loading}
									className={clsx('btn-loader', loading && 'is-loading')}
									text="Změnit nebo zrušit tarif"
								/>
							</p>
						</div>
					) : (
						<div>
							<p>
								<strong>Nemáte nastavený žádný tarif.</strong> Je potřeba si vybrat jeden z našich tarifů.
							</p>
							<p className="u-mb-xs">
								<Button href="/premium-ucet">Zvolit tarif</Button>
							</p>
						</div>
					)}
				</div>
				<div className="b-product__bottom">
					<p>
						<FormattedMessage
							description={'userEditForm-instructions'}
							defaultMessage={
								'Pro změnu přihlašovacího e-mailu ({userEmail}) nebo hesla vám zašleme e-mail s dalšími instrukcemi:'
							}
							values={{ userEmail: email }}
						/>
						<br />
						<button className="btn a" onClick={openReset}>
							<FormattedMessage
								description={'userEditForm-changeEmailButton'}
								defaultMessage={'Změnit přihlašovací e-mail či heslo'}
							/>
						</button>
					</p>
				</div>
			</div>
		</Form>
	);
};

UserEditForm.displayName = 'UserEditForm';
