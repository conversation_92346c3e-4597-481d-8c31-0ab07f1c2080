import { fetchCompleteRegistration } from 'api/auth/fetchCompleteRegistration';
import { fetchCompleteReset } from 'api/auth/fetchCompleteReset';
import { fetchLogin } from 'api/auth/fetchLogin';
import { fetchRegister } from 'api/auth/fetchRegister';
import { fetchReset } from 'api/auth/fetchReset';
import { ErrorMessage } from 'components/ui/core/ErrorMessage';
import { Icon } from 'components/ui/core/Icon';
import { AuthForm, useAuth } from 'components/user/AuthProvider';
import { UserCompleteRegistrationForm } from 'components/user/UserCompleteRegistrationForm';
import { UserCompleteResetForm } from 'components/user/UserCompleteResetForm';
import { UserLoginForm } from 'components/user/UserLoginForm';
import { useUser } from 'components/user/UserProvider';
import { UserRegisterForm } from 'components/user/UserRegisterForm';
import { UserResetCredentialsForm } from 'components/user/UserResetCredentialsForm';
import { UserResetLoggedInForm } from 'components/user/UserResetLoggedInForm';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { CompleteRegistrationInputs, CompleteResetInputs, LoginInputs, RegisterInputs, ResetInputs } from 'types/Auth';
import { trackEvent } from 'utils/analytics';

type Props = {
	form: AuthForm;
};

export const UserAuthWidget: FC<Props> = ({ form }) => {
	const router = useRouter();
	const { login, payload } = useUser();
	const { openAuthForm, closeAuthForm } = useAuth();
	const [userEmail, setUserEmail] = useState(payload.email);
	const [error, setError] = useState<string>('');

	const handleLoginForm = async (data: LoginInputs) => {
		const response = await fetchLogin(data);
		login(response?.data?.token);
		await router.push('/moje-filtry');
		trackEvent({ category: 'authentication', action: 'login', label: data.email });
		closeAuthForm();
	};

	const handleRegisterForm = async (data: RegisterInputs) => {
		await fetchRegister(data);
		trackEvent({ category: 'authentication', action: 'register_start', label: data.email });
		openAuthForm(AuthForm.SENT);
	};

	const handleResetCredentialsForm = async (data: ResetInputs) => {
		await fetchReset(data);
		trackEvent({ category: 'authentication', action: 'reset_start', label: data.email });
		openAuthForm(AuthForm.SENT);
	};

	const handleCompleteRegistrationForm = async (data: CompleteRegistrationInputs) => {
		const response = await fetchCompleteRegistration(data);
		login(response?.data?.token);
		await router.push('/moje-filtry');
		trackEvent({ category: 'authentication', action: 'register_complete', label: data.magic });
		closeAuthForm();
	};

	const handleCompleteResetForm = async (data: CompleteResetInputs) => {
		const response = await fetchCompleteReset(data);
		login(response?.data?.token);
		await router.push('/moje-filtry');
		trackEvent({ category: 'authentication', action: 'reset_complete', label: data.email });
		closeAuthForm();
	};

	const handleForm =
		<T extends Record<string, string>>(callback: (data: T) => Promise<void>) =>
		async (data: T) => {
			setError('');
			try {
				await callback(data);
			} catch (e) {
				console.error(e);
				// eslint-disable-next-line @typescript-eslint/ban-ts-comment
				// @ts-ignore
				setError(e.message);
			}
		};

	useEffect(() => {
		setUserEmail(payload.email);
	}, [payload]);

	useEffect(() => {
		setError('');
	}, [form]);

	return (
		<div>
			{form === AuthForm.LOGIN && <UserLoginForm onSubmit={handleForm(handleLoginForm)} userEmail={userEmail} />}
			{form === AuthForm.REGISTER && <UserRegisterForm onSubmit={handleForm(handleRegisterForm)} userEmail={userEmail} />}
			{form === AuthForm.RESET && (
				<UserResetCredentialsForm onSubmit={handleForm(handleResetCredentialsForm)} userEmail={userEmail} />
			)}
			{form === AuthForm.RESET_LOGGED_IN && (
				<UserResetLoggedInForm onSubmit={handleForm(handleResetCredentialsForm)} userEmail={userEmail} />
			)}
			{form === AuthForm.COMPLETE_REGISTER && <UserCompleteRegistrationForm onSubmit={handleForm(handleCompleteRegistrationForm)} />}
			{form === AuthForm.COMPLETE_RESET && <UserCompleteResetForm onSubmit={handleForm(handleCompleteResetForm)} />}
			{form === AuthForm.SENT && (
				<p className="u-text-center u-color-success u-mb0">
					<Icon name="highlight-3" /> <br /> <br />
					<FormattedMessage
						description={'userAuthWidget-instructions'}
						defaultMessage={'Zaslali jsme vám e\u2011mail s dalšími instrukcemi'}
					/>
				</p>
			)}

			{error && <ErrorMessage>{error}</ErrorMessage>}

			{[AuthForm.LOGIN, AuthForm.REGISTER, AuthForm.RESET].includes(form) && (
				<p className="u-text-center u-mb-0 u-mt-sm form__content-icon">
					{[AuthForm.REGISTER, AuthForm.RESET].includes(form) && (
						<>
							<button type="button" className="btn a" onClick={() => openAuthForm(AuthForm.LOGIN)}>
								<FormattedMessage description={'userAuthWidget-login'} defaultMessage={'Přihlásit se'} />
							</button>
							{' / '}
						</>
					)}
					{[AuthForm.LOGIN, AuthForm.RESET].includes(form) && (
						<>
							<button type="button" className="btn a" onClick={() => openAuthForm(AuthForm.REGISTER)}>
								<FormattedMessage description={'userAuthWidget-register'} defaultMessage={'Chci se zaregistrovat'} />
							</button>
						</>
					)}
					{[AuthForm.LOGIN].includes(form) && <>{' / '}</>}
					{[AuthForm.REGISTER, AuthForm.LOGIN].includes(form) && (
						<button type="button" className="btn a" onClick={() => openAuthForm(AuthForm.RESET)}>
							<FormattedMessage description={'userAuthWidget-forgotPassword'} defaultMessage={'Zaslat zapomenuté heslo'} />
						</button>
					)}
				</p>
			)}
		</div>
	);
};

UserAuthWidget.displayName = 'UserAuthWidget';
