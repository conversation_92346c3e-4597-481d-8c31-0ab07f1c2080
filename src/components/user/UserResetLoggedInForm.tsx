import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { Title } from 'components/ui/core/Title';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage, useIntl } from 'react-intl';
import { ResetInputs } from 'types/Auth';
import { formValidationInltMessages } from '../../i18n/commonIntlMessages/formValidation';

type Props = {
	onSubmit: SubmitHandler<ResetInputs>;
	userEmail: string;
};

export const UserResetLoggedInForm: FC<Props> = ({ onSubmit, userEmail }) => {
	const context = useForm<ResetInputs>({ defaultValues: { email: userEmail ?? '' } });
	const { formatMessage } = useIntl();
	const { requiredEmailIntlString } = formValidationInltMessages;
	const {
		register,
		formState: { isSubmitting },
	} = context;

	return (
		<Form<ResetInputs> onSubmit={onSubmit} context={context}>
			<input type="hidden" {...register('email', { required: formatMessage(requiredEmailIntlString) })} />
			<Title tagName="h3">
				<FormattedMessage description={'resetLoggedin-changeData'} defaultMessage={'Změna údajů'} />
			</Title>
			<p>
				<FormattedMessage
					description={'resetLoggedin-security'}
					defaultMessage={
						'Z bezpečnostních důvodů na vaši současnou adresu <strong>{email}</strong> pošleme e-mail s unikátním odkazem pro změnu vašich údajů.'
					}
					values={{
						strong: (...chunks: string[]) => <strong>{chunks}</strong>,
						email: userEmail,
					}}
				/>
			</p>
			<p className="u-text-center">
				<FormattedMessage description={'resetLoggedin-continue'} defaultMessage={'Chcete pokračovat?'} />
			</p>

			<div className="form__bottom-container u-text-center">
				<p className="btn__wrapper">
					<Button type="submit" disabled={isSubmitting}>
						<FormattedMessage description={'resetLoggedin-button'} defaultMessage={'Změnit údaje'} />
					</Button>
				</p>
			</div>
		</Form>
	);
};

UserResetLoggedInForm.displayName = 'UserResetLoggedInForm';
