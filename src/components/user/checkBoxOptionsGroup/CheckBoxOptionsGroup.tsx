import clsx from 'clsx';
import { Label } from 'components/ui/core/Label';
import { FC, ReactNode, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { ConsentOption } from 'types/userConsents';
import { CheckBoxOption } from './CheckBoxOption';

type Props = {
	name: string;
	optionsAvailable: {
		value: ConsentOption;
		label: string;
		tooltip?: string;
	}[];
	label: ReactNode;
};

export const CheckBoxOptionsGroup: FC<Props> = ({ name, optionsAvailable, label }) => {
	const { formatMessage } = useIntl();
	const { watch, setValue } = useFormContext();
	const watchAvailableOptions = watch(name);
	const [isAllSelected, setAllSelected] = useState(false);
	const [isNoneSelected, setNoneSelected] = useState(false);

	const setAll = () =>
		setValue(
			name,
			optionsAvailable.map(({ value }) => value),
		);
	const setNone = () => setValue(name, []);
	const toggleSelectAll = () => (isAllSelected ? setNone : setAll)();
	const toggleSelectNone = () => (isNoneSelected ? setAll : setNone)();

	useEffect(() => {
		if (typeof watchAvailableOptions !== 'undefined') {
			setAllSelected(optionsAvailable.every(({ value }) => watchAvailableOptions.includes(value)));
			setNoneSelected(optionsAvailable.every(({ value }) => !watchAvailableOptions.includes(value)));
		}
	}, [optionsAvailable, watchAvailableOptions]);

	return (
		<div className={clsx('inp-row')}>
			<Label>{label}</Label>
			<div className="inp-items">
				<div className="inp-items__list">
					<CheckBoxOption
						label={formatMessage({
							defaultMessage: 'Vše',
							description: 'checkboxOptionsGroup-all-label',
						})}
						isChecked={isAllSelected}
						handleChange={toggleSelectAll}
					/>
					{optionsAvailable.map((item) => (
						<CheckBoxOption key={item.value} name={name} {...item} />
					))}
					<CheckBoxOption
						label={formatMessage({
							defaultMessage: 'Nic',
							description: 'checkboxOptionsGroup-nothing-label',
						})}
						isChecked={isNoneSelected}
						handleChange={toggleSelectNone}
					/>
				</div>
			</div>
		</div>
	);
};
