import { Tooltip } from 'components/ui/tooltip/Tooltip';
import { FC } from 'react';
import { useFormContext } from 'react-hook-form';

type Props = {
	label: string;
	name?: string;
	value?: string;
	tooltip?: string;
	handleChange?: () => void;
	isChecked?: boolean;
};

export const CheckBoxOption: FC<Props> = ({ name, value, label, tooltip, handleChange, isChecked }) => {
	const { register } = useFormContext();

	return (
		<div key={value} className="inp-items__item">
			<div className="inp-row">
				<label className="inp-item inp-item--checkbox">
					<input
						type="checkbox"
						value={value}
						className="inp-item__inp"
						checked={isChecked}
						{...(name ? register(name) : { onChange: handleChange })}
					/>
					<span className="inp-item__text">
						{label}
						{tooltip && <Tooltip>{tooltip}</Tooltip>}
					</span>
				</label>
			</div>
		</div>
	);
};
