import { ErrorMessage } from 'components/ui/core/ErrorMessage';
import { Row } from 'components/ui/core/Row';
import { useUser } from 'components/user/UserProvider';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';

export const IsBlockedNotification: FC = () => {
	const { isBlocked } = useUser();
	if (!isBlocked) return null;

	return (
		<Row>
			<ErrorMessage>
				<FormattedMessage
					description={'blockedNotification'}
					defaultMessage={
						'Váš <PERSON>et jsme bohužel byli nuce<PERSON>, protože se nám nedaří doručit vám e-maily. Pokud k zadanému e-mailu již nem<PERSON>te přístup, napište nám na <a><EMAIL></a> a zkusíme to spolu vyřešit.'
					}
					values={{ a: (...chunks: string[]) => <a href="mailto:<EMAIL>">{chunks}</a> }}
				/>
			</ErrorMessage>
		</Row>
	);
};

IsBlockedNotification.displayName = 'IsBlockedNotification';
