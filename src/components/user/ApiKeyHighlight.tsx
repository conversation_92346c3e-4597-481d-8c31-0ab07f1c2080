import { fetchUserApiKeyGet } from 'api/user/fetchUserApiKeyGet';
import { Link } from 'components/Link';
import { Button } from 'components/ui/core/Button';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { FC, Fragment, useState } from 'react';

export const ApiKeyHighlight: FC = () => {
	const { addNotification } = useNotifications();
	const {
		token,
		payload: { apiAccess },
	} = useUser();
	const [apiKey, setApiKey] = useState('');
	const [loading, setLoading] = useState(false);

	const handleFetch = () => {
		setLoading(true);
		fetchUserApiKeyGet(token)
			.then((response) => {
				setApiKey(response.data.apiKey);
			})
			.catch((e) => {
				addNotification({ message: e.message });
			})
			.finally(() => {
				setLoading(false);
			});
	};

	const handleCopy = async () => {
		setLoading(true);
		await navigator.clipboard.writeText(apiKey);
		setLoading(false);
	};

	return (
		<div className="u-text-center u-mb-md">
			<p>
				Níže najdete svůj soukromý klíč pro přístup k našemu API. <strong>S nikým ho nesdílejte.</strong> <br />
				Nový klíč se automaticky vygeneruje při změně vašeho hesla.
			</p>

			<p>
				Máte přístup k těmto API endpointům: <br />
				{apiAccess.map((value) => (
					<Fragment key={value}>
						{value} <br />
					</Fragment>
				))}
			</p>

			<p>
				<Link href="/docs">Dokumentace</Link>
			</p>

			{apiKey ? (
				<>
					<p style={{ wordBreak: 'break-all', maxWidth: '600px', margin: 'auto' }}>
						<strong>{apiKey}</strong>
					</p>
					<Button disabled={loading} variant="outline" onClick={handleCopy}>
						Zkopírovat do schránky
					</Button>
				</>
			) : (
				<Button disabled={loading} variant="outline" onClick={handleFetch}>
					Zobrazit API klíč
				</Button>
			)}
		</div>
	);
};

ApiKeyHighlight.displayName = 'ApiKeyHighlight';
