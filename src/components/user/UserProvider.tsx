import { fetchUserGet } from 'api/user/fetchUserGet';
import { decode } from 'js-base64';
import { createContext, FC, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { AuthState, UserRole, UserState } from 'types/user';

const UserContext = createContext({
	token: '',
	payload: {
		apiAccess: [],
		email: '',
		emailPreferences: [],
		filters: 0,
		id: '',
		locale: '',
		name: '',
		role: 0,
		roleExpiresAt: '',
		state: '',
		surname: '',
		telephone: '',
		timezone: '',
	},
	login: (token: string | undefined) => void token,
	logout: () => void null,
	refresh: async () => void null,
	userState: AuthState.LOADING,
	loggedIn: false,
	loggedOut: false,
	loading: true,
	isBlocked: false,
	isAdmin: false,
	isAdvanced: false,
	isMini: false,
	isStandard: false,
	isPremium: false,
	isProfi: false,
	isEnterprise: false,
	isPayingUser: false,
});

export const useUser = () => useContext(UserContext);

export const UserProvider: FC = ({ children }) => {
	const [token, setToken] = useState('');
	const [userState, setUserState] = useState<AuthState>(AuthState.LOADING);
	const [fetched, setFetched] = useState(false);
	const payload = useMemo(() => {
		const encodedPayload = token.split('.')[1];
		if (!encodedPayload) return {};

		try {
			return JSON.parse(decode(encodedPayload));
		} catch (e) {
			console.error(e);
			setUserState(AuthState.LOGGED_OUT);
			return {};
		}
	}, [token]);

	const login = useCallback((token: string | undefined) => {
		if (!token) return logout();

		localStorage.setItem('visidoo:jwt', token);
		setToken(token);
		setUserState(AuthState.LOGGED_IN);
		return undefined;
	}, []);

	const logout = () => {
		localStorage.removeItem('visidoo:jwt');
		setToken('');
		setUserState(AuthState.LOGGED_OUT);
		return undefined;
	};

	const refresh = useCallback(async () => {
		try {
			const response = await fetchUserGet(token);
			setFetched(true);
			login(response?.data?.token);
		} catch (e) {
			logout();
		}
		return undefined;
	}, [login, token]);

	useEffect(() => {
		const token = localStorage.getItem('visidoo:jwt');
		login(token ?? '');
	}, [login]);

	useEffect(() => {
		if (userState === AuthState.LOGGED_IN && !fetched) {
			refresh();
		}
	}, [refresh, fetched, userState]);

	return (
		<UserContext.Provider
			value={{
				token,
				payload,
				login,
				logout,
				refresh,
				userState,
				loggedIn: userState === AuthState.LOGGED_IN,
				loggedOut: userState === AuthState.LOGGED_OUT,
				loading: userState === AuthState.LOADING,
				isBlocked: payload.state === UserState.BLOCKED,
				isAdmin: payload.role >= UserRole.ADMIN,
				isAdvanced: payload.role >= UserRole.ADVANCED,
				isMini: payload.role >= UserRole.MINI,
				isStandard: payload.role >= UserRole.STANDARD,
				isPremium: payload.role >= UserRole.PREMIUM,
				isProfi: payload.role >= UserRole.PROFI,
				isEnterprise: payload.role >= UserRole.ENTERPRISE,
				isPayingUser: payload.role >= UserRole.MINI,
			}}
		>
			{children}
		</UserContext.Provider>
	);
};

UserProvider.displayName = 'UserProvider';
