import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { Title } from 'components/ui/core/Title';
import { useRouter } from 'next/router';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage, useIntl } from 'react-intl';
import { CompleteRegistrationInputs } from 'types/Auth';
import { formValidationInltMessages } from '../../i18n/commonIntlMessages/formValidation';
import { commonInltMessages } from '../../i18n/commonIntlMessages/common';
import { TelephoneInput } from 'components/ui/core/TelephoneInput';

type Props = {
	onSubmit: SubmitHandler<CompleteRegistrationInputs>;
};

export const UserCompleteRegistrationForm: FC<Props> = ({ onSubmit }) => {
	const router = useRouter();
	const { formatMessage } = useIntl();
	const { commonTelephoneIntlString } = commonInltMessages;
	const { requiredNameIntlString, requiredSurnameIntlString, requiredPasswordIntlString, invalidFormIntlString } =
		formValidationInltMessages;
	const { magic } = router.query;
	const context = useForm<CompleteRegistrationInputs>({
		defaultValues: {
			magic: magic ? (Array.isArray(magic) ? magic[0] : magic) : '',
			name: '',
			surname: '',
			password: '',
			telephone: '',
		},
	});
	const {
		register,
		formState: { errors, isSubmitting },
	} = context;

	return (
		<Form<CompleteRegistrationInputs> onSubmit={onSubmit} context={context}>
			<Title tagName="h3">
				<FormattedMessage
					description={'registrationForm-endRegistration'}
					defaultMessage={'Dokončete svou registraci'}
					values={{ br: <br /> }}
				/>
			</Title>
			<p>
				<FormattedMessage description={'registrationForm-needData'} defaultMessage={'Potřebujeme už jen pár údajů...'} />
			</p>
			<input type="hidden" {...register('magic', { required: formatMessage(invalidFormIntlString) })} />
			<FormError name="magic" />
			<p className={clsx('inp-row', errors.name && 'has-error')}>
				<Label id="complete-name" required>
					<FormattedMessage description={'registrationForm-name'} defaultMessage={'Jméno'} />
				</Label>
				<input
					type="text"
					id="complete-name"
					className="inp-text"
					autoComplete="given-name"
					{...register('name', { required: formatMessage(requiredNameIntlString) })}
				/>
				<FormError name="name" />
			</p>

			<p className={clsx('inp-row', errors.surname && 'has-error')}>
				<Label id="complete-surname" required>
					<FormattedMessage description={'registrationForm-surname'} defaultMessage={'Příjmení'} />
				</Label>
				<input
					type="text"
					id="complete-surname"
					autoComplete="family-name"
					className="inp-text"
					{...register('surname', { required: formatMessage(requiredSurnameIntlString) })}
				/>
				<FormError name="surname" />
			</p>

			<p className={clsx('inp-row', errors.password && 'has-error')}>
				<Label id="complete-password" required>
					<FormattedMessage description={'registrationForm-password'} defaultMessage={'Heslo'} />
				</Label>
				<input
					type="password"
					id="complete-password"
					className="inp-text"
					autoComplete="new-password"
					{...register('password', { required: formatMessage(requiredPasswordIntlString) })}
				/>
				<FormError name="password" />
			</p>

			<p className={clsx('inp-row')}>
				<Label id="complete-telephone">
					<FormattedMessage {...commonTelephoneIntlString} />
				</Label>
				<TelephoneInput />
			</p>

			<p className="u-mb0 u-text-center">
				<Button type="submit" disabled={isSubmitting}>
					<FormattedMessage description={'registrationForm-button'} defaultMessage={'Registrovat se'} />
				</Button>
			</p>
		</Form>
	);
};

UserCompleteRegistrationForm.displayName = 'UserCompleteRegistrationForm';
