import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { EmailInput } from 'components/ui/core/EmailInput';
import { Form } from 'components/ui/core/Form';
import { Label } from 'components/ui/core/Label';
import { Title } from 'components/ui/core/Title';
import { FC } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
import { ResetInputs } from 'types/Auth';

type Props = {
	onSubmit: SubmitHandler<ResetInputs>;
	userEmail: string;
};

export const UserResetCredentialsForm: FC<Props> = ({ onSubmit, userEmail }) => {
	const context = useForm<ResetInputs>({ defaultValues: { email: userEmail ?? '' } });
	const {
		formState: { errors, isSubmitting },
	} = context;

	return (
		<Form<ResetInputs> onSubmit={onSubmit} context={context}>
			<Title tagName="h3">
				<FormattedMessage description={'userResetCredentials-forgottenData'} defaultMessage={'Zapomenuté údaje'} />
			</Title>
			<p>
				<FormattedMessage
					description={'userResetCredentials-enterEmail'}
					defaultMessage={'Nic se neděje, zadejte váš e\u2011mail, na který vám pošleme další instrukce.'}
				/>
			</p>
			<div className={clsx('inp-row', errors.email && 'has-error')}>
				<Label id="reset-email">
					<FormattedMessage description={'userResetCredentials-email'} defaultMessage={'E\u2011mail'} />
				</Label>
				<EmailInput id="reset-email" />
			</div>

			<div className="form__bottom-container u-text-center">
				<p className="btn__wrapper">
					<Button type="submit" disabled={isSubmitting} className={clsx('btn-loader', isSubmitting && 'is-loading')}>
						<FormattedMessage description={'userResetCredentials-resetPassword'} defaultMessage={'Resetovat heslo'} />
					</Button>
				</p>
			</div>
		</Form>
	);
};

UserResetCredentialsForm.displayName = 'UserResetCredentialsForm';
