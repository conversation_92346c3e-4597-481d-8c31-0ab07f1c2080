import { fetchUser } from 'api/advanced/fetchUser';
import { fetchUserFilters } from 'api/advanced/fetchUserFilters';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Icon } from 'components/ui/core/Icon';
import { FilterBox } from 'components/ui/filter/FilterBox';
import { useUser } from 'components/user/UserProvider';
import { FC, useCallback, useEffect, useState } from 'react';
import { UserPayload } from 'types/admin';
import { Filter } from 'types/filter';
import { UserRole } from 'types/user';

type Props = {
	id: string;
};

export const SimpleUserDetail: FC<Props> = ({ id }) => {
	const { token } = useUser();
	const [loading, setLoading] = useState(false);
	const [user, setUser] = useState<UserPayload | undefined>();
	const [filters, setFilters] = useState<Filter[]>([]);

	const fetchData = useCallback(async () => {
		try {
			const [userResponse, filterResponse] = await Promise.all([fetchUser(token, id), fetchUserFilters(token, id)]);
			setUser(userResponse.data.user);
			setFilters(filterResponse.data.filters);
		} catch (e) {
			console.error(e);
		}
		setLoading(false);
	}, [token, id]);

	useEffect(() => {
		setLoading(true);
		fetchData();
	}, [fetchData, id]);

	return (
		<BlockLoader loading={loading}>
			{user && (
				<>
					<div className="b-product">
						<div className="b-product__header">
							<div className="b-annot-product">
								<div className="b-annot-product__content">
									<p className="b-annot-product__caption">Uživatel</p>
									<h2 className="b-annot-product__title">
										{user.name} {user.surname}
									</h2>
								</div>
							</div>
						</div>
						<div className="b-product__content">
							<div className="b-product__content-left">
								<div className="b-product__description">
									<div className="b-description">
										<ul className="b-description__list">
											<li className="b-description__item">
												<span className="u-font-medium">E-mail:</span>{' '}
												<a href={`mailto:${user.email}`}>{user.email}</a>
											</li>
											{user.telephone && (
												<li className="b-description__item">
													<span className="u-font-medium">Telefon:</span>{' '}
													<a href={`tel:${user.telephone.replace(/\s/g, '')}`}>{user.telephone}</a>
												</li>
											)}
										</ul>
									</div>
								</div>
							</div>
						</div>
						<div className="b-product__bottom">
							<div className="b-product__bottom-left">
								<span className="flag flag--purple">
									<span className="flag__inner">
										<span className="flag__icon">
											<Icon name="tip" />
										</span>
										<span className="flag__text">{user.state}</span>
									</span>
								</span>
								<span className="flag flag--green">
									<span className="flag__inner">
										<span className="flag__icon">
											<Icon name="tip" />
										</span>
										<span className="flag__text">{UserRole[user.role]}</span>
									</span>
								</span>
							</div>
						</div>
					</div>

					<hr className="u-mb-md u-mt-md" />

					{filters.map((filter) => (
						<div className="c-products__item" key={filter.id}>
							<FilterBox {...filter} />
						</div>
					))}
				</>
			)}
		</BlockLoader>
	);
};

SimpleUserDetail.displayName = 'SimpleUserDetail';
