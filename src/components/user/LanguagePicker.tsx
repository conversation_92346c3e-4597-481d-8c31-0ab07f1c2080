// import { Icon } from 'components/ui/core/Icon';
import clsx from 'clsx';
import { Link } from 'components/Link';
import { ItemIcon } from 'components/ui/core/ItemIcon';
import { Locale } from 'i18n/supportedLocales';
import { useRouter } from 'next/router';
// import { DropdownMenu } from 'components/ui/dropdown/DropdownMenu';
// import { localeLanguageMap, SupportedLocale } from 'i18n/supportedLocales';
// import { useRouter } from 'next/router';
import { FC } from 'react';
// import { DropdownMenuItem } from 'types/dropdown';

export const LanguagePicker: FC = () => {
	const { locale, route, asPath, isReady } = useRouter();
	if (!isReady)
		return (
			<li className={clsx('m-main__item', 'm-main__item--flag')}>
				<a href="" className="m-main__link">
					{locale === Locale.cs ? <ItemIcon name="en" text="English" /> : <ItemIcon name="cz" text="Česky" />}
				</a>
			</li>
		);
	// const locales = Object.keys(localeLanguageMap);
	// const { route, asPath, isReady } = useRouter();

	// const dropdownItems: DropdownMenuItem[] = isReady
	// 	? locales.map((locale) => ({
	// 			href: route,
	// 			as: asPath,
	// 			locale: locale as SupportedLocale,
	// 			textContent: localeLanguageMap[locale as keyof typeof localeLanguageMap],
	// 	  }))
	// 	: [];

	// return <DropdownMenu toggleButtonContent={<Icon name="language" />} items={dropdownItems} />;

	return (
		<li className={clsx('m-main__item', 'm-main__item--flag')}>
			<Link
				href={route}
				as={asPath}
				locale={locale === Locale.cs ? Locale.en : Locale.cs}
				Element={({ className, ...props }) => <a {...props} className={clsx('m-main__link', className)} />}
			>
				{locale === Locale.cs ? <ItemIcon name="en" text="English" /> : <ItemIcon name="cz" text="Česky" />}
			</Link>
		</li>
	);
};
