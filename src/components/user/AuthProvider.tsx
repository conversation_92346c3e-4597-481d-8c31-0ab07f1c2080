import { Dialog } from 'components/ui/dialog/Dialog';
import { UserAuthWidget } from 'components/user/UserAuthWidget';
import { useUser } from 'components/user/UserProvider';
import { useRouter } from 'next/router';
import { createContext, FC, useCallback, useContext, useEffect, useState } from 'react';
import { filterQueryString } from 'utils/filterQueryString';

const AuthContext = createContext({
	openAuthForm: (form: AuthForm) => void form,
	closeAuthForm: () => void null,
});

export const useAuth = () => useContext(AuthContext);

export enum AuthForm {
	LOGIN,
	REGISTER,
	RESET,
	RESET_LOGGED_IN,
	COMPLETE_REGISTER,
	COMPLETE_RESET,
	SENT,
}

export const AuthProvider: FC = ({ children }) => {
	const router = useRouter();
	const { loading, loggedIn } = useUser();
	const { magic, email, login } = router.query;

	const [isModalOpen, setModalOpen] = useState(false);
	const [form, setForm] = useState<AuthForm>(AuthForm.LOGIN);

	const closeAuthForm = useCallback(() => {
		setModalOpen(false);
		const newPath = filterQueryString(router.asPath, ['magic', 'email', 'login']);
		if (router.asPath !== newPath) {
			router.replace(router.route, newPath);
		}

		return undefined;
	}, [router]);

	const openAuthForm = useCallback(
		(form: AuthForm) => {
			if (form === AuthForm.SENT) {
				const newPath = filterQueryString(router.asPath, ['magic', 'email']);
				if (router.asPath !== newPath) {
					router.replace(router.route, newPath);
				}
			}
			setForm(form);
			setModalOpen(true);

			return undefined;
		},
		[router],
	);

	useEffect(() => {
		if (loading) return;

		if (loggedIn && magic && email) {
			openAuthForm(AuthForm.COMPLETE_RESET);
		} else {
			if (magic) {
				if (email) {
					openAuthForm(AuthForm.COMPLETE_RESET);
				} else {
					openAuthForm(AuthForm.COMPLETE_REGISTER);
				}
			} else if (typeof login !== 'undefined') {
				openAuthForm(AuthForm.LOGIN);
			}
		}
	}, [loggedIn, magic, email, openAuthForm, loading, login]);

	return (
		<AuthContext.Provider value={{ openAuthForm, closeAuthForm }}>
			{children}
			{!loading && (
				<Dialog isOpen={isModalOpen} close={closeAuthForm}>
					<UserAuthWidget form={form} />
				</Dialog>
			)}
		</AuthContext.Provider>
	);
};

AuthProvider.displayName = 'AuthProvider';
