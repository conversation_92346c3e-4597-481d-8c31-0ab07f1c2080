import { useRouter } from 'next/router';
import { FC } from 'react';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { Title } from 'components/ui/core/Title';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
import { RegisterInputs } from 'types/Auth';
import { EmailInput } from 'components/ui/core/EmailInput';

type Props = {
	onSubmit: SubmitHandler<RegisterInputs>;
	userEmail: string;
};

export const UserRegisterForm: FC<Props> = ({ onSubmit, userEmail }) => {
	const { locale } = useRouter();
	const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
	const context = useForm<RegisterInputs>({
		defaultValues: {
			email: userEmail ?? '',
			timezone,
			locale,
		},
	});
	const {
		formState: { errors, isSubmitting },
	} = context;

	return (
		<Form<RegisterInputs> onSubmit={onSubmit} context={context}>
			<Title tagName="h3">
				<FormattedMessage description={'registerForm-heading'} defaultMessage={'Registrace'} />
			</Title>
			<p>
				<FormattedMessage
					description={'registerForm-instructions'}
					defaultMessage={'Zadejte váš e\u2011mail, na který vám pošleme další instrukce.'}
				/>
			</p>
			<div className={clsx('inp-row', errors.email && 'has-error')}>
				<Label id="register-email">
					<FormattedMessage description={'registerForm-email'} defaultMessage={'E\u2011mail'} />
				</Label>
				<EmailInput needsSuggestions={true} id="register-email" />
				<FormError name="email" />
			</div>

			<div className="form__bottom-container u-text-center">
				<p className="btn__wrapper">
					<Button type="submit" disabled={isSubmitting} className={clsx('btn-loader', isSubmitting && 'is-loading')}>
						<FormattedMessage description={'registerForm-button'} defaultMessage={'Registrovat se'} />
					</Button>
				</p>
			</div>
		</Form>
	);
};

UserRegisterForm.displayName = 'UserRegisterForm';
