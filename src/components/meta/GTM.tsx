import { FC } from 'react';

export const GTM: FC = () => {
	if (process.env.NEXT_PUBLIC_DEVELOPMENT === 'true' || !process.env.NEXT_PUBLIC_GTM_TRACKING_ID) return <script />;

	return (
		<script
			dangerouslySetInnerHTML={{
				__html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
					new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
					j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
					'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
					})(window,document,'script','dataLayer','${process.env.NEXT_PUBLIC_GTM_TRACKING_ID}');
				`,
			}}
		/>
	);
};

GTM.displayName = 'GTM';

export const GTMIframe: FC = () => {
	if (process.env.NEXT_PUBLIC_DEVELOPMENT === 'true' || !process.env.NEXT_PUBLIC_GTM_TRACKING_ID) return null;

	return (
		<noscript>
			<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-57DCQTG" height="0" width="0" className="u-hide u-out"></iframe>
		</noscript>
	);
};

GTM.displayName = 'GTMIframe';
