import { FC } from 'react';

export const Cookie: FC = () => {
	return (
		<script
			dangerouslySetInnerHTML={{
				__html: `
								(function () {
 									var match = document.cookie.match(new RegExp('(^| )SKcookieConsent=([^;]+)'));
									document.documentElement.dataset.showCookie = true;
									if (match) {
										try {
										var cookieState = JSON.parse(match[2]);
										if (cookieState.id && cookieState.datetime && cookieState.storages) {
											document.documentElement.dataset.showCookie = false;
											window.cookieState = cookieState;
										}
										} catch (error) {}
									}
									
									window.dataLayer = window.dataLayer || [];
									function gtag() {
										window.dataLayer.push(arguments);
									}

									gtag('consent', 'default', window.cookieState && window.cookieState.storages ?  window.cookieState.storages : {
										functionality_storage: 'granted',
										security_storage: 'granted',
										ad_storage: 'denied',
										analytics_storage: 'denied',
										personalization_storage: 'denied',
									});
								})();
							`,
			}}
		/>
	);
};
