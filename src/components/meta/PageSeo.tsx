import { useRouter } from 'next/router';
import { FC } from 'react';
import { SITE_NAME } from 'utils/constants';
import { getBaseUrl } from 'utils/getBaseUrl';
import Head from 'next/head';

type Props = {
	title?: string;
	description?: string;
	image?: string;
	keywords?: string[];
};

export const PageSeo: FC<Props> = ({ title, description, image, keywords, children }) => {
	const router = useRouter();
	const fullTitle = title ? `${title} | ${SITE_NAME}` : SITE_NAME;
	const fullImage = image ? `${getBaseUrl()}/${image.startsWith('/') ? image.substring(1) : image}` : undefined;
	const isAdministration = router.route.startsWith('/administrace');

	return (
		<Head>
			<title key="title">{fullTitle}</title>
			<meta key="og:title" name="og:title" content={fullTitle} />
			<meta key="twitter:title" name="twitter:title" content={fullTitle} />

			<meta key="og:image" name="og:image" content={fullImage ?? `${getBaseUrl()}/img/illust/social/facebook.png`} />
			<meta
				key="og:image-property"
				name="image"
				property="og:image"
				content={fullImage ?? `${getBaseUrl()}/img/illust/social/facebook.png`}
			/>
			<meta key="twitter:image" name="twitter:image" content={fullImage ?? `${getBaseUrl()}/img/illust/social/twitter.png`} />

			<meta key="og:locale" name="og:locale" content="cs_CZ" />
			<meta key="og:site_name" name="og:site_name" content={SITE_NAME} />
			<meta key="og:type" name="og:type" content="website" />
			{router && router.asPath && <meta key="og:url" name="og:url" content={`${getBaseUrl()}${router.asPath}`} />}

			{description && (
				<>
					<meta key="description" name="description" content={description} />
					<meta key="og:description" name="og:description" content={description} />
					<meta key="twitter:description" name="twitter:description" content={description} />
				</>
			)}

			{keywords && <meta key="keywords" name="keywords" content={keywords.join(', ')} />}

			<meta
				key="robots"
				name="robots"
				content={process.env.NEXT_PUBLIC_DEVELOPMENT === 'true' || isAdministration ? 'noindex, nofollow' : 'index, follow'}
			/>

			{children}
		</Head>
	);
};

PageSeo.displayName = 'PageSeo';
