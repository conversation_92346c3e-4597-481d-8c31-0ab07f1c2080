import { FC } from 'react';

export const UserAgentScript: FC = () => (
	<script
		dangerouslySetInnerHTML={{
			__html: `
				(function () {
					var className = document.documentElement.className;
					className = className.replace('no-js', 'js');
					(function() {
						var mediaHover = window.matchMedia('(hover: none), (pointer: coarse), (pointer: none)');
						mediaHover.addListener(function(media) {
							document.documentElement.classList[media.matches ? 'add' : 'remove']('no-hoverevents');
							document.documentElement.classList[!media.matches ? 'add' : 'remove']('hoverevents');
						});
						className += (mediaHover.matches ? ' no-hoverevents' : ' hoverevents');
					})();

					var ua = navigator.userAgent.toLowerCase();
					var isIOS = /ipad|iphone|ipod/.test(ua) && !window.MSStream;
					if (isIOS === true) {
						var viewportTag = document.querySelector("meta[name=viewport]");
						viewportTag.setAttribute("content", "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no");
					}
					document.documentElement.className = className;
				}());
			`,
		}}
	/>
);

UserAgentScript.displayName = 'UserAgentScript';
