import { Dialog } from 'components/ui/dialog/Dialog';
import Image from 'next/image';
import { ComponentProps, FC } from 'react';

type Props = ComponentProps<typeof Dialog>;

export const PremiumStatsBasicDialog: FC<Props> = (props) => (
	<Dialog className="b-dialog--lg" {...props}>
		<h2 className="h3">Vyhodnocení výhodnosti ceny</h2>
		<p>V každém e-mailu vám poskytneme hodnocení výhodnosti ceny. Toto hodnocení najdete přímo pod uvedenou cenou nemovitosti.</p>
		<p>
			<Image
				src="/img/illust/screens/evaluations.jpg"
				alt=""
				width="684"
				height="1404"
				loading="lazy"
				quality="100"
				lazyBoundary="1000px"
			/>
		</p>
	</Dialog>
);

PremiumStatsBasicDialog.displayName = 'PremiumStatsBasicDialog';
