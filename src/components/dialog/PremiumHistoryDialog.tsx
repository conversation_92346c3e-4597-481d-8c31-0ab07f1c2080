import { Dialog } from 'components/ui/dialog/Dialog';
import Image from 'next/image';
import { ComponentProps, FC } from 'react';

type Props = ComponentProps<typeof Dialog>;

export const PremiumHistoryDialog: FC<Props> = (props) => (
	<Dialog className="b-dialog--lg" {...props}>
		<h2 className="h3">Listování nabíd<PERSON></h2>
		<p>
			S tímto vylepšením budete mít možnost prohlížet a sledovat historii inzerátů pro vaše vybrané filtry. Budete mít přehled o
			předchozích nabídkách a jednoduše se k nim budete moci vrátit.
		</p>
		<p>
			<Image
				src="/img/illust/screens/history.jpg?"
				alt=""
				width="700"
				height="2557"
				loading="lazy"
				quality="100"
				lazyBoundary="1000px"
			/>
		</p>
	</Dialog>
);

PremiumHistoryDialog.displayName = 'PremiumHistoryDialog';
