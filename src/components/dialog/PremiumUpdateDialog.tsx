import { Dialog } from 'components/ui/dialog/Dialog';
import Image from 'next/image';
import { ComponentProps, FC } from 'react';

type Props = ComponentProps<typeof Dialog>;

export const PremiumUpdateDialog: FC<Props> = (props) => (
	<Dialog className="b-dialog--lg" {...props}>
		<h2 className="h3">Upozornění na změnu ceny</h2>
		<p>
			Okamžitě vás informujeme o jakékoli změně ceny u inzerátu. Tato informace je velmi cenná, zejména pokud máte zájem o jednání o
			ceně.
		</p>
		<p>
			<Image
				src="/img/illust/screens/price-update.jpg"
				alt=""
				width="700"
				height="1415"
				loading="lazy"
				quality="100"
				lazyBoundary="1000px"
			/>
		</p>
	</Dialog>
);

PremiumUpdateDialog.displayName = 'PremiumUpdateDialog';
