import { Dialog } from 'components/ui/dialog/Dialog';
import Image from 'next/image';
import { ComponentProps, FC } from 'react';

type Props = ComponentProps<typeof Dialog>;

export const PremiumMobileAppDialog: FC<Props> = (props) => (
	<Dialog className="b-dialog--lg" {...props}>
		<h2 className="h3">Mobilní aplikace</h2>
		<p>
			Získáte všechna upozornění na jednom místě, rychle a přehledně. Zapomeňte na záplavu e-mailových notifikací, s naší mobilní
			aplikací budete mít všechny důležité informace na dosah ruky.
		</p>
		<p className="grid grid--center u-text-center">
			<span className="grid__cell size--auto@md">
				<Image
					src="/img/illust/screens/mobile-app.jpg"
					alt=""
					width="300"
					height="605"
					loading="lazy"
					quality="100"
					lazyBoundary="1000px"
				/>
			</span>
			<span className="grid__cell size--auto@md">
				<Image
					src="/img/illust/screens/mobile-app2.jpg"
					alt=""
					width="300"
					height="605"
					loading="lazy"
					quality="100"
					lazyBoundary="1000px"
				/>
			</span>
		</p>
	</Dialog>
);

PremiumMobileAppDialog.displayName = 'PremiumMobileAppDialog';
