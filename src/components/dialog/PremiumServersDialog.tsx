import { Dialog } from 'components/ui/dialog/Dialog';
import { ComponentProps, FC } from 'react';
import { ScraperData } from 'types/admin';

type Props = ComponentProps<typeof Dialog> & { scrapers: ScraperData[] };

export const PremiumServersDialog: FC<Props> = ({ scrapers, ...props }) => (
	<Dialog className="b-dialog--lg" {...props}>
		<h2 className="h3">Hl<PERSON><PERSON><PERSON><PERSON> celkem {scrapers.length} realitních serverů</h2>
		{scrapers.length > 0 && (
			<p>
				Konkrétně se jedná o tyto:{' '}
				{scrapers
					.slice(0, -1)
					.map(({ name }) => name)
					.join(', ')}{' '}
				a {scrapers[scrapers.length - 1].name}
			</p>
		)}
	</Dialog>
);

PremiumServersDialog.displayName = 'PremiumServersDialog';
