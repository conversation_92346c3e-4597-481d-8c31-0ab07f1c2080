import { Dialog } from 'components/ui/dialog/Dialog';
import Image from 'next/image';
import { ComponentProps, FC } from 'react';

type Props = ComponentProps<typeof Dialog>;

export const PremiumExtendedFiltersDialog: FC<Props> = (props) => (
	<Dialog className="b-dialog--lg" {...props}>
		<h2 className="h3">Rozšířené možnosti filtrování nabídek</h2>
		<p>
			Získáte tím možnost rozšířit radius lokality, filtrovat dle typu vlastnictví, zdroje inzerátu, či dal<PERSON> parametrů o
			nemovitosti.
		</p>
		<p className="u-text-center">
			<Image
				src="/img/illust/screens/filters-extend.jpg"
				alt=""
				width="503"
				height="1299"
				loading="lazy"
				quality="100"
				lazyBoundary="1000px"
			/>
		</p>
	</Dialog>
);

PremiumExtendedFiltersDialog.displayName = 'PremiumExtendedFiltersDialog';
