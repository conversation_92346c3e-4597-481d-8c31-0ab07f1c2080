import { PremiumDetailDialog } from 'components/dialog/PremiumDetailDialog';
import { PremiumExtendedFiltersDialog } from 'components/dialog/PremiumExtendedFiltersDialog';
import { PremiumFiltersDialog } from 'components/dialog/PremiumFiltersDialog';
import { PremiumFrequencyDialog } from 'components/dialog/PremiumFrequencyDialog';
import { PremiumHistoryDialog } from 'components/dialog/PremiumHistoryDialog';
import { PremiumMobileAppDialog } from 'components/dialog/PremiumMobileAppDialog';
import { PremiumNotificationsDialog } from 'components/dialog/PremiumNotificationsDialog';
import { PremiumServersDialog } from 'components/dialog/PremiumServersDialog';
import { PremiumStatsBasicDialog } from 'components/dialog/PremiumStatsBasicDialog';
import { PremiumStatsDetailDialog } from 'components/dialog/PremiumStatsDetailDialog';
import { PremiumUpdateDialog } from 'components/dialog/PremiumUpdateDialog';
import { createContext, FC, PropsWithChildren, useContext, useState } from 'react';
import { ScraperData } from 'types/admin';

enum PremiumDialog {
	SERVERS,
	NOTIFICATIONS,
	FILTERS,
	DETAIL,
	STATS_BASIC,
	STAtS_DETAIL,
	UPDATE,
	FILTERS_EXTENDED,
	FREQUENCY,
	MOBILE_APP,
	HISTORY,
}

type PremiumDialogsContextValue = {
	close: () => void;
	openNotifications: () => void;
	openServers: () => void;
	openFilters: () => void;
	openDetails: () => void;
	openStatsBasic: () => void;
	openStatsDetail: () => void;
	openHistory: () => void;
	openFrequency: () => void;
	openFiltersExtend: () => void;
	openMobileApp: () => void;
	openPriceUpdate: () => void;
};

type Props = PropsWithChildren<{
	scrapers: ScraperData[];
}>;

const PremiumDialogsContext = createContext<PremiumDialogsContextValue>({
	close: () => void null,
	openNotifications: () => void null,
	openServers: () => void null,
	openFilters: () => void null,
	openDetails: () => void null,
	openStatsBasic: () => void null,
	openStatsDetail: () => void null,
	openHistory: () => void null,
	openFrequency: () => void null,
	openFiltersExtend: () => void null,
	openMobileApp: () => void null,
	openPriceUpdate: () => void null,
});

export const usePremiumDialogs = () => useContext(PremiumDialogsContext);

export const PremiumDialogsProvider: FC<Props> = ({ scrapers, children }) => {
	const [openDialog, setOpenDialog] = useState<PremiumDialog | null>(null);
	const open = (dialog: PremiumDialog) => setOpenDialog(dialog);
	const close = () => setOpenDialog(null);

	const openNotifications = () => open(PremiumDialog.NOTIFICATIONS);
	const openServers = () => open(PremiumDialog.SERVERS);
	const openFilters = () => open(PremiumDialog.FILTERS);
	const openDetails = () => open(PremiumDialog.DETAIL);
	const openStatsBasic = () => open(PremiumDialog.STATS_BASIC);
	const openStatsDetail = () => open(PremiumDialog.STAtS_DETAIL);
	const openHistory = () => open(PremiumDialog.HISTORY);
	const openFrequency = () => open(PremiumDialog.FREQUENCY);
	const openFiltersExtend = () => open(PremiumDialog.FILTERS_EXTENDED);
	const openMobileApp = () => open(PremiumDialog.MOBILE_APP);
	const openPriceUpdate = () => open(PremiumDialog.UPDATE);

	return (
		<PremiumDialogsContext.Provider
			value={{
				close,
				openNotifications,
				openServers,
				openFilters,
				openDetails,
				openStatsBasic,
				openStatsDetail,
				openHistory,
				openFrequency,
				openFiltersExtend,
				openMobileApp,
				openPriceUpdate,
			}}
		>
			{children}

			<PremiumServersDialog isOpen={openDialog === PremiumDialog.SERVERS} close={close} scrapers={scrapers} />
			<PremiumNotificationsDialog isOpen={openDialog === PremiumDialog.NOTIFICATIONS} close={close} />
			<PremiumFiltersDialog isOpen={openDialog === PremiumDialog.FILTERS} close={close} />
			<PremiumDetailDialog isOpen={openDialog === PremiumDialog.DETAIL} close={close} />
			<PremiumStatsBasicDialog isOpen={openDialog === PremiumDialog.STATS_BASIC} close={close} />
			<PremiumStatsDetailDialog isOpen={openDialog === PremiumDialog.STAtS_DETAIL} close={close} />
			<PremiumUpdateDialog isOpen={openDialog === PremiumDialog.UPDATE} close={close} />
			<PremiumExtendedFiltersDialog isOpen={openDialog === PremiumDialog.FILTERS_EXTENDED} close={close} />
			<PremiumFrequencyDialog isOpen={openDialog === PremiumDialog.FREQUENCY} close={close} />
			<PremiumMobileAppDialog isOpen={openDialog === PremiumDialog.MOBILE_APP} close={close} />
			<PremiumHistoryDialog isOpen={openDialog === PremiumDialog.HISTORY} close={close} />
		</PremiumDialogsContext.Provider>
	);
};

PremiumDialogsProvider.displayName = 'PremiumDialogsProvider';
