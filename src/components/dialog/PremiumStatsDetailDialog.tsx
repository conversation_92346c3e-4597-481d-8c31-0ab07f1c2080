import { Dialog } from 'components/ui/dialog/Dialog';
import Image from 'next/image';
import { ComponentProps, FC } from 'react';

type Props = ComponentProps<typeof Dialog>;

export const PremiumStatsDetailDialog: FC<Props> = (props) => (
	<Dialog className="b-dialog--lg" {...props}>
		<h2 className="h3">Detailní vyhodnocení ceny a lokality</h2>
		<p>
			Pro každé vyhodnocení výhodnosti ceny a lokality je možné zobrazit podrobnosti. U ceny získáte informace, na základě kterých
			byla výhodnost vypočítána. U lokality pak můžete prozkoumat informace o okolí, jako je životní prostředí, dostupnost služeb,
			bezpečnost, infrastruktura a další.
		</p>
		<p>
			<Image
				src="/img/illust/screens/evaluations-detail.jpg"
				alt=""
				width="700"
				height="368"
				loading="lazy"
				quality="100"
				lazyBoundary="1000px"
			/>
		</p>
	</Dialog>
);

PremiumStatsDetailDialog.displayName = 'PremiumStatsDialog';
