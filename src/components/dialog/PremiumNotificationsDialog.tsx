import { Dialog } from 'components/ui/dialog/Dialog';
import Image from 'next/image';
import { ComponentProps, FC } from 'react';

type Props = ComponentProps<typeof Dialog>;

export const PremiumNotificationsDialog: FC<Props> = (props) => (
	<Dialog className="b-dialog--lg" {...props}>
		<h2 className="h3">Okamžitá upozornění na nové inzeráty</h2>
		<p>Pošleme vám do e-mailu upozornění ihned po zveřejnění inzerátu, který vyhovuje vašim nastaveným parametrům.</p>
		<p>
			<Image
				src="/img/illust/screens/notifications.jpg"
				alt=""
				width="700"
				height="1248"
				loading="lazy"
				quality="100"
				lazyBoundary="1000px"
			/>
		</p>
	</Dialog>
);

PremiumNotificationsDialog.displayName = 'PremiumNotificationsDialog';
