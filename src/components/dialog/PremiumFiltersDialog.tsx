import { Dialog } from 'components/ui/dialog/Dialog';
import Image from 'next/image';
import { ComponentProps, FC } from 'react';

type Props = ComponentProps<typeof Dialog>;

export const PremiumFiltersDialog: FC<Props> = (props) => (
	<Dialog className="b-dialog--lg" {...props}>
		<h2 className="h3">Počet hlídacích psů</h2>
		<p>
			V každém tarifu je možné nastavit určitý počet hlídacích psů. V tarifu Mini je k dispozici 1 hlídací pes, ve Standardu maximálně
			5 hlídacích psů a v Profi tarifu je počet neomezený.
		</p>
		<p className="u-text-center">
			<Image
				src="/img/illust/screens/filters.jpg"
				alt=""
				width="510"
				height="834"
				loading="lazy"
				quality="100"
				lazyBoundary="1000px"
			/>
		</p>
	</Dialog>
);

PremiumFiltersDialog.displayName = 'PremiumFiltersDialog';
