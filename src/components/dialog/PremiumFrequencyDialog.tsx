import { Dialog } from 'components/ui/dialog/Dialog';
import Image from 'next/image';
import { ComponentProps, FC } from 'react';

type Props = ComponentProps<typeof Dialog>;

export const PremiumFrequencyDialog: FC<Props> = (props) => (
	<Dialog className="b-dialog--lg" {...props}>
		<h2 className="h3">Nastavení frekvence upozornění</h2>
		<p>
			Všichni nemusí preferovat okamžitá upozornění, proto jsme připravili možnost nastavit si denní nebo týdenní upozornění. Tyto
			reporty vám poskytnou všechny nabídky za dané období v jednom souhrnném e-mailu.
		</p>
		<p>
			<Image
				src="/img/illust/screens/frekvency.jpg"
				alt=""
				width="700"
				height="1251"
				loading="lazy"
				quality="100"
				lazyBoundary="1000px"
			/>
		</p>
	</Dialog>
);

PremiumFrequencyDialog.displayName = 'PremiumFrequencyDialog';
