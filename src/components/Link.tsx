import clsx from 'clsx';
import { LocalizedUrls } from 'i18n/exportLocalizedUrls';
import { Locale } from 'i18n/supportedLocales';
import NextLink, { LinkProps } from 'next/link';
import { useRouter } from 'next/router';
import { AnchorHTMLAttributes, FC } from 'react';
import localizedUrls from 'i18n/localizedUrls.json';

export interface Props extends LinkProps, Omit<AnchorHTMLAttributes<HTMLAnchorElement>, 'href' | 'onClick' | 'onMouseEnter'> {
	Element?: FC<AnchorHTMLAttributes<HTMLAnchorElement>>;
}

export const Link: FC<Props> = ({ href, as, locale, Element = (props) => <a {...props} />, className, target, children, ...props }) => {
	const router = useRouter();
	const isActive = router && href === router.route && router.locale === locale;
	const isAnchor = href.toString().startsWith('#');
	if (isActive && !isAnchor) {
		className = clsx(className, 'active');
	}

	if (router.locale && href) {
		const pathItem = (localizedUrls as unknown as LocalizedUrls).find(({ name }) => name === href);
		if (pathItem) {
			href = pathItem.urls[(locale ?? router.locale) as Locale];
			if (as) {
				const hrefParts = href.split('/');
				const asParts = as.toString().split('/');
				as = hrefParts.map((part, index) => (part.startsWith('[') && part.endsWith(']') ? asParts[index] : part)).join('/');
			}
		}
	}

	return (
		<NextLink href={href} as={as} locale={locale} passHref {...props}>
			{Element({ children, className, target })}
		</NextLink>
	);
};

Link.displayName = 'Link';
