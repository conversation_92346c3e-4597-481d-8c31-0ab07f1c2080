import { Link } from 'components/Link';
import Image from 'next/image';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';

type Props = {
	title?: string;
};

export const Workflow: FC<Props> = ({ title = false }) => {
	const Title1FormattedMessage = (
		<FormattedMessage
			description={'workflow title 1'}
			defaultMessage={'Nastavte si realitního hlídacího psa a už jen čekejte na nové nabídky'}
		/>
	);
	const Title2FormattedMessage = (
		<FormattedMessage
			description={'workflow title 2'}
			defaultMessage={'Do 1 minuty vám budou chodit upozornění na nový realitní inzerát'}
		/>
	);
	const Title3FormattedMessage = (
		<FormattedMessage description={'workflow title 3'} defaultMessage={'Získáte tak cenný náskok před ostatními <PERSON>'} />
	);
	const Title4FormattedMessage = (
		<FormattedMessage
			description={'workflow title 4'}
			defaultMessage={'Spravujte své nastavené hlídací psi snadno v\u0020administraci'}
		/>
	);

	return (
		<section className="c-workflow u-mb-xl">
			{title && <h2 className="title title--semicircle u-mb-lg">{title}</h2>}
			<div className="c-workflow__list u-mb-last-0">
				<article className="c-workflow__item">
					<div className="c-workflow__inner">
						<div className="c-workflow__img-wrapper">
							<div className="c-workflow__img">
								<Image src="/img/illust/workflow-1.jpg" alt="" width="300" height="350" layout="fixed" quality="100" />
							</div>
						</div>
						<div className="c-workflow__content u-mb-last-0">
							{title ? (
								<h3 className="c-workflow__title h3">{Title1FormattedMessage}</h3>
							) : (
								<h2 className="c-workflow__title h3">{Title1FormattedMessage}</h2>
							)}
							<p className="c-workflow__annot">
								<FormattedMessage description={'workflow text 1 - fragment1'} defaultMessage={'V '} />{' '}
								<Link href="/">
									<FormattedMessage
										description={'workflow text 1 - fragment2'}
										defaultMessage={' jednoduchém formuláři '}
									/>
								</Link>{' '}
								<FormattedMessage
									defaultMessage={
										'si nastavte požadované parametry pro monitoring realit a zadejte váš e-mail. Visidoo začne sledovat realitní inzeráty 24/7 a hledat pro vás nové nabídky.'
									}
									description={'workflow text 1 - fragment3'}
								/>
							</p>
						</div>
					</div>
				</article>
				<article className="c-workflow__item">
					<div className="c-workflow__inner">
						<div className="c-workflow__img-wrapper">
							<div className="c-workflow__img">
								<Image src="/img/illust/workflow-2.jpg" alt="" width="300" height="350" layout="fixed" quality="100" />
							</div>
						</div>
						<div className="c-workflow__content u-mb-last-0">
							{title ? (
								<h3 className="c-workflow__title h3">{Title2FormattedMessage}</h3>
							) : (
								<h2 className="c-workflow__title h3">{Title2FormattedMessage}</h2>
							)}
							<p className="c-workflow__annot">
								<FormattedMessage
									description={'workflow text 2'}
									defaultMessage={
										'Ano, tak rychle to dokážeme! Sestrojili jsme unikátního hlídacího psa, díky kterému vám posíláme e-mailová upozornění na nové inzeráty téměř okamžitě po jejich zveřejnění.'
									}
								/>
							</p>
						</div>
					</div>
				</article>
				<article className="c-workflow__item">
					<div className="c-workflow__inner">
						<div className="c-workflow__img-wrapper">
							<div className="c-workflow__img">
								<Image src="/img/illust/workflow-3.jpg" alt="" width="300" height="350" layout="fixed" quality="100" />
							</div>
						</div>
						<div className="c-workflow__content u-mb-last-0">
							{title ? (
								<h3 className="c-workflow__title h3">{Title3FormattedMessage}</h3>
							) : (
								<h2 className="c-workflow__title h3">{Title3FormattedMessage}</h2>
							)}
							<p className="c-workflow__annot">
								<FormattedMessage
									description={'workflow text 3'}
									defaultMessage={
										'Díky rychlým informacím o nových nemovitostech získáte jedinečnou možnost zareagovat na inzerát jako první! Nezapomeňte si upravit četnost stahování e\u2011mailů ve vašem e\u2011mailovém klientovi.'
									}
								/>
							</p>
						</div>
					</div>
				</article>
				<article className="c-workflow__item">
					<div className="c-workflow__inner">
						<div className="c-workflow__img-wrapper">
							<div className="c-workflow__img">
								<Image src="/img/illust/workflow-4.jpg" alt="" width="300" height="350" layout="fixed" quality="100" />
							</div>
						</div>
						<div className="c-workflow__content u-mb-last-0">
							{title ? (
								<h3 className="c-workflow__title h3">{Title4FormattedMessage}</h3>
							) : (
								<h2 className="c-workflow__title h3">{Title4FormattedMessage}</h2>
							)}
							<p className="c-workflow__annot">
								<FormattedMessage
									description={'workflow text 4'}
									defaultMessage={
										'Dokončete registraci a získejte možnost si jednoduše spravovat předplatné a hlídací psi nemovitostí v přehledné administraci.'
									}
								/>
							</p>
						</div>
					</div>
				</article>
			</div>
		</section>
	);
};

Workflow.displayName = 'Workflow';
