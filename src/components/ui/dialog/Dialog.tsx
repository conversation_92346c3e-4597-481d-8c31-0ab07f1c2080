import { Dialog as ReachDialog } from '@reach/dialog';
import clsx from 'clsx';
import { Icon } from 'components/ui/core/Icon';
import { FC } from 'react';

type Props = {
	isOpen: boolean;
	close: () => void;
	className?: string;
};

export const Dialog: FC<Props> = ({ isOpen, close, className, children }) => {
	return (
		<ReachDialog isOpen={isOpen} onDismiss={close} className={clsx('b-dialog', className)} aria-label="Dialog">
			<button type="button" className="b-dialog__close" onClick={close}>
				<Icon name="close" />
			</button>
			<div className="b-dialog__content u-mb-last-0">{children}</div>
		</ReachDialog>
	);
};

Dialog.displayName = 'Dialog';
