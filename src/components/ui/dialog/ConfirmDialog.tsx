import { Dialog } from 'components/ui/dialog/Dialog';
import { Button } from 'components/ui/core/Button';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';

type Props = {
	question: string;
	onConfirm: () => void;
	onCancel: () => void;
	isOpen: boolean;
	close: () => void;
};

export const ConfirmDialog: FC<Props> = ({ question, onConfirm, isOpen, close }) => (
	<Dialog isOpen={isOpen} close={close} className="b-dialog--md u-text-center">
		<p>{question}</p>
		<p>
			<span className="grid grid--y-0 grid--center">
				<span className="grid__cell size--auto">
					<Button
						type="button"
						onClick={() => {
							onConfirm();
							close();
						}}
					>
						<FormattedMessage description={'confirmDialogue-confirm'} defaultMessage={'Potvrdit'} />
					</Button>
				</span>
				<span className="grid__cell size--auto">
					<Button type="button" onClick={close} variant="secondary">
						<FormattedMessage description={'confirmDialogue-cancel'} defaultMessage={'Zrušit'} />
					</Button>
				</span>
			</span>
		</p>
	</Dialog>
);

ConfirmDialog.displayName = 'ConfirmDialog';
