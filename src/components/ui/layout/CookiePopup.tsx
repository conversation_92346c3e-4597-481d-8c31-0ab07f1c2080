import { FC, useEffect } from 'react';
import { FormattedMessage } from 'react-intl';

export const CookiePopup: FC = () => {
	useEffect(() => {
		window.SKcookieAPI.init();
	}, []);

	return (
		<div className="b-cookie" data-cookie>
			<div className="b-cookie__bg"></div>
			<div className="b-cookie__box">
				<div className="b-cookie__box-inner">
					<h2 className="h3">
						<FormattedMessage defaultMessage="Tento web používá cookies" description="cookiePopup-title" />
					</h2>
					<p>
						<FormattedMessage
							defaultMessage="Cookies na tomto webu slouží k personalizaci obsahu a reklam, poskytování funkcí sociálních médií a analýze provozu. Kromě toho sdílíme informace o vašem používání webových stránek s našimi partnery v sociáln<PERSON>ch médiích, re<PERSON><PERSON><PERSON> a webové analytice, k<PERSON><PERSON><PERSON> je mohou kombinovat s dal<PERSON><PERSON><PERSON> informacemi, kter<PERSON> jste jim poskytli nebo které shromáždili z vašeho používání jejich služeb."
							description="cookiePopup-explanation"
						/>
					</p>
					<div className="b-cookie__settings" data-step="2">
						<h3 className="h4">
							<FormattedMessage defaultMessage="Jednotlivé souhlasy" description="cookiePopup-agreeTitle" />
						</h3>

						<div className="b-cookie__option">
							<div className="b-cookie__option-head" data-cookie-toggle>
								<span>
									<FormattedMessage
										defaultMessage="<strong>Nezbytné</strong>, aby stránky fungovaly"
										description="cookiePopup-required-label"
										values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
									/>
								</span>
								<label className="inp-item inp-item--checkbox">
									<input type="checkbox" checked disabled className="inp-item__inp" />
									<span className="inp-item__text"></span>
								</label>
							</div>
							<div className="b-cookie__option-body">
								<p>
									<FormattedMessage
										defaultMessage="Nezbytné soubory cookie pomáhají učinit web použitelným povolením základních funkcí, jako je navigace na stránce a přístup do zabezpečených oblastí webu. Bez těchto cookies nemůže web správně fungovat."
										description="cookiePopup-required-description"
									/>
								</p>
							</div>
						</div>

						<div className="b-cookie__option">
							<div className="b-cookie__option-head" data-cookie-toggle>
								<span>
									<FormattedMessage
										defaultMessage="<strong>Předvolby</strong>, abyste si mohli ukládat vaše nastavení"
										description="cookiePopup-settings-label"
										values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
									/>
								</span>
								<label className="inp-item inp-item--checkbox">
									<input type="checkbox" data-cookie-option="personalization_storage" className="inp-item__inp" />
									<span className="inp-item__text"></span>
								</label>
							</div>
							<div className="b-cookie__option-body">
								<p>
									<FormattedMessage
										defaultMessage="Předvolené soubory cookie umožňují webové stránce zapamatovat si informace, které mění způsob, jakým se web chová nebo vypadá, například preferovaný jazyk nebo oblast, ve které se nacházíte."
										description="cookiePopup-settings-description"
									/>
								</p>
							</div>
						</div>

						<div className="b-cookie__option">
							<div className="b-cookie__option-head" data-cookie-toggle>
								<span>
									<FormattedMessage
										defaultMessage="<strong>Analytika</strong>, abychom mohli stránky zlepšovat"
										description="cookiePopup-analytics-label"
										values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
									/>
								</span>
								<label className="inp-item inp-item--checkbox">
									<input type="checkbox" data-cookie-option="analytics_storage" className="inp-item__inp" />
									<span className="inp-item__text"></span>
								</label>
							</div>
							<div className="b-cookie__option-body">
								<p>
									<FormattedMessage
										defaultMessage="Analytické cookies pomáhají majitelům webových stránek porozumět tomu, jak návštěvníci interagují s webovými stránkami, a to anonymním shromažďováním a hlášením informací."
										description="cookiePopup-analytics-description"
									/>
								</p>
							</div>
						</div>

						<div className="b-cookie__option">
							<div className="b-cookie__option-head" data-cookie-toggle>
								<span>
									<FormattedMessage
										defaultMessage="<strong>Marketing</strong>, aby se vám nezobrazovala reklama, která vás nezajímá"
										description="cookiePopup-marketing-label"
										values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
									/>
								</span>
								<label className="inp-item inp-item--checkbox">
									<input type="checkbox" data-cookie-option="ad_storage" className="inp-item__inp" />
									<span className="inp-item__text"></span>
								</label>
							</div>
							<div className="b-cookie__option-body">
								<p>
									<FormattedMessage
										defaultMessage="Marketingové cookies se používají ke sledování návštěvníků napříč webovými stránkami. Záměrem je zobrazovat reklamy, které jsou pro jednotlivého uživatele relevantní a poutavé, a tím cennější pro vydavatele a inzerenty třetích stran."
										description="cookiePopup-marketing-description"
									/>
								</p>
							</div>
						</div>
					</div>
					<div className="b-cookie__btns" data-step="1">
						<p>
							<button className="as-link" type="button" data-cookie-settings>
								<FormattedMessage defaultMessage="Detailně nastavit" description="cookiePopup-button-detail" />
							</button>
						</p>
						<p>
							<button className="btn btn--outline" type="button" data-cookie-reject>
								<span className="btn__text">
									<FormattedMessage defaultMessage="Odmítnout" description="cookiePopup-button-reject" />
								</span>
							</button>
							<button className="btn" type="button" data-cookie-accept>
								<span className="btn__text">
									<FormattedMessage defaultMessage="Souhlasím a pokračovat" description="cookiePopup-button-agree" />
								</span>
							</button>
						</p>
					</div>
					<div className="b-cookie__btns" data-step="2">
						<p>
							<button className="as-link" type="button" data-cookie-save>
								<FormattedMessage defaultMessage="Potvrdit vybrané" description="cookiePopup-button-confirm" />
							</button>
						</p>
						<p>
							<button className="btn" type="button" data-cookie-accept>
								<span className="btn__text">
									<FormattedMessage defaultMessage="Přijmout vše" description="cookiePopup-button-accept" />
								</span>
							</button>
						</p>
					</div>
				</div>
			</div>
		</div>
	);
};

CookiePopup.displayName = 'CookieBar';
