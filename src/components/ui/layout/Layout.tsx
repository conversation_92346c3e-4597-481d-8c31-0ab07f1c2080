import { Footer } from 'components/ui/layout/Footer';
import { Header } from 'components/ui/layout/Header';
import { FC, memo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';

export const Layout: FC = memo(({ children }) => {
	const intl = useIntl();

	return (
		<div className="mother" id="mother">
			<p className="m-accessibility">
				<a
					title={intl.formatMessage({
						description: 'layout-skipToContent',
						defaultMessage: 'Přej<PERSON>t k obsahu (Klávesová zkratka: Alt + 2)',
					})}
					accessKey="2"
					href="#main"
				>
					<FormattedMessage description={'layout-skipToContent2'} defaultMessage={'Přejít k obsahu'} />
				</a>
				<span className="hide">|</span>
				<a href="#menu-main">
					<FormattedMessage description={'layout-toMainMenu'} defaultMessage={'Přejít k hlavnímu menu'} />
				</a>
			</p>
			<Header />
			<main id="main" className="main">
				{children}
			</main>
			<Footer />
		</div>
	);
});

Layout.displayName = 'Layout';
