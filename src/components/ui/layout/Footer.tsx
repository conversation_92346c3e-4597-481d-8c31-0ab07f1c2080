import clsx from 'clsx';
import { Link } from 'components/Link';
import { Row } from 'components/ui/core/Row';
import Image from 'next/image';
import { FC } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Icon } from '../core/Icon';
import { ItemIcon } from '../core/ItemIcon';

type Props = {
	withBg?: boolean;
};

export const Footer: FC<Props> = ({ withBg = false }) => {
	const intl = useIntl();

	return (
		<footer className={clsx('footer', withBg && 'u-bg-orange')}>
			<p className="footer__btn">
				<a href="#top">
					<ItemIcon
						name="arrow-up"
						text={intl.formatMessage({ description: 'button to go up the page', defaultMessage: 'Nahoru' })}
						variant="after"
					/>
				</a>
			</p>
			<Row>
				<div className="footer__inner">
					<div className="grid">
						<div className="footer__menu grid__cell size--6-12@sm size--8-12@md">
							<nav className="m-footer">
								<ul className="m-footer__list u-mb-last-0">
									<li className="m-footer__item">
										<span className="m-footer__title">
											<FormattedMessage description={'footer-header-1'} defaultMessage={'O službě Visidoo'} />
										</span>
										<ul className="m-footer__sublist u-mb-last-0">
											<li className="m-footer__subitem">
												<Link
													href="/casto-kladene-otazky"
													Element={({ className, ...props }) => (
														<a {...props} className={clsx('m-footer__sublink', className)} />
													)}
												>
													<FormattedMessage
														description={'footer-link-FAQ'}
														defaultMessage={'Často kladené otázky'}
													/>
												</Link>
											</li>
											<li className="m-footer__subitem">
												<Link
													href="/jak-to-funguje"
													Element={({ className, ...props }) => (
														<a {...props} className={clsx('m-footer__sublink', className)} />
													)}
												>
													<FormattedMessage
														description={'footer-link-howItWorks'}
														defaultMessage={'Jak to funguje'}
													/>
												</Link>
											</li>
											<li className="m-footer__subitem">
												<Link
													href="/nas-pribeh"
													Element={({ className, ...props }) => (
														<a {...props} className={clsx('m-footer__sublink', className)} />
													)}
												>
													<FormattedMessage description={'footer-link-history'} defaultMessage={'Náš příběh'} />
												</Link>
											</li>
											<li className="m-footer__subitem">
												<Link
													href="/kontakt"
													Element={({ className, ...props }) => (
														<a {...props} className={clsx('m-footer__sublink', className)} />
													)}
												>
													<FormattedMessage description={'footer-link-contacts'} defaultMessage={'Kontakt'} />
												</Link>
											</li>
										</ul>
									</li>
									<li className="m-footer__item">
										<span className="m-footer__title">
											<FormattedMessage description={'footer-header-2'} defaultMessage={'Právničiny'} />
										</span>
										<ul className="m-footer__sublist u-mb-last-0">
											<li className="m-footer__subitem">
												<Link
													href="/zpracovani-osobnich-udaju"
													Element={({ className, ...props }) => (
														<a {...props} className={clsx('m-footer__sublink', className)} />
													)}
												>
													<FormattedMessage
														description={'footer-link-personalData'}
														defaultMessage={'Zpracování osobních údajů'}
													/>
												</Link>
											</li>
											<li className="m-footer__subitem">
												<Link
													href="/cookies"
													Element={({ className, ...props }) => (
														<a {...props} className={clsx('m-footer__sublink', className)} />
													)}
												>
													<FormattedMessage description={'footer-link-cookies'} defaultMessage={'Cookies'} />
												</Link>
											</li>
											<li className="m-footer__subitem">
												<Link
													href="/obchodni-podminky"
													Element={({ className, ...props }) => (
														<a {...props} className={clsx('m-footer__sublink', className)} />
													)}
												>
													<FormattedMessage
														description={'footer-link-t&c'}
														defaultMessage={'Obchodní podmínky'}
													/>
												</Link>
											</li>
										</ul>
									</li>
									{/* <li className="m-footer__item">
										<span className="m-footer__title">
											<FormattedMessage description={'footer-header-3'} defaultMessage={'Sociální sítě'} />
										</span>
										<ul className="m-footer__sublist u-mb-last-0">
											<li className="m-footer__subitem">
												<a
													href="https://www.facebook.com/visidoo"
													className="m-footer__sublink"
													target="_blank"
													rel="noopener noreferrer"
												>
													Facebook
												</a>
											</li>
											<li className="m-footer__subitem">
												<a
													href="https://linkedin.com/company/visidoo"
													className="m-footer__sublink"
													target="_blank"
													rel="noopener noreferrer"
												>
													LinkedIn
												</a>
											</li>
											<li className="m-footer__subitem">
												<a
													href="https://www.instagram.com/visidoo/"
													className="m-footer__sublink"
													target="_blank"
													rel="noopener noreferrer"
												>
													Instagram
												</a>
											</li>
										</ul>
									</li> */}
								</ul>
							</nav>
						</div>
						<div className="footer__contacts u-mb-last-0 grid__cell size--6-12@sm size--4-12@md">
							<p className="footer__title">
								<FormattedMessage description={'footer-header-4'} defaultMessage={'Kontakty'} />
							</p>
							<p className="footer__company">Visidoo s.r.o.</p>
							<p className="footer__contact">
								<span className="footer__contact-item">
									E:&nbsp;
									<a href="mailto:<EMAIL>" className="footer__link">
										<EMAIL>
									</a>
								</span>
							</p>
							<p className="footer__address">
								IČ: 14340119 • DIČ: CZ14340119
								<br />
								Hněvkovského 30/65, 617 00, Brno
							</p>
						</div>
					</div>
					<div className="footer__bottom u-mb-last-0">
						<Icon name="logotype-visidoo" />
						<p className="footer__copyrights">
							<span>©&nbsp;{new Date().getUTCFullYear()} Visidoo • </span>
							<span className="u-text-gray">
								<FormattedMessage description={'footer-disclaimer'} defaultMessage={'\u00A0 Všechna práva vyhrazena.'} />
							</span>
						</p>
						<p className="footer__nvidia">
							<Link href="https://www.nvidia.com/en-us/startups/" target="_blank">
								<Image
									src="/img/illust/logo-nvidia-program.png"
									alt="nVidia – Inception program"
									width={100}
									height={37}
									lazyBoundary="1000px"
								/>
							</Link>
						</p>
					</div>
				</div>
			</Row>
		</footer>
	);
};

Footer.displayName = 'Footer';
