import clsx from 'clsx';
import { Link } from 'components/Link';
import { Row } from 'components/ui/core/Row';
import { UserSection } from 'components/user/UserSection';
import { useClickOutside } from 'hooks/useClickOutside';
import { useRouter } from 'next/router';
import { FC, useEffect, useRef, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { FlagPremiumAlone } from '../core/FlagPremiumAlone';

type Props = {
	onlyLogo?: boolean;
};

export const Header: FC<Props> = ({ onlyLogo = false }) => {
	const menuRef = useRef<HTMLDivElement>(null);
	const [isOpen, setOpen] = useState(false);
	const { pathname } = useRouter();
	const landingPageReg = new RegExp('^/lp-(ppc|fb)');

	const toggleMenu = () => setOpen((current) => !current);
	const handleLinkClick = ({ target }: MouseEvent) => {
		if (target && (target as HTMLElement).tagName === 'A') {
			setOpen(false);
		}
	};

	useEffect(() => {
		const element = menuRef.current;
		element?.addEventListener('click', handleLinkClick);

		return () => {
			element?.addEventListener('click', handleLinkClick);
		};
	}, [menuRef]);

	useClickOutside(menuRef, () => setOpen(false));

	return (
		<header className="header" id="top">
			<Row lg>
				<div className={`header__inner ${onlyLogo && 'header__inner--center'}`}>
					<div className="header__left">
						<div className="header__logo">
							<Link href="/">
								<svg width="124px" height="40px" viewBox="0 0 124 40" xmlns="http://www.w3.org/2000/svg">
									<g fill="#160F24">
										<path
											d="M113.371 8.37023C107.501 8.37023 102.743 13.0978 102.743 18.929C102.743 24.7602 107.501 29.4882 113.371 29.4882C119.241 29.4882 124 24.7602 124 18.929C124 13.0978 119.241 8.37023 113.371 8.37023L113.371 8.37023ZM113.371 24.4365C110.314 24.4365 107.827 21.966 107.827 18.929C107.827 15.8919 110.314 13.421 113.371 13.421C116.428 13.421 118.915 15.8919 118.915 18.929C118.915 21.966 116.428 24.4365 113.371 24.4365L113.371 24.4365ZM10.1231 22.714L5.15898 9.00827L0 9.00827L7.30903 28.6937L12.7802 28.6937L20.1284 9.00827L15.0868 9.00827L10.1231 22.714ZM22.042 28.6937L26.9276 28.6937L26.9276 9.00827L22.042 9.00827L22.042 28.6937ZM48.99 28.6937L53.8761 28.6937L53.8761 9.00827L48.99 9.00827L48.99 28.6937ZM51.4134 0C49.5377 0 48.2474 1.28173 48.2474 3.0286C48.2474 4.77594 49.5377 6.09619 51.4134 6.09619C53.2891 6.09619 54.6186 4.77594 54.6186 3.0286C54.6186 1.28173 53.2891 0 51.4134 0L51.4134 0ZM24.4654 0C22.5892 0 21.2995 1.28173 21.2995 3.0286C21.2995 4.77594 22.5892 6.09619 24.4654 6.09619C26.3412 6.09619 27.6706 4.77594 27.6706 3.0286C27.6706 1.28173 26.3412 0 24.4654 0L24.4654 0ZM72.4655 10.0306C70.9123 9.11633 69.1061 8.58165 67.1699 8.58165C61.4173 8.58165 56.754 13.2143 56.754 18.929C56.754 24.6432 61.4173 29.2763 67.1699 29.2763C72.676 29.2763 77.1728 25.028 77.5488 19.6525L77.5853 19.6525L77.5853 0.570857L72.4655 0.570857L72.4655 10.0306ZM72.4655 19.2757C72.282 22.0318 69.9896 24.225 67.1699 24.225C64.2305 24.225 61.8382 21.849 61.8382 18.929C61.8382 16.0089 64.2305 13.6325 67.1699 13.6325C69.9896 13.6325 72.282 15.8261 72.4655 18.5827L72.4655 19.2757ZM40.0984 16.8133L38.1447 16.3862C35.7605 15.8806 35.1348 15.299 35.1348 14.1718C35.1348 13.0466 36.3465 12.2694 38.0662 12.2694C40.0984 12.2694 41.1144 13.1626 41.1541 14.8714L46.156 14.8714C45.8439 10.7941 42.8733 8.50319 38.1447 8.50319C35.7605 8.50319 33.8455 9.04727 32.36 10.1735C30.9142 11.2612 30.1716 12.7365 30.1716 14.5604C30.1716 17.5505 31.7745 19.2579 36.425 20.3455L38.7699 20.8887C40.841 21.3557 41.506 22.0158 41.506 23.1402C41.506 24.5788 40.2555 25.4311 38.1835 25.4311C35.8773 25.4311 34.6268 24.3448 34.5876 22.3264L29.4683 22.3264C29.6244 26.7904 32.8679 29.1969 38.3395 29.1969C43.342 29.1969 46.5079 26.7518 46.5079 22.714C46.5079 19.5694 44.7887 17.8615 40.0984 16.8133L40.0984 16.8133Z"
											transform="translate(0 1.310059)"
										/>
										<path
											d="M9.64542 6.54865L10.8987 6.62335L10.8987 0L7.31139 0L7.31139 6.62335L8.56567 6.54865C8.92275 6.52422 9.28692 6.52422 9.64542 6.54865L9.64542 6.54865ZM3.4781 7.82192C3.94491 7.5964 4.41928 7.40047 4.88892 7.23932L5.86368 6.90385L3.31493 0.790273L0 2.15328L2.55064 8.27015L3.4781 7.82192ZM14.733 7.82192L15.6604 8.26968L18.2106 2.15375L14.8966 0.790273L12.3474 6.90385L13.3226 7.23885C13.7904 7.4 14.2652 7.59593 14.733 7.82192L14.733 7.82192Z"
											transform="translate(104.266 0)"
											fill="#f58000"
										/>
										<path
											d="M10.6287 0C4.75886 0 0 4.72754 0 10.5587C0 16.3899 4.75886 21.118 10.6287 21.118C16.4985 21.118 21.2574 16.3899 21.2574 10.5587C21.2574 4.72754 16.4985 0 10.6287 0M10.6287 16.0662C7.57152 16.0662 5.08425 13.5958 5.08425 10.5587C5.08425 7.52169 7.57152 5.0508 10.6287 5.0508C13.6859 5.0508 16.1727 7.52169 16.1727 10.5587C16.1727 13.5958 13.6859 16.0662 10.6287 16.0662"
											transform="translate(79.641655 9.680238)"
										/>
										<path
											d="M0.536803 3.75074C3.94538 7.26563 8.51033 9.20185 13.3907 9.20185C18.2702 9.20185 22.8352 7.26563 26.2437 3.75074L26.7805 3.1968L23.4382 0L22.9018 0.553943C20.3763 3.1578 16.9984 4.59129 13.3907 4.59129C9.78211 4.59129 6.4038 3.1578 3.8787 0.553943L3.34237 0L0 3.1968L0.536803 3.75074Z"
											transform="translate(76.88007 30.798155)"
										/>
									</g>
								</svg>
							</Link>
						</div>
					</div>
					{!onlyLogo && (
						<div className="header__right">
							<div className={clsx('site-nav', isOpen && 'menu-is-open')} ref={menuRef}>
								<nav className="m-main js-site-nav-menu" id="menu-main" aria-expanded="false">
									<ul className="m-main__list">
										{!landingPageReg.test(pathname) && (
											<>
												<li className={clsx('m-main__item', pathname === '/' && 'is-active')}>
													<Link
														href="/"
														Element={({ className, ...props }) => (
															<a {...props} className={clsx('m-main__link', className)} />
														)}
													>
														<FormattedMessage
															defaultMessage={'Hledám nemovitost'}
															description={'navbar btn-hledám-nemovitost'}
														/>
													</Link>
												</li>
												<li className={clsx('m-main__item', pathname === '/premium-ucet' && 'is-active')}>
													<Link
														href="/premium-ucet"
														Element={({ className, ...props }) => (
															<a {...props} className={clsx('m-main__link', className)} />
														)}
													>
														<span className="item-icon">
															<span className="item-icon__icon">
																<FlagPremiumAlone />
															</span>
															<span className="item-icon__text">
																<FormattedMessage
																	defaultMessage={'Tarify'}
																	description={'navbar btn-premium-ucet'}
																/>
															</span>
														</span>
													</Link>
												</li>
												<li
													className={clsx(
														'm-main__item',
														pathname.includes('/casto-kladene-otazky') && 'is-active',
													)}
												>
													<Link
														href="/casto-kladene-otazky"
														Element={({ className, ...props }) => (
															<a {...props} className={clsx('m-main__link', className)} />
														)}
													>
														<FormattedMessage defaultMessage={'FAQ'} description={'navbar btn-faq'} />
													</Link>
												</li>
												<li className={clsx('m-main__item', pathname.includes('/kontakt') && 'is-active')}>
													<Link
														href="/kontakt"
														Element={({ className, ...props }) => (
															<a {...props} className={clsx('m-main__link', className)} />
														)}
													>
														<FormattedMessage defaultMessage={'Kontakt'} description={'navbar btn-kontakt'} />
													</Link>
												</li>
											</>
										)}
										<UserSection />
									</ul>
								</nav>
								<button className="js-site-nav-toggle m-main__toggle btn" aria-label="Menu" onClick={toggleMenu}>
									<span className="u-vhide">
										<FormattedMessage defaultMessage={'Menu'} description={'navbar menu'} />
									</span>
								</button>
								<span className="m-main__overlay js-site-nav-toggle" />
							</div>
						</div>
					)}
				</div>
			</Row>
		</header>
	);
};

Header.displayName = 'Header';
