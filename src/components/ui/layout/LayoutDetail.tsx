import { Header } from 'components/ui/layout/Header';
import { FC, memo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';

export const LayoutDetail: FC = memo(({ children }) => {
	const intl = useIntl();

	return (
		<div className="mother mother--detail" id="mother">
			<p className="m-accessibility">
				<a
					title={intl.formatMessage({
						description: 'layout-skipToContent',
						defaultMessage: 'P<PERSON>ej<PERSON>t k obsahu (Klávesová zkratka: Alt + 2)',
					})}
					accessKey="2"
					href="#main"
				>
					<FormattedMessage description={'layout-skipToContent2'} defaultMessage={'Přejít k obsahu'} />
				</a>
				<span className="hide">|</span>
				<a href="#menu-main">
					<FormattedMessage description={'layout-toMainMenu'} defaultMessage={'Přejít k hlavnímu menu'} />
				</a>
			</p>
			<Header onlyLogo={true} />
			<main id="main" className="main">
				{children}
			</main>
		</div>
	);
});

LayoutDetail.displayName = 'LayoutDetail';
