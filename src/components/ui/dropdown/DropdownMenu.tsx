import clsx from 'clsx';
import { useClickOutside } from 'hooks/useClickOutside';
import { FC, ReactNode, useRef, useState } from 'react';
import { DropdownMenuItem } from 'types/dropdown';
import { DropdownItem } from './DropdownItem';

type BaseProps = {
	conditionLogin: boolean;
	items: DropdownMenuItem[];
	toggleButtonContent: ReactNode;
};

type PropsWithoutFallback = BaseProps & {
	conditionToShowList?: never;
	fallbackToggleAction?: never;
};

type PropsWithFallback = BaseProps & {
	conditionToShowList: boolean;
	fallbackToggleAction: () => unknown;
};

export const DropdownMenu: FC<PropsWithFallback | PropsWithoutFallback> = ({
	toggleButtonContent,
	conditionLogin,
	conditionToShowList = true,
	fallbackToggleAction,
	items,
}) => {
	const wrapperRef = useRef<HTMLLIElement>(null);
	const [dropdownVisible, setDropdownVisible] = useState(false);

	const toggleDropdown = () => setDropdownVisible((dropdownVisible) => !dropdownVisible);
	const closeDropdown = () => setDropdownVisible(false);

	useClickOutside(wrapperRef, closeDropdown);

	return (
		<li
			className={clsx(
				'm-main__item',
				conditionLogin && 'm-main__item--login',
				conditionToShowList && 'm-main__item--dropdown',
				dropdownVisible && 'dropdown-is-open',
			)}
			ref={wrapperRef}
		>
			<button type="button" className="m-main__link btn" onClick={conditionToShowList ? toggleDropdown : fallbackToggleAction}>
				{toggleButtonContent}
			</button>
			{conditionToShowList && (
				<ul className="m-main__sub-list">
					{items.map((item, index) => (
						<DropdownItem
							textContent={item.textContent}
							onClickHandler={item.onClickHandler}
							href={item.href}
							as={item.as}
							locale={item.locale}
							closeDropdown={closeDropdown}
							key={index}
						/>
					))}
				</ul>
			)}
		</li>
	);
};

DropdownMenu.displayName = 'DropdownMenu';
