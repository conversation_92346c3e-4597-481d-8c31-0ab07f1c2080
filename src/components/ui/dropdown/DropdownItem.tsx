import clsx from 'clsx';
import { <PERSON> } from 'components/Link';
import { FC } from 'react';
import { DropdownMenuItem } from 'types/dropdown';

type Props = DropdownMenuItem & { closeDropdown: () => void };

export const DropdownItem: FC<Props> = ({ textContent, onClickHandler, href, as, locale, closeDropdown }) => {
	const onClick = () => {
		closeDropdown();
		if (onClickHandler) onClickHandler();
	};

	return (
		<li className="m-main__sub-item">
			{href ? (
				<Link
					href={href}
					as={as}
					locale={locale}
					Element={({ className, ...props }) => (
						<a {...props} className={clsx(`m-main__sub-link`, 'inactive', className)} onClick={onClick} />
					)}
				>
					{textContent}
				</Link>
			) : (
				<a role="button" className="m-main__sub-link inactive" onClick={onClick}>
					{textContent}
				</a>
			)}
		</li>
	);
};

DropdownItem.displayName = 'DropdownItem';
