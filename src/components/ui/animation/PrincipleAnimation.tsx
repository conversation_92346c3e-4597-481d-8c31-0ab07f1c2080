/* eslint-disable @next/next/no-img-element */
// import clsx from 'clsx';
import { FC, useEffect, useRef, useState } from 'react';

export const PrincipleAnimation: FC = () => {
	const [isAnimating, setAnimation] = useState(true);
	const animNode = useRef<null | HTMLDivElement>(null);

	useEffect(() => {
		const element = animNode.current;
		if (!element || !isAnimating) return;

		const onEnd = (event: AnimationEvent) => {
			if (event.target === element && event.animationName === 'opacity-inverse') {
				setAnimation(false);
				setTimeout(() => {
					setAnimation(true);
				}, 100);
			}
		};
		element.addEventListener('animationend', onEnd);
		return () => {
			element.removeEventListener('animationend', onEnd);
		};
	}, [isAnimating, animNode]);

	return (
		<video src={`/video/animation-${Math.floor(Math.random() * 2)}.mp4`} autoPlay loop controls={false} muted playsInline></video>
		// <div className={clsx('b-annot__animation', isAnimating && 'is-playing')}>
		// 	<div className="b-annot__logo b-annot__logo--visidoo">
		// 		<img src="/img/logo-visidoo.svg" alt="Visidoo logo" width="200" height="66" />
		// 	</div>
		// 	<span className="b-annot__notification b-annot__notification--visidoo">
		// 		<div className="b-annot__notification-inner">4</div>
		// 	</span>
		// 	{/* <div className="b-annot__logo b-annot__logo--sreality">
		// 		<img src="/img/illust/logo-sreality.svg" alt="sReality" width="170" height="24" />
		// 	</div>
		// 	<div className="b-annot__notification b-annot__notification--sreality">
		// 		<div className="b-annot__notification-inner">2</div>
		// 	</div> */}
		// 	<div className="b-annot__logo b-annot__logo--bezrealitky">
		// 		<img src="/img/illust/logo-bezrealitky.svg" alt="Bezrealitky" width="150" height="31" />
		// 	</div>
		// 	<div className="b-annot__logo b-annot__logo--idnes">
		// 		<img src="/img/illust/logo-idnes.svg" alt="Reality iDnes" width="100" height="49" />
		// 	</div>
		// 	<div className="b-annot__notification b-annot__notification--idnes">
		// 		<div className="b-annot__notification-inner">1</div>
		// 	</div>
		// 	<div className="b-annot__logo b-annot__logo--mm">
		// 		<img src="/img/illust/logo-mm.png" alt="MM reality" width="90" height="44" />
		// 	</div>
		// 	<div className="b-annot__logo b-annot__logo--patreal">
		// 		<img src="/img/illust/logo-patreal.png" alt="Patreal" width="110" height="47" />
		// 	</div>
		// 	<div className="b-annot__logo b-annot__logo--remax">
		// 		<img src="/img/illust/logo-remax.svg" alt="Remax" width="110" height="39" />
		// 	</div>
		// 	<div className="b-annot__logo b-annot__logo--unicareal">
		// 		<img src="/img/illust/logo-unicareal.png" alt="Unicareal" width="150" height="21" />
		// 	</div>
		// 	<div className="b-annot__notification b-annot__notification--unicareal">
		// 		<div className="b-annot__notification-inner">1</div>
		// 	</div>
		// 	<div className="b-annot__other-wrap">
		// 		<div className="b-annot__other">a další</div>
		// 	</div>
		// 	<div className="b-annot__device">
		// 		<div className="b-annot__device-wrap" ref={animNode}>
		// 			<div className="b-annot__device-inner">
		// 				<img src="/img/illust/animation-1.png" alt="" width="303" height="646" loading="lazy" />
		// 				<div className="b-annot__device-content">
		// 					<img src="/img/illust/animation-2.jpg" alt="" width="303" height="70" loading="lazy" />
		// 					<img src="/img/illust/animation-3.jpg" alt="" width="303" height="70" loading="lazy" />
		// 					<img src="/img/illust/animation-4.jpg" alt="" width="303" height="70" loading="lazy" />
		// 					<img src="/img/illust/animation-5.jpg" alt="" width="303" height="70" loading="lazy" />
		// 				</div>
		// 				<div className="b-annot__device-click">
		// 					<img src="/img/illust/animation-2-active.jpg" alt="" width="303" height="70" loading="lazy" />
		// 				</div>
		// 				<div className="b-annot__device-detail">
		// 					<img src="/img/illust/animation-6.jpg" alt="" width="303" height="646" loading="lazy" />
		// 				</div>
		// 			</div>
		// 		</div>
		// 	</div>

		// 	{/* <div className="b-annot__img">
		// 		<Image
		// 			src="/img/illust/woman.png?t=1625656006975"
		// 			alt="Žena s mobilem a kávou v ruce"
		// 			width="460"
		// 			height="552"
		// 		/>
		// 	</div> */}
		// </div>
	);
};

PrincipleAnimation.displayName = 'PrincipleAnimation';
