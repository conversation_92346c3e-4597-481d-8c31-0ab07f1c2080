import { FC } from 'react';

type Props = {
	onClose: () => void;
};

export const Modal: FC<Props> = ({ onClose, children }) => {
	return (
		<div
			style={{
				width: '100vw',
				height: '100vh',
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				backgroundColor: 'rgba(27, 38, 49, 0.8)',
				position: 'fixed',
				top: 0,
				left: 0,
				zIndex: 1000,
			}}
		>
			<div style={{ position: 'absolute', top: 30, right: 30, color: 'white', cursor: 'pointer' }} onClick={onClose}>
				<h4>&#10006;</h4>
			</div>
			{children}
		</div>
	);
};
