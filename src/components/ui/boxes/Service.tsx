import Image from 'next/image';
import { FC, ReactNode } from 'react';
import { Flag } from 'components/ui/core/Flag';

type Props = {
	img?: string;
	icon?: string;
	flag?: string;
	flagColor?: 'purple' | 'green' | 'semidark' | 'dark' | 'yellow';
	logo?: string;
	logoAlt?: string;
	logoWidth?: number;
	logoHeight?: number;
	title: string;
	url: string;
	desc?: string | ReactNode;
};

export const Service: FC<Props> = ({
	img,
	icon,
	flag,
	flagColor = 'yellow',
	logo,
	logoWidth = 50,
	logoHeight,
	logoAlt,
	title,
	url,
	desc,
}) => {
	return (
		<article className="b-service b-service--purple link-extend u-mb-last-0">
			<div className="b-service__img">
				{img && <Image src={img} alt="" quality="100" layout="fill" lazyBoundary="1000px" />}
				{icon && (
					<div className="b-service__icon">
						<Image src={icon} alt="" quality="100" layout="fill" lazyBoundary="1000px" />
					</div>
				)}
				{flag && (
					<span className="b-service__flags">
						<Flag name={flag} color={flagColor} />
					</span>
				)}
				{logo && (
					<span className="b-service__logo">
						<Image src={logo} layout="fixed" width={logoWidth} height={logoHeight} alt={logoAlt} />
					</span>
				)}
			</div>
			<h2 className="b-service__title h5">
				<a className="b-service__link link-extend__link" href={url}>
					{title}
				</a>
			</h2>
			{desc && <p className="b-service__annot">{desc}</p>}
		</article>
	);
};

Service.displayName = 'Service';
