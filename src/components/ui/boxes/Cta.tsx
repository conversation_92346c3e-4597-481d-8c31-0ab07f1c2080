import clsx from 'clsx';
import { Link } from 'components/Link';
import { Icon } from 'components/ui/core/Icon';
import { Row } from 'components/ui/core/Row';
import Image from 'next/image';
import { FC, ReactNode } from 'react';
import { FormattedMessage } from 'react-intl';
import { EmailShareButton, FacebookShareButton, TwitterShareButton } from 'react-share';
import { SITE_NAME } from 'utils/constants';
import { getBaseUrl } from 'utils/getBaseUrl';
import { Button } from '../core/Button';

type BaseProps = {
	title: string;
	text: string | ReactNode;
};

type ShareProps = BaseProps & {
	share: boolean;
};

type LinkProps = BaseProps & {
	href: string;
	btn: string;
};

type Props = ShareProps | LinkProps;

const isShareProps = (props: Props): props is ShareProps => 'share' in props;
const isLinkProps = (props: Props): props is LinkProps => 'href' in props && 'btn' in props;

export const Cta: FC<Props> = (props) => {
	const shareUrl = getBaseUrl();
	return (
		<section className={clsx('b-cta', isLinkProps(props) && 'u-bg-orange', isShareProps(props) && 'u-bg-gray')}>
			<Row>
				<div className="b-cta__inner">
					<div className="b-cta__content u-mb-last-0">
						<h2>{props.title}</h2>
						<p className="b-cta__text">{props.text}</p>
						{isLinkProps(props) && (
							<p className="btn__wrapper">
								<Link href={props.href} prefetch={false} Element={(props) => <Button {...props} type="button" />}>
									{props.btn}
								</Link>
							</p>
						)}
						{isShareProps(props) && (
							<div className="b-share b-share--color">
								<ul className="b-share__list">
									<li className="b-share__item">
										<FacebookShareButton url={shareUrl}>
											<a href={shareUrl} className="b-share__link">
												<Icon name="facebook" />
												<span className="u-vhide">Facebook</span>
											</a>
										</FacebookShareButton>
									</li>
									<li className="b-share__item">
										<TwitterShareButton url={shareUrl}>
											<a href={shareUrl} className="b-share__link">
												<Icon name="twitter" />
												<span className="u-vhide">Twitter</span>
											</a>
										</TwitterShareButton>
									</li>
									<li className="b-share__item">
										<EmailShareButton url={shareUrl} subject={SITE_NAME}>
											<a href={shareUrl} className="b-share__link">
												<Icon name="share" />
												<span className="u-vhide">
													<FormattedMessage defaultMessage={'Sdílet'} description={'share-button'} />
												</span>
											</a>
										</EmailShareButton>
									</li>
								</ul>
							</div>
						)}
					</div>
					<div className="b-cta__img">
						<Image
							src="/img/illust/graphics-2.png"
							alt=""
							width="300"
							height="320"
							loading="lazy"
							quality="100"
							lazyBoundary="1000px"
						/>
					</div>
				</div>
			</Row>
		</section>
	);
};

Cta.displayName = 'Cta';
