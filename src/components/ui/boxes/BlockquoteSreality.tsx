import { Icon } from 'components/ui/core/Icon';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';

export const BlockquoteSreality: FC = () => {
	return (
		<article className="blockquote blockquote--color blockquote--lp u-animate-2">
			<p className="blockquote__text u-mb-0">
				<strong className="blockquote__title">
					&hellip;
					<FormattedMessage defaultMessage="na Srealitách nejsou všechny nabídky?" description="blockquoteSreality-title" />
				</strong>
				<FormattedMessage
					defaultMessage="Neztrácejte čas procházením mnoha různých serverů."
					description="blockquoteSreality-description"
				/>
			</p>
			<span className="blockquote__quotes">
				<Icon name="quotes" />
			</span>
		</article>
	);
};

BlockquoteSreality.displayName = 'BlockquoteSreality';
