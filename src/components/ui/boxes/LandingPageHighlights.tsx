import { Icon } from 'components/ui/core/Icon';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import { formatNumber } from 'utils/formats';

type Props = {
	count: number;
};

export const LandingPageHighlights: FC<Props> = ({ count }) => {
	return (
		<section className="b-highlights b-highlights--row u-mb-xl">
			<h2 className="title title--semicircle h3 u-mb-sm u-color-default">
				<FormattedMessage
					defaultMessage="Visidoo je pro vás <strong>zdarma</strong>, bez registrace {br}a bez otravného spamu"
					description="landingPageHighlights-title"
					values={{
						strong: (...chunks: string[]) => <strong>{chunks}</strong>,
						br: <br className="u-hide@lgDown" />,
					}}
				/>
			</h2>
			<div className="b-highlights__list u-mb-last-0">
				<div className="b-highlights__item">
					<div className="b-highlights__inner u-mb-last-0">
						<span className="b-highlights__icon">
							<Icon name="highlight-3" />
						</span>
						<h3 className="b-highlights__title">
							<FormattedMessage
								defaultMessage="{count} minut každý den"
								description="landingPageHighlights-1-title"
								values={{ count: formatNumber(45) }}
							/>
						</h3>
						<span className="b-highlights__text">
							<FormattedMessage
								defaultMessage="Tolik času můžete ušetřit procházením všech serverů s nemovitostmi. My to uděláme za vás. Váš čas je vzácný."
								description="landingPageHighlights-1-description"
							/>
						</span>
					</div>
				</div>
				<div className="b-highlights__item">
					<div className="b-highlights__inner u-mb-last-0">
						<span className="b-highlights__icon">
							<Icon name="highlight-2" />
						</span>
						<h3 className="b-highlights__title">
							<FormattedMessage
								defaultMessage="{count} realitních serverů"
								description="landingPageHighlights-2-title"
								values={{ count: formatNumber(count) }}
							/>
						</h3>
						<span className="b-highlights__text">
							<FormattedMessage
								defaultMessage="Projdeme automaticky téměř všechny realitní servery v Česku. Každý den tak analyzujeme přes {count} nabídek."
								description="landingPageHighlights-2-description"
								values={{ count: formatNumber(30000) }}
							/>
						</span>
					</div>
				</div>
				<div className="b-highlights__item">
					<div className="b-highlights__inner u-mb-last-0">
						<span className="b-highlights__icon">
							<Icon name="highlight-1" />
						</span>
						<h3 className="b-highlights__title">
							<FormattedMessage
								defaultMessage="{count} uživatelů"
								description="landingPageHighlights-3-title"
								values={{ count: formatNumber(7500) }}
							/>
						</h3>
						<span className="b-highlights__text">
							<FormattedMessage
								defaultMessage="Visidoo se rozrůstá každý den o nové spokojené uživatele. Přidejte se také a budete mezi prvními ve frontě."
								description="landingPageHighlights-3-description"
							/>
						</span>
					</div>
				</div>
			</div>
		</section>
	);
};

LandingPageHighlights.displayName = 'LandingPageHighlights';
