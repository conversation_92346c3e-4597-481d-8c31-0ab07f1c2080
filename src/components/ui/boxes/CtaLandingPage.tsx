import { Cta } from 'components/ui/boxes/Cta';
import { FC } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';

export const CtaLandingPage: FC = () => {
	const { formatMessage } = useIntl();

	return (
		<Cta
			title={formatMessage({ defaultMessage: 'Jsme Visidoo', description: 'lp-ctaTitle' })}
			text={
				<>
					<strong>
						<FormattedMessage defaultMessage="Pomáháme lidem najít nové bydlení" description="lp-ctaDescriptionPrefix" />
					</strong>{' '}
					<FormattedMessage
						defaultMessage="pomocí technologií a umělé inteligence. Prohledáváme většinu trhu nemovitostí v ČR a informujeme o nových
						nemovitostech"
						description="lp-ctaDescriptionBody"
					/>{' '}
					<strong>
						<FormattedMessage defaultMessage="nejrychleji na trhu" description="lp-ctaDescriptionSuffix" />
					</strong>
					.
				</>
			}
			btn={formatMessage({ defaultMessage: 'Hlídat nejnovějš<PERSON> inzeráty', description: 'lp-ctaButton' })}
			href="/"
		/>
	);
};

CtaLandingPage.displayName = 'CtaLandingPage';
