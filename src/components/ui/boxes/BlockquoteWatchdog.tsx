import { Icon } from 'components/ui/core/Icon';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';

export const BlockquoteWatchdog: FC = () => {
	return (
		<article className="blockquote blockquote--color blockquote--lp u-animate-1">
			<p className="blockquote__text u-mb-0">
				<strong className="blockquote__title">
					&hellip;
					<FormattedMessage
						defaultMessage="většina hlídacích psů posílá upozornění až druhý den?"
						description="blockquoteWatchdog-title"
					/>
				</strong>
				<FormattedMessage
					defaultMessage="U těch nejzajímavějších nabídek jde přitom o minuty."
					description="blockquoteWatchdog-description"
				/>
			</p>
			<span className="blockquote__quotes">
				<Icon name="quotes" />
			</span>
		</article>
	);
};

BlockquoteWatchdog.displayName = 'BlockquoteWatchdog';
