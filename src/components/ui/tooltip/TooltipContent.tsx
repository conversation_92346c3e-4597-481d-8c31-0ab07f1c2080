import { Portal } from '@reach/portal';
import { createRef, FC, RefObject, useEffect, useState } from 'react';

type Props = {
	handle: RefObject<HTMLDivElement>;
	show: () => void;
	hide: () => void;
	position: 'top' | 'right';
};

export const TooltipContent: FC<Props> = ({ handle, show, hide, position, children }) => {
	const [elementPosition, setElementPosition] = useState({});
	const [pointerPosition, setPointerPosition] = useState({});
	const contentRef = createRef<HTMLDivElement>();

	const update = () => {
		const calculatePosition = () => {
			const handleElement = handle.current;
			const contentElement = contentRef.current;
			if (handleElement && contentElement) {
				const handleBox = handleElement.getBoundingClientRect();
				const contentBox = contentElement.getBoundingClientRect();
				const pointerBox = (contentElement.querySelector('.tooltip__pointer') as HTMLSpanElement).getBoundingClientRect();
				if (position === 'top') {
					const anchor = {
						x: handleBox.left + handleBox.width / 2 + window.scrollX,
						y: handleBox.top + window.scrollY - contentBox.height - pointerBox.height / 2,
					};
					const overflow = window.outerWidth - anchor.x - contentBox.width / 2;

					return {
						elementPosition: {
							top: anchor.y,
							left: anchor.x + (overflow < 0 ? overflow : 0),
						},
						pointerPosition: {
							left: `calc(50% + ${overflow < 0 ? -overflow : 0}px)`,
						},
					};
				}

				const anchor = {
					x: handleBox.left + handleBox.width + window.scrollX + pointerBox.width / 2,
					y: handleBox.top + window.scrollY + handleBox.height / 2,
				};
				return {
					elementPosition: {
						top: anchor.y,
						left: anchor.x,
					},
					pointerPosition: {},
				};
			}

			return { elementPosition: {}, pointerPosition: {} };
		};

		const { elementPosition, pointerPosition } = calculatePosition();
		setElementPosition(elementPosition);
		setPointerPosition(pointerPosition);
	};

	useEffect(() => {
		if (Object.keys(elementPosition).length === 0 && Object.keys(pointerPosition).length === 0) {
			update();
		}
		window.addEventListener('resize', update);
		return () => {
			window.removeEventListener('resize', update);
		};
	}, [elementPosition, pointerPosition]);

	return (
		<Portal>
			<div
				className={`tooltip__content tooltip__content--${position}`}
				ref={contentRef}
				style={elementPosition}
				onMouseEnter={show}
				onMouseLeave={hide}
			>
				<span className="tooltip__pointer" style={pointerPosition} />
				<span>{children}</span>
			</div>
		</Portal>
	);
};

TooltipContent.displayName = 'TooltipContent';
