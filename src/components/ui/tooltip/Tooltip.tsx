import clsx from 'clsx';
import { Icon } from 'components/ui/core/Icon';
import { TooltipContent } from 'components/ui/tooltip/TooltipContent';
import { FC, ReactNode, useRef, useState } from 'react';
import { FormattedMessage } from 'react-intl';

type Props = {
	handle?: ReactNode;
	title?: ReactNode;
	forInput?: boolean;
	position?: 'top' | 'right';
};

export const Tooltip: FC<Props> = ({ handle, title, forInput, position = 'top', children }) => {
	const handleRef = useRef<HTMLDivElement>(null);
	const [isVisible, setVisible] = useState(false);
	const show = () => setVisible(true);
	const hide = () => setVisible(false);

	return (
		<div className={clsx('tooltip', forInput && 'tooltip--input', `tooltip--${position}`)} onMouseEnter={show} onMouseLeave={hide}>
			{title && <span className="tooltip__text">{title}</span>}
			{handle ? (
				<span className="tooltip__handle" ref={handleRef}>
					{handle}
				</span>
			) : (
				<span className="tooltip__icon" ref={handleRef}>
					<Icon name="question" />
					<span className="u-vhide">
						<FormattedMessage description={'tooltip-learnMore'} defaultMessage={'Dovědět se více'} />
					</span>
				</span>
			)}
			{isVisible && (
				<TooltipContent handle={handleRef} show={show} hide={hide} position={position}>
					{children}
				</TooltipContent>
			)}
		</div>
	);
};
