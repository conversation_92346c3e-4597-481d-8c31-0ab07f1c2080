import Image from 'next/image';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import { BlogAuthor } from 'types/author';
import { formatDate } from 'utils/formats';
import { Flag } from '../core/Flag';
import { ItemIcon } from '../core/ItemIcon';
// import { ArticleAuthor } from './ArticleAuthor';
import { Socials } from '../core/Socials';

type Props = {
	title: string;
	annot?: string;
	date?: number;
	readingTime?: string;
	canonical?: string;
	author?: BlogAuthor | null;
	imgSrc?: string;
	tags?: string[];
};

export const ArticleAnnotation: FC<Props> = ({ tags = [], imgSrc, title, annot, date, readingTime, canonical /*, author*/ }) => (
	<div className="b-annot-blog b-annot-blog--high">
		{typeof imgSrc !== 'undefined' && imgSrc !== '' && (
			<div className="b-annot-blog__img">
				<Image src={imgSrc} alt={title} quality="100" layout="fill" lazyBoundary="1000px" priority />
				{tags.length > 0 && (
					<span className="b-annot-blog__flags">
						{tags.map((tag) => (
							<Flag icon="tip" name={tag} key={tag} />
						))}
					</span>
				)}
			</div>
		)}
		<div className="b-annot-blog__content u-mb-last-0">
			<h1 className="b-annot-blog__title">{title}</h1>
			{annot && <p className="b-annot-blog__annot">{annot}</p>}
			<ul className="b-annot-blog__list">
				{/* <li className="b-annot-blog__item">
						<ArticleAuthor {...author} />
					</li> */}
				{date && (
					<li className="b-annot-blog__item">
						<FormattedMessage description={'articleAnnotation-released'} defaultMessage={'Vydáno:'} />{' '}
						{formatDate(new Date(date))}
					</li>
				)}
				{readingTime && (
					<li className="b-annot-blog__item">
						<ItemIcon name="clock" text={readingTime} />
					</li>
				)}
				{canonical && (
					<li className="b-annot-blog__item">
						<Socials canonical={canonical} title={title} />
					</li>
				)}
			</ul>
			<hr />
		</div>
	</div>
);

ArticleAnnotation.displayName = 'ArticleAnnotation';
