import { FC } from 'react';
import { B<PERSON><PERSON>uthor } from 'types/author';
import Image from 'next/image';
import { Link } from 'components/Link';
import { FormattedMessage } from 'react-intl';

export const ArticleAuthor: FC<BlogAuthor> = ({ name, surname, path, thumbnail }) => (
	<p className="author">
		{typeof thumbnail !== 'undefined' && (
			<span className="author__img">
				<Image src={thumbnail} alt={`${name} ${surname}`} width={40} height={40} lazyBoundary="1000px" />
			</span>
		)}
		<span className="author__name">
			<span className="u-font-medium">
				<FormattedMessage description={'articleAuthor-name'} defaultMessage={'Autor článku:'} />{' '}
			</span>
			<Link href="/autor/[...author]" as={`/autor/${path}`} Element={() => <a className="author__link" />}>
				{name} {surname}
			</Link>
		</span>
	</p>
);

ArticleAuthor.displayName = 'ArticleAuthor';
