import clsx from 'clsx';
import { FC, ReactNode } from 'react';
import { HeadingElementNames, Title } from '../core/Title';
import { Icon } from '../core/Icon';

type Props = {
	caption?: ReactNode;
	title: ReactNode;
	bg?: boolean;
	titleElement?: HeadingElementNames;
	margin?: '0' | 'xs' | 'sm' | 'md' | 'lg';
};

export const ArticleIntro: FC<Props> = ({ caption, title, bg = false, titleElement = 'h1', margin, children }) => (
	<div className={clsx('b-intro', 'u-mb-md', 'u-mb-last-0', bg && 'b-intro--bg')}>
		{caption && (
			<div className="b-intro__caption">
				<p className="caption u-mb-last-0">{caption}</p>
			</div>
		)}
		<Title tagName={titleElement} margin={margin}>
			{title}
		</Title>
		{bg && (
			<div className="b-intro__bg">
				<Icon name="rays" />
			</div>
		)}
		{children}
	</div>
);

ArticleIntro.displayName = 'ArticleIntro';
