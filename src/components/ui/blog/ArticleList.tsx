import { FC } from 'react';
import { BlogArticles } from 'types/blog';
import { ArticleListItem } from 'components/ui/blog/ArticleListItem';

type Props = {
	articles: BlogArticles;
	featured?: boolean;
};

export const ArticleList: FC<Props> = ({ articles, featured }) => {
	return (
		<div className="c-crossroad__list grid grid--y-lg">
			{articles.map((article, index) =>
				featured && index === 0 ? (
					<div className="grid__cell" key={article.metadata.path}>
						<ArticleListItem metadata={article.metadata} featured={true} imgWidth={970} imgHeight={400} />
					</div>
				) : (
					<div className="grid__cell size--6-12@sm size--4-12@md" key={article.metadata.path}>
						<ArticleListItem metadata={article.metadata} />
					</div>
				),
			)}
		</div>
	);
};

ArticleList.displayName = 'ArticleList';
