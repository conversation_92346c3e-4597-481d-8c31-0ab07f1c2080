import clsx from 'clsx';
import { Link } from 'components/Link';
import Image from 'next/image';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import { BlogArticleMeta } from 'types/blog';
import { formatDate } from 'utils/formats';
import { Flag } from '../core/Flag';

type Props = {
	metadata: BlogArticleMeta;
	featured?: boolean;
	imgWidth?: number;
	imgHeight?: number;
};

export const ArticleListItem: FC<Props> = ({ metadata, featured = false }) => {
	const articleTitle = (
		<Link
			href="/blog/detail/[...article]"
			as={`/blog/detail/${metadata.path}`}
			Element={(props) => <a {...props} className="b-article__link link-extend__link" />}
		>
			{metadata.heading}
		</Link>
	);

	return (
		<article className={clsx('b-article', 'link-extend', 'u-mb-last-0', featured && 'b-article--featured')}>
			{typeof metadata.previewImage !== 'undefined' && metadata.previewImage !== '' && (
				<div className="b-article__img">
					<Image src={metadata.previewImage} alt="" layout="fill" quality="100" lazyBoundary="1000px" />
					<span className="b-article__flags">
						{metadata.tags.map((tag) => (
							<Flag icon="tip" name={tag} key={tag} />
						))}
					</span>
				</div>
			)}
			{featured ? <h2 className="b-article__title h6">{articleTitle}</h2> : <h3 className="b-article__title h6">{articleTitle}</h3>}
			<p className="b-article__annot">
				<strong>{formatDate(new Date(metadata.publish))}</strong> {metadata.perex}
			</p>
			<p>
				<Link
					href="/blog/detail/[...article]"
					as={`/blog/detail/${metadata.path}`}
					Element={(props) => <a {...props} className="link-extend__unmask" />}
				>
					<FormattedMessage description={'articleListItem-readArticle'} defaultMessage={'Přečíst článek'} />
				</Link>
			</p>
		</article>
	);
};

ArticleListItem.displayName = 'ArticleListItem';
