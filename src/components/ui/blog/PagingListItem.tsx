import { Link } from 'components/Link';
import { FC } from 'react';
import { Button } from '../core/Button';

type Props = {
	href: string;
	disabled?: boolean;
	as?: string;
};

export const PagingListItem: FC<Props> = ({ href, as, disabled = false, children }) => (
	<Link href={href} as={as} prefetch={false} Element={(props) => <Button {...props} type="button" disabled={disabled} />}>
		{children}
	</Link>
);

PagingListItem.displayName = 'PagingItem';
