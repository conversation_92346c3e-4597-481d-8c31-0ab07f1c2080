import { FC } from 'react';
import { BlogArticleSource } from 'types/blog';

type Props = {
	sources: BlogArticleSource[];
};

export const ArticleSources: FC<Props> = ({ sources }) => {
	return (
		<div className="c-sources">
			<h2 className="c-sources__title h6"><PERSON><PERSON><PERSON>je</h2>
			<ul className="c-sources__list">
				{sources.map((source) => {
					if (source.href === '') return null;

					return (
						<li className="c-sources__item" key={source.href}>
							<a href={source.href} target="_blank" rel="noopener noreferrer">
								{source.title !== '' ? source.title : source.href}
							</a>
						</li>
					);
				})}
			</ul>
		</div>
	);
};

ArticleSources.displayName = 'ArticleSources';
