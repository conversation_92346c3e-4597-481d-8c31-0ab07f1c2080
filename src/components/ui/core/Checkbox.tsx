import clsx from 'clsx';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { FC, ReactNode } from 'react';
import { useFormContext } from 'react-hook-form';
import { FlagPremium } from '../core/FlagPremium';

type Props = {
	label: string | ReactNode;
	name: string;
	required?: boolean;
	reversed?: boolean;
	onlyPremium?: boolean;
	showBadgePremium?: boolean;
};

export const Checkbox: FC<Props> = ({ label, name, required = false, reversed = false, onlyPremium = false, showBadgePremium = false }) => {
	const {
		register,
		formState: { errors },
	} = useFormContext();

	const content = (
		<>
			<Label required={required} checkbox>
				<input
					type="checkbox"
					className={clsx('inp-item__inp', reversed && 'is-reversed')}
					{...register(name, { required: required ? 'Toto je povinná polož<PERSON>' : false })}
				/>
				<span className="inp-item__text">{label}</span>
			</Label>
			<FormError name={name} block />
		</>
	);
	return (
		<div className={clsx('inp-row', errors[name] && 'has-error')}>
			{onlyPremium ? (
				<>
					<span className="premium-enabled">{content}</span>
					<FlagPremium text="Standard" verticalFix={true} />
				</>
			) : (
				<>
					<>{content}</>
					{showBadgePremium && <FlagPremium text="Standard" verticalFix={true} />}
				</>
			)}
		</div>
	);
};

Checkbox.displayName = 'Checkbox';
