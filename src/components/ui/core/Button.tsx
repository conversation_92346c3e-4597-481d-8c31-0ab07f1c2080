import clsx from 'clsx';
import { FC, forwardRef, HTMLAttributes } from 'react';
import { Icon } from './Icon';
import { ItemIcon } from './ItemIcon';

type Props = {
	text?: string;
	href?: string;
	type?: 'button' | 'submit' | 'reset';
	variant?: 'secondary' | 'outline' | 'facebook' | 'twitter' | 'linkedin';
	size?: 'lg' | 'md' | 'sm' | 'xs';
	disabled?: boolean;
	full?: boolean;
	iconOnly?: string;
	iconBefore?: string;
	iconAfter?: string;
	target?: string;
} & HTMLAttributes<HTMLElement>;

export const Button: FC<Props> = forwardRef<HTMLAnchorElement, Props>(
	(
		{
			text,
			href,
			full = false,
			type = 'button',
			size = 'md',
			variant,
			disabled = false,
			iconOnly,
			iconBefore,
			iconAfter,
			children,
			className,
			target,
			...rest
		},
		ref,
	) => {
		const fullClassName = clsx(
			'btn',
			`btn--${size}`,
			variant && `btn--${variant}`,
			disabled && 'btn--disabled',
			full && 'btn--full',
			iconOnly && 'btn--icon',
			className,
		);

		const content = (
			<span className="btn__text">
				{typeof children !== 'undefined' ? (
					children
				) : typeof iconOnly !== 'undefined' ? (
					<Icon name={iconOnly} />
				) : typeof iconBefore !== 'undefined' && typeof text !== 'undefined' ? (
					<ItemIcon name={iconBefore} text={text} />
				) : typeof iconAfter !== 'undefined' && typeof text !== 'undefined' ? (
					<ItemIcon name={iconAfter} text={text} variant="after" />
				) : typeof text !== 'undefined' ? (
					text
				) : null}
			</span>
		);

		return typeof href === 'undefined' ? (
			<button className={fullClassName} type={type} disabled={disabled} {...rest}>
				{content}
			</button>
		) : (
			<a href={href} className={fullClassName} ref={ref} target={target} {...rest}>
				{content}
			</a>
		);
	},
);

Button.displayName = 'Button';
