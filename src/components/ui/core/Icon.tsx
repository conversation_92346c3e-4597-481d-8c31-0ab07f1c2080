import { FC } from 'react';

type Props = {
	name: string;
	newType?: boolean;
};

export const Icon: FC<Props> = ({ name }) => {
	return (
		<span className={`icon-svg icon-svg--${name}`} aria-hidden="true">
			<svg className="icon-svg__svg" xmlnsXlink="http://www.w3.org/1999/xlink">
				<use xlinkHref={`/img/bg/icons-svg.svg#icon-${name}`} width="100%" height="100%" focusable="false"></use>
			</svg>
		</span>
	);
};

Icon.displayName = 'Icon';
