import { FC } from 'react';

type Props = {
	text?: string;
};

export const FlagPremiumAlone: FC<Props> = ({ text }) => {
	return (
		<span className="flag flag--premium">
			<span className="icon-svg flag__icon">
				<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" className="icon-svg__svg">
					<path d="M16,2.3c-1.9,0-3.4,1.5-3.4,3.4c0,1.3,0.7,2.4,1.8,3L11,15.4l-5.2-3.8c0.7-0.6,1.1-1.5,1.1-2.5c0-1.9-1.5-3.4-3.4-3.4 S0,7.3,0,9.1c0,1.5,1.1,2.8,2.5,3.2L4.6,24v5.7h22.9V24l2.1-11.6C30.9,12,32,10.7,32,9.1c0-1.9-1.5-3.4-3.4-3.4 c-1.9,0-3.4,1.5-3.4,3.4c0,1,0.4,1.9,1.1,2.5L21,15.4l-3.4-6.7c1.1-0.6,1.8-1.7,1.8-3C19.4,3.8,17.9,2.3,16,2.3z M16,4.6 c0.6,0,1.1,0.5,1.1,1.1S16.6,6.9,16,6.9c-0.6,0-1.1-0.5-1.1-1.1S15.4,4.6,16,4.6z M3.4,8c0.6,0,1.1,0.5,1.1,1.1 c0,0.6-0.5,1.1-1.1,1.1S2.3,9.8,2.3,9.1C2.3,8.5,2.8,8,3.4,8z M28.6,8c0.6,0,1.1,0.5,1.1,1.1c0,0.6-0.5,1.1-1.1,1.1 s-1.1-0.5-1.1-1.1C27.4,8.5,27.9,8,28.6,8z M16,10.6l3.5,7.1l1.7,0.4L27,14l-1.6,8.9H6.6L5,14l5.7,4.1l1.7-0.4L16,10.6z M6.9,25.1 h18.3v2.3H6.9V25.1z" />
				</svg>
			</span>
			{text && <span className="flag__text">{text}</span>}
		</span>
	);
};

FlagPremiumAlone.displayName = 'FlagPremiumAlone';
