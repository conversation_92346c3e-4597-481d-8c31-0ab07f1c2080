import { fetchLocationMeta } from 'api/location/fetchLocationMeta';
import { fetchLocations } from 'api/location/fetchLocations';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { FormError } from 'components/ui/core/FormError';
import { ChangeEvent, FC, KeyboardEventHandler, MouseEventHandler, ReactNode, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { Location } from 'types/filter';
import { Icon } from '../core/Icon';
import { Label } from './Label';

type Props = {
	label: string;
	minLength?: number;
	name: string;
	required?: boolean;
	onSelect?: (location: Location | null) => void;
	extraContent?: ReactNode;
};

let timer: number | null = null;

const defaultSuggestions = [
	{
		id: '-439840',
		level: 8,
		name: '<PERSON>rah<PERSON>',
		fullName: 'Praha, okres Hlav<PERSON> m<PERSON>, <PERSON><PERSON><PERSON><PERSON> Praha, Praha, Česko',
		part: null,
		city: 'Praha',
		district: 'okres Hlavní město Praha',
		region: 'Hlavní město Praha',
		cohesion: 'Praha',
		country: 'Česko',
	},
	{
		id: '-438171',
		level: 8,
		name: 'Brno',
		fullName: 'Brno, okres Brno-město, Jihomoravský kraj, Jihovýchod, Česko',
		part: null,
		city: 'Brno',
		district: 'okres Brno-město',
		region: 'Jihomoravský kraj',
		cohesion: 'Jihovýchod',
		country: 'Česko',
	},
	{
		id: '-437354',
		level: 8,
		name: 'Ostrava',
		fullName: 'Ostrava, okres Ostrava-město, Moravskoslezský kraj, Moravskoslezsko, Česko',
		part: null,
		city: 'Ostrava',
		district: 'okres Ostrava-město',
		region: 'Moravskoslezský kraj',
		cohesion: 'Moravskoslezsko',
		country: 'Česko',
	},
	{
		id: '-438344',
		level: 8,
		name: 'Plzeň',
		fullName: 'Plzeň, okres Plzeň-město, Plzeňský kraj, Jihozápad, Česko',
		part: null,
		city: 'Plzeň',
		district: 'okres Plzeň-město',
		region: 'Plzeňský kraj',
		cohesion: 'Jihozápad',
		country: 'Česko',
	},
	{
		id: '-439073',
		level: 8,
		name: 'Liberec',
		fullName: 'Liberec, okres Liberec, Liberecký kraj, Severovýchod, Česko',
		part: null,
		city: 'Liberec',
		district: 'okres Liberec',
		region: 'Liberecký kraj',
		cohesion: 'Severovýchod',
		country: 'Česko',
	},
];

const getLocationLabel = ({ city, district, region, part }: Location) =>
	[part, city, district, region].filter((string): string is string => typeof string === 'string').join(', ');

export const Suggest: FC<Props> = ({ name, label, minLength = 2, required = false, onSelect, extraContent }) => {
	const {
		setValue,
		clearErrors,
		register,
		trigger,
		watch,
		formState: { errors },
	} = useFormContext();
	const locationId = watch('locationId');
	const [isOpened, setIsOpened] = useState<boolean>(false);
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const [currentValue, setCurrentValue] = useState('');
	const [items, setItems] = useState<Location[]>(defaultSuggestions);
	const [selectedItem, setSelectedItem] = useState<Location | null>(null);
	const [highlightedItem, setHighlightedItem] = useState<Location | null>(null);
	const [crossVisible, setCrossVisible] = useState(currentValue.length > 0);
	const intl = useIntl();

	const enterAreaIntlString = intl.formatMessage({
		description: 'suggest-enterArea',
		defaultMessage: 'Zadejte a vyberte prosím některou oblast',
	});

	const enterDataIntlString = intl.formatMessage({
		description: 'suggest-enterData',
		defaultMessage: 'Zadejte kraj, okres, obec…',
	});

	const handleItemClick = (item: Location) => {
		setSelectedItem(item);
		setIsOpened(false);
		setHighlightedItem(null);
		setValue(name, item.id);
		trigger(name);
		onSelect?.(item);
	};

	const handleCrossClick: MouseEventHandler = (event) => {
		setItems([]);
		setSelectedItem(null);
		setHighlightedItem(null);
		setIsOpened(false);
		setCrossVisible(false);
		setCurrentValue('');
		setValue(name, '');
		trigger(name);
		onSelect?.(null);

		event.currentTarget.closest('.suggest__field-wrapper')?.querySelector<HTMLInputElement>('input[type="text"]')?.focus();
	};

	const handleFocus = () => {
		setIsOpened(items.length > 0);
	};

	const handleBlur = () => {
		setIsOpened(false);
	};

	const handleKeyDown: KeyboardEventHandler = (event) => {
		const { key } = event;
		if (key !== 'ArrowDown' && key !== 'ArrowUp' && key !== 'Enter') {
			setHighlightedItem(null);
			return;
		}
		event.preventDefault();
		setIsOpened(true);

		if (!highlightedItem) {
			if (key === 'ArrowDown') {
				setHighlightedItem(items[0]);
			} else if (key === 'ArrowUp') {
				setHighlightedItem(items[items.length - 1]);
			} else {
				handleItemClick(items[0]);
			}
			return;
		}

		const currentIndex = items.findIndex((item) => item.id === highlightedItem.id);
		if (~currentIndex) {
			if (key === 'ArrowDown') {
				if (items[currentIndex + 1]) {
					setHighlightedItem(items[currentIndex + 1]);
				} else {
					setHighlightedItem(items[0]);
				}
			} else if (key === 'ArrowUp') {
				if (items[currentIndex - 1]) {
					setHighlightedItem(items[currentIndex - 1]);
				} else {
					setHighlightedItem(items[items.length - 1]);
				}
			} else {
				handleItemClick(highlightedItem);
			}
		}
	};

	const handleMouseOver = () => {
		setHighlightedItem(null);
	};

	const getSuggestedItems = (event: ChangeEvent<HTMLInputElement>): void => {
		const value = event.currentTarget.value;
		setCurrentValue(value);
		setValue(name, '');
		if (value.length > 0) {
			clearErrors(name);
			setCrossVisible(true);
		} else {
			setCrossVisible(false);
			trigger(name);
		}

		if (value.length < minLength) {
			setIsOpened(false);
			return;
		}

		const getLocations = async () => {
			setIsLoading(true);

			const result = await fetchLocations(value);
			if (!result) return;
			setItems(result.data.suggestions);
			setIsOpened(result.data.suggestions.length > 0);
			setIsLoading(false);
		};

		if (timer) {
			clearTimeout(timer);
			timer = null;
		}
		timer = window.setTimeout(getLocations, 200);
	};

	useEffect(() => {
		if (locationId) {
			const found = items.find((item) => item.id === locationId);
			if (found) {
				setCurrentValue(getLocationLabel(found));
				setCrossVisible(true);
			} else {
				(async () => {
					setIsLoading(true);
					const response = await fetchLocationMeta(locationId);
					const geo = response.data.geo;
					setItems([geo]);
					setCurrentValue(getLocationLabel(geo));
					setCrossVisible(true);
					setIsLoading(false);
				})();
			}
		} else {
			setCurrentValue('');
			setCrossVisible(false);
		}
	}, [locationId]);

	return (
		<>
			<div className="suggest  u-mb-xs">
				<div className={clsx('inp-row u-mb-0', errors[name] && 'has-error')}>
					<div className="inp-row__top">
						<Label id={name} required={required}>
							{label} {extraContent && extraContent}
						</Label>
						<div className="suggest__tool u-mt-0">Možnosti: Kraj, Město, Obec, část obce/města</div>
					</div>
					<div className={clsx('suggest__group-container', 'input-loader', isLoading && 'is-loading')}>
						<div className="suggest__field-container">
							<div className="suggest__field-wrapper">
								<input type="hidden" {...register(name, { required: required ? enterAreaIntlString : false })} />
								<input
									type="text"
									id={name}
									className="inp-text inp-text--dark suggest__field js-suggest__inp"
									placeholder={enterDataIntlString}
									autoComplete="new-password"
									role="presentation"
									onInput={getSuggestedItems}
									onFocus={handleFocus}
									onBlur={handleBlur}
									onKeyDown={handleKeyDown}
									value={currentValue}
								/>
								<div className="suggest__visual-field">
									{crossVisible && !isLoading && <Button iconOnly="close" onClick={handleCrossClick} />}
								</div>
							</div>
							<div className="suggest__prepend">
								<Icon name="pin-1" />
							</div>
						</div>
					</div>
					<FormError name={name} />
				</div>
				{items.length > 0 && (
					<div className={clsx('suggest__wrapper', isOpened && 'is-visible')}>
						<ul className="suggest__list">
							{items.map((item) => (
								<li
									className={clsx(
										'suggest__item',
										selectedItem?.id === item.id && 'is-selected',
										highlightedItem?.id === item.id && 'is-highlighted',
									)}
									key={item.id}
								>
									<a
										className="suggest__link"
										onMouseOver={handleMouseOver}
										onClick={(event) => {
											event.preventDefault();
											handleItemClick(item);
										}}
									>
										<Icon name="pin-1" />
										{getLocationLabel(item)}
									</a>
								</li>
							))}
						</ul>
					</div>
				)}
			</div>
		</>
	);
};

Suggest.displayName = 'Suggest';
