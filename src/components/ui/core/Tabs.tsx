import clsx from 'clsx';
import { Tab } from 'components/ui/core/Tab';
import { useRouter } from 'next/router';
import { Children, createContext, FC, MouseEventHandler, ReactElement, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { normalizeString } from 'utils/formats';

export type TabItem = {
	label: string;
	value: string;
};

type Props = {
	className?: string;
	children: (ReactElement | boolean)[];
};

const TabsContext = createContext<(key: string) => MouseEventHandler<HTMLElement>>((key) => (event) => void key && void event);

export const useTabs = () => useContext(TabsContext);

export const Tabs: FC<Props> = ({ children, className }) => {
	const router = useRouter();
	const wrapperRef = useRef<HTMLDivElement>(null);
	const properChildren = Array.from(children).filter(Boolean) as ReactElement[];
	const [activeItem, setActiveItem] = useState<string>(normalizeString(properChildren[0].props.title));
	const [renderedTabs, setRenderedTabs] = useState<string[]>([]);

	const handleChange = useCallback(
		async (key: string) => {
			if (key !== activeItem) {
				setActiveItem(key);
				await router.push(router.route, `${router.asPath.split('#')[0]}#${key}`, { scroll: false, shallow: true });
				window.scrollTo(window.scrollX, window.scrollY); // To prevent automatic scrolling...
			}
			if (!renderedTabs.includes(key)) {
				setRenderedTabs([...renderedTabs, key]);
			}
		},
		[activeItem, renderedTabs, router],
	);

	const changeTab =
		(key: string): MouseEventHandler<HTMLElement> =>
		(event) => {
			event.preventDefault();
			const element = wrapperRef.current;
			if (element) {
				const { x, y } = element.getBoundingClientRect();
				window.scrollTo(window.scrollX + x, window.scrollY + y - 150);
			} else {
				window.scrollTo(0, 0);
			}
			handleChange(key);
		};

	const handleRouteChange = useCallback(
		(url: string) => {
			const key = url.split('#')[1];
			if (key) {
				setActiveItem(key);
			} else {
				setActiveItem(normalizeString(properChildren[0].props.title));
			}
			window.scrollTo(window.scrollX, window.scrollY); // To prevent automatic scrolling...
		},
		[properChildren],
	);

	useEffect(() => {
		router.events.on('hashChangeComplete', handleRouteChange);
		router.events.on('routeChangeComplete', handleRouteChange);
		return () => {
			router.events.off('routeChangeComplete', handleRouteChange);
			router.events.off('hashChangeComplete', handleRouteChange);
		};
	}, [handleRouteChange, router.events]);

	useEffect(() => {
		const key = router.asPath.split('#')[1];
		handleChange(key ? key : activeItem);
	}, [activeItem, handleChange, router.asPath]);

	return (
		<TabsContext.Provider value={changeTab}>
			<div className={clsx('b-tabs', className)}>
				<nav className="b-tabs__menu">
					<div className="b-tabs__container">
						<ul className="b-tabs__list">
							{Children.map(properChildren, ({ props: { title } }) => {
								const key = normalizeString(title);
								return (
									<li key={key} className="b-tabs__item">
										<a
											className={clsx('b-tabs__link', activeItem === key && 'is-active')}
											onClick={() => handleChange(key)}
										>
											{title}
										</a>
									</li>
								);
							})}
						</ul>
					</div>
				</nav>
				<div className="b-tabs__fragments">
					{Children.map(properChildren, ({ props: { title, children } }) => {
						const key = normalizeString(title);
						return (
							<Tab key={key} open={activeItem === key} render={renderedTabs.includes(key)}>
								{children}
							</Tab>
						);
					})}
				</div>
			</div>
		</TabsContext.Provider>
	);
};

Tabs.displayName = 'Tabs';
