import clsx from 'clsx';
import { Link } from 'components/Link';
import { FC } from 'react';
import { Icon } from '../core/Icon';

type Props = {
	name: string;
	icon?: string;
	href?: string;
	color?: 'purple' | 'green' | 'semidark' | 'dark' | 'yellow';
};

export const Flag: FC<Props> = ({ name, icon = 'tip', href, color }) => {
	const className = clsx('flag', href && 'flag--link', color && `flag--${color}`);
	const content = (
		<span className="flag__inner">
			<span className="flag__icon">
				<Icon name={icon} />
			</span>
			<span className="flag__text">{name}</span>
		</span>
	);

	if (href) {
		return (
			<Link
				href={href}
				Element={({ className: linkClassName, ...props }) => <a {...props} className={clsx(className, linkClassName)} />}
			>
				{content}
			</Link>
		);
	} else {
		return <span className={className}>{content}</span>;
	}
};

Flag.displayName = 'Flag';
