import clsx from 'clsx';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { FC } from 'react';
import { useFormContext } from 'react-hook-form';

type Props = {
	name: string;
	label: string;
	required?: boolean;
};

export const DateTimePicker: FC<Props> = ({ name, label, required = false }) => {
	const {
		register,
		formState: { errors },
	} = useFormContext();

	return (
		<div className={clsx('inp-row', errors[name] && 'has-error')}>
			<span className="inp-row__top">
				<Label id={name} required={required}>
					{label}
				</Label>
			</span>
			<span className="inp-fix">
				<input
					className="inp-text"
					id={name}
					type="text"
					{...register(name, {
						required,
						validate: (value) => (!value || !isNaN(new Date(value).getTime()) ? true : 'Vstup není platný datum a čas'),
					})}
				/>
			</span>
			<FormError name={name} />
		</div>
	);
};

DateTimePicker.displayName = 'DateTimePicker';
