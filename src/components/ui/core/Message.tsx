import clsx from 'clsx';
import { FC } from 'react';
import { Icon } from './Icon';

type Props = {
	icon: string;
	variant?: 'ok' | 'warning' | 'error';
	noclose?: boolean;
	iconMiddle?: boolean;
};

export const Message: FC<Props> = ({ icon, variant, noclose, iconMiddle, children }) => (
	<p className={clsx('message', variant && `message--${variant}`, noclose && 'message--noclose', iconMiddle && 'message--icon-middle')}>
		<span className="message__icon">
			<Icon name={icon} />
		</span>
		{children}
	</p>
);

Message.displayName = 'Message';
