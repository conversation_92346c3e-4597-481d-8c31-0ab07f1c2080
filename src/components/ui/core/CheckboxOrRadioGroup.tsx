import clsx from 'clsx';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { formValidationInltMessages } from 'i18n/commonIntlMessages/formValidation';
import { FC } from 'react';
import { useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { checkFormError } from 'utils/checkFormError';
import { FlagPremium } from './FlagPremium';

type Item = { label: string; value: string };

type Props = {
	items: Item[];
	label?: string;
	emptyLabel?: string;
	name: string;
	size?: 'sm' | 'md' | 'lg' | 'auto';
	inputType?: 'checkbox' | 'radio';
	required?: boolean;
	onlyPremium?: boolean;
	disabled?: boolean;
	showBadgePremium?: boolean;
};

export const CheckboxOrRadioGroup: FC<Props> = ({
	items,
	label,
	emptyLabel,
	name,
	size = 'sm',
	inputType = 'checkbox',
	required = false,
	onlyPremium = false,
	disabled = false,
	showBadgePremium = false,
}: Props) => {
	const {
		register,
		formState: { errors },
		watch,
		setValue,
	} = useFormContext();
	const groupValue = watch(name);
	const { formatMessage } = useIntl();
	const { selectAtLeastOneIntlString } = formValidationInltMessages;
	const requiredMessage = formatMessage(selectAtLeastOneIntlString);

	const hasError = checkFormError(name, errors);

	const onEmptyClick = () => {
		if (groupValue.length === 0) return;
		setValue(name, []);
	};

	return (
		<div className={clsx('inp-row', hasError && 'has-error')}>
			{onlyPremium ? (
				<>
					<span className="premium-enabled">
						<Label required={required}>{label}</Label>
					</span>
					<FlagPremium text="Standard" verticalFix={true} />
				</>
			) : (
				<>
					{label && <Label required={required}>{label}</Label>}
					{showBadgePremium && <FlagPremium text="Standard" verticalFix={true} />}
				</>
			)}
			<div className={clsx('inp-items', onlyPremium && 'premium-enabled')}>
				<ul className="inp-items__list grid">
					{emptyLabel && (
						<li
							className={clsx(
								size == 'auto' ? 'inp-items__item grid__cell size--auto' : 'inp-items__item grid__cell size--6-12',
								size === 'sm' && 'size--4-12@sm size--3-12@md',
								size === 'md' && 'size--4-12@md',
							)}
						>
							<div className="inp-row">
								<label className="inp-item inp-item--checkbox">
									<input
										type={inputType}
										id="empty"
										className="inp-item__inp"
										checked={!groupValue || groupValue.length === 0}
										onChange={onEmptyClick}
									/>
									<span className="inp-item__text">{emptyLabel}</span>
								</label>
							</div>
						</li>
					)}
					{items.map(({ label, value }) => (
						<li
							key={value}
							className={clsx(
								size == 'auto' ? 'inp-items__item grid__cell size--auto' : 'inp-items__item grid__cell size--6-12@sm',
								size === 'sm' && 'size--4-12@sm size--3-12@md',
								size === 'md' && 'size--4-12@md',
							)}
						>
							<div className="inp-row">
								<label className="inp-item inp-item--checkbox">
									<input
										type={inputType}
										id={value}
										value={value}
										className="inp-item__inp"
										{...register(name, {
											disabled: disabled,
											required: required ? requiredMessage : undefined,
										})}
									/>
									<span className="inp-item__text">{label}</span>
								</label>
							</div>
						</li>
					))}
				</ul>
			</div>
			<FormError name={name} />
		</div>
	);
};

CheckboxOrRadioGroup.displayName = 'CheckboxOrRadioGroup';
