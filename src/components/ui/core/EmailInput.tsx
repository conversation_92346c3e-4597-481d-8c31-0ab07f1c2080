import { filterInltMessages } from 'components/ui/filter/filterIntlMessages';
import { EmailSuggestionsBox } from 'components/user/EmailSuggestionsBox';
import { useEmailSuggestions } from 'hooks/useEmailSuggestions';
import { formValidationInltMessages } from 'i18n/commonIntlMessages/formValidation';
import { FC, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { EMAIL_PATTERN } from 'utils/constants';
import { getEmailSuggestions } from 'utils/emailSuggestions';

type Props = {
	needsSuggestions?: boolean;
	name?: string;
	id?: string;
	disabled?: boolean;
};

export const EmailInput: FC<Props> = ({ needsSuggestions = false, name = 'email', id = 'email', disabled = false }) => {
	const { formatMessage } = useIntl();
	const {
		register,
		formState: { errors },
		watch,
	} = useFormContext();

	const { requiredEmailIntlString } = formValidationInltMessages;
	const { emailFormatIntlString } = filterInltMessages;
	const startingEmailValue = watch('email');
	const [userEmail, setUserEmail] = useState(startingEmailValue ?? '');

	const { showSuggestionsBox, emailClientSuggestions, handleSuggestionSelection, customValidationRan } = useEmailSuggestions({
		needsSuggestions,
		currentEmailErrorType: errors.email?.type,
		userEmail: userEmail,
		updateEmailValue: (email: string) => setUserEmail(email),
	});

	return (
		<>
			<input
				type="email"
				inputMode="email"
				id={id}
				className="inp-text"
				placeholder="@"
				{...register(name, {
					onChange: (e) => setUserEmail(e.target.value),
					disabled: disabled,
					required: formatMessage(requiredEmailIntlString),
					pattern: {
						value: EMAIL_PATTERN,
						message: formatMessage(emailFormatIntlString),
					},
					validate: (value) => {
						if (!needsSuggestions || customValidationRan) return true;
						return getEmailSuggestions(value).length === 0;
					},
				})}
				autoComplete="email"
				value={userEmail}
			/>

			{showSuggestionsBox && emailClientSuggestions && (
				<EmailSuggestionsBox
					emailClientSuggestions={emailClientSuggestions}
					handleSuggestionSelection={handleSuggestionSelection}
				/>
			)}
		</>
	);
};
