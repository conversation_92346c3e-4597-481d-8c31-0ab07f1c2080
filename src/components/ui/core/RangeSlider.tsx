import clsx from 'clsx';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { ChangeEvent, FC, FocusEvent, useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import InputRange from 'react-input-range';
import { useIntl } from 'react-intl';
import { formatNumber } from 'utils/formats';
import { sanitizeInputNumber } from 'utils/sanitizeInputNumber';

export type RangeValue = {
	min: number;
	max: number;
};

type Props = {
	label: string;
	min?: number;
	max: number;
	name: string;
	steps?: number;
	logarithmic?: boolean;
	roundTo?: number;
	unit?: string;
	focus?: 'lte' | 'gte';
};

type CalculateProps = {
	min: number;
	max: number;
	steps: number;
	logarithmic: boolean;
	roundTo: number;
};

const calculateValues = ({ min, max, steps, logarithmic, roundTo = 1 }: CalculateProps) => {
	let results: number[];
	if (logarithmic) {
		const scale = (Math.log(max) - (roundTo === 0 ? roundTo : Math.log(roundTo))) / steps;
		results = Array.from({ length: steps }).map((_, i) => Math.exp((roundTo === 0 ? roundTo : Math.log(roundTo)) + scale * (i + 1)));
	} else {
		const scale = (max - min) / steps;
		results = Array.from({ length: steps }).map((_, i) => min + scale * (i + 1));
	}

	return [0, ...results.map((value) => Math.round(value / roundTo) * roundTo)];
};

export const RangeSlider: FC<Props> = ({
	label,
	min = 0,
	max,
	name,
	steps = 100,
	logarithmic = false,
	roundTo = 1,
	unit,
	focus = 'lte',
}) => {
	const {
		register,
		setValue,
		getValues,
		formState: { errors },
	} = useFormContext();

	const { formatMessage } = useIntl();
	const values = useMemo(() => calculateValues({ logarithmic, max, min, roundTo, steps }), [logarithmic, max, min, roundTo, steps]);

	const findPosition = (value: number | null) => {
		if (value === null) return steps;
		if (value >= max) return steps;
		if (value <= min) return 0;

		const found = values.findIndex((val) => val > value);
		if (found) return found - 1;
		return steps;
	};

	const formatMinValue = (value: string | number | null) => {
		if (!value) return min;
		const sanitized = sanitizeInputNumber(value);
		return isNaN(sanitized) ? min : sanitized <= min ? min : sanitized;
	};

	const formatMaxValue = (value: string | number | null) => {
		if (!value) return null;
		const sanitized = sanitizeInputNumber(value);
		return isNaN(sanitized) ? null : sanitized;
	};

	const baseValues = getValues()[name];
	const [visualValue, setVisualValue] = useState<{ min: number; max: number | null }>({
		min: baseValues.gte ?? formatMinValue(min),
		max: baseValues.lte === null ? null : baseValues.lte ?? formatMaxValue(max),
	});

	const [position, setPosition] = useState<RangeValue>({
		min: baseValues.gte ? findPosition(baseValues.gte) : 0,
		max: baseValues.lte ? findPosition(baseValues.lte) : steps,
	});

	const handleMinChange = (event: ChangeEvent<HTMLInputElement>) => {
		setVisualValue((visualValue) => ({
			...visualValue,
			min: formatMinValue(event.target.value),
		}));
		setPosition((position) => ({
			...position,
			min: findPosition(formatMinValue(event.target.value)),
		}));
	};

	const handleMaxChange = (event: ChangeEvent<HTMLInputElement>) => {
		setVisualValue((visualValue) => ({
			...visualValue,
			max: formatMaxValue(event.target.value),
		}));
		setPosition((position) => ({
			...position,
			max: findPosition(formatMaxValue(event.target.value)),
		}));
	};

	const handleMinBlur = (event: FocusEvent<HTMLInputElement>) => {
		const value = formatMinValue(event.target.value);
		const rangeValue = ((current) => {
			if (current.max !== null && value >= current.max) {
				return { min: value, max: value + roundTo };
			}

			return { min: value, max: current.max };
		})(visualValue);

		setVisualValue(rangeValue);
		setPosition({
			min: findPosition(rangeValue.min),
			max: findPosition(rangeValue.max),
		});
		setValue(`${name}.gte`, rangeValue.min);
		setValue(`${name}.lte`, rangeValue.max);
	};

	const handleMaxBlur = (event: FocusEvent<HTMLInputElement>) => {
		const value = formatMaxValue(event.target.value);
		const rangeValue = ((current: { min: number; max: number | null }) => {
			if (value === null) {
				return { min: current.min, max: value };
			}

			if (value <= min) {
				return { min, max: min + roundTo };
			}

			if (value <= current.min) {
				if (current.min <= min + roundTo) {
					return { min: current.min, max: min + roundTo };
				}

				return { min: value - roundTo, max: value };
			}

			return { min: current.min, max: value };
		})(visualValue);

		setVisualValue(rangeValue);
		setPosition({
			min: findPosition(rangeValue.min),
			max: findPosition(rangeValue.max),
		});
		setValue(`${name}.gte`, rangeValue.min);
		setValue(`${name}.lte`, rangeValue.max);
	};

	const handleSliderChange = (newPosition: RangeValue | number) => {
		const position = newPosition as RangeValue;
		setPosition(position);
		setVisualValue({
			min: formatMinValue(values[position.min]),
			max: formatMaxValue(position.max === steps ? null : values[position.max]),
		});
	};

	const handleSliderChangeComplete = (newPosition: RangeValue | number) => {
		const position = newPosition as RangeValue;
		setValue(`${name}.gte`, values[position.min]);
		setValue(`${name}.lte`, position.max === steps ? null : values[position.max]);
	};

	const formatLabel = (position: number) =>
		`${formatNumber(values[position > steps ? steps : position < min ? min : position])}${unit ? ` ${unit}` : ''}`;

	useEffect(() => {
		setVisualValue({
			min: formatMinValue(values[position.min]),
			max: formatMaxValue(position.max === steps ? null : values[position.max]),
		});
		setValue(`${name}.gte`, values[position.min]);
		setValue(`${name}.lte`, position.max === steps ? null : values[position.max]);
	}, [values]);

	return (
		<div className={clsx('inp-row', errors[name] && 'has-error')}>
			<Label id={`${name}.${focus}`}>{label}</Label>
			<div className="range">
				<div className="range__input">
					<p className="inp-row">
						<span className="inp-fix inp-append">
							<input type="hidden" {...register(`${name}.gte`, { setValueAs: (value) => formatMinValue(value) })} />
							<input
								type="text"
								id={`${name}.gte`}
								className="inp-text"
								value={formatNumber(visualValue.min)}
								onChange={handleMinChange}
								onBlur={handleMinBlur}
							/>
							{unit && <span className="inp-append__append">{unit}</span>}
						</span>
					</p>
				</div>
				<div className="range__input">
					<p className="inp-row">
						<span className="inp-fix inp-append">
							<input type="hidden" {...register(`${name}.lte`, { setValueAs: (value) => formatMaxValue(value) })} />
							<input
								type="text"
								id={`${name}.lte`}
								className="inp-text"
								placeholder={formatMessage({ defaultMessage: 'Neomezeno', description: 'slider-noLimit' })}
								value={!visualValue.max ? '' : formatNumber(visualValue.max)}
								onChange={handleMaxChange}
								onBlur={handleMaxBlur}
							/>
							{unit && <span className="inp-append__append">{unit}</span>}
						</span>
					</p>
				</div>
			</div>
			<InputRange
				minValue={0}
				maxValue={steps}
				value={position}
				onChange={handleSliderChange}
				onChangeComplete={handleSliderChangeComplete}
				step={1}
				formatLabel={formatLabel}
			/>
			<FormError name={name} />
		</div>
	);
};

RangeSlider.displayName = 'RangeSlider';
