/* eslint-disable @next/next/no-img-element */
import { fetchRealEstatePatch } from 'api/realEstate/fetchRealEstatePatch';
import clsx from 'clsx';
import { FC, ImgHTMLAttributes, useMemo, useState } from 'react';

type Props = ImgHTMLAttributes<HTMLImageElement> & {
	src: string;
	alt: string;
	realEstateId?: string;
};

const fallback = '/img/logo-visidoo.svg';

export const ExternalImage: FC<Props> = ({ realEstateId, ...props }) => {
	const [src, setSrc] = useState(props.src);
	const className = useMemo(() => clsx(props.className, src === fallback && 'is-fallback'), [src, props.className]);

	const handleError = async () => {
		setSrc(fallback);
		if (realEstateId) {
			await fetchRealEstatePatch(realEstateId);
		}
	};

	return <img {...props} src={src} className={className} onError={handleError} alt={props.alt} loading="lazy" decoding="async" />;
};

ExternalImage.displayName = 'ExternalImage';
