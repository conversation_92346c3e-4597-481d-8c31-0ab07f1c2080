import clsx from 'clsx';
import { FC } from 'react';
import { useFormContext } from 'react-hook-form';

type Props = {
	name: string;
	block?: boolean;
};

export const FormError: FC<Props> = ({ name, block = false }) => {
	const {
		formState: { errors },
	} = useFormContext();

	const explodedName = name.split('.');
	const error = explodedName.reduce((acc, key) => acc?.[key], errors);
	if (!error) return null;

	return <span className={clsx('inp-note', block && 'u-show')}>{Array.isArray(error) ? error[0].message : error.message}</span>;
};

FormError.displayName = 'FormError';
