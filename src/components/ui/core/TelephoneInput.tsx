import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { formValidationInltMessages } from 'i18n/commonIntlMessages/formValidation';
import { FC } from 'react';
import { useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';

type Props = {
	name?: string;
	id?: string;
	required?: boolean;
	disabled?: boolean;
	defaultValue?: string;
};

export const TelephoneInput: FC<Props> = ({
	name = 'telephone',
	id = 'telephone',
	required = false,
	disabled = false,
	defaultValue = undefined,
}) => {
	const { formatMessage } = useIntl();
	const { register } = useFormContext();

	const { telephonePlaceholderIntlString } = commonInltMessages;
	const { requiredTelephoneIntlString } = formValidationInltMessages;

	return (
		<input
			type="tel"
			inputMode="tel"
			id={id}
			className="inp-text"
			placeholder={formatMessage(telephonePlaceholderIntlString)}
			{...register(name, {
				disabled: disabled,
				required: required && formatMessage(requiredTelephoneIntlString),
			})}
			autoComplete="tel"
			defaultValue={defaultValue}
		/>
	);
};
