import clsx from 'clsx';
import { FC, ReactNode } from 'react';
import { Icon } from './Icon';

type Props = {
	name: string;
	text: string | ReactNode;
	variant?: 'after' | 'low' | 'medium' | 'high';
};

export const ItemIcon: FC<Props> = ({ variant, name, text }) => (
	<span className={clsx('item-icon', variant && `item-icon--${variant}`)}>
		<span className="item-icon__icon">
			<Icon name={name} />
		</span>
		<span className="item-icon__text">{text}</span>
	</span>
);

ItemIcon.displayName = 'ItemIcon';
