import clsx from 'clsx';
import { FC } from 'react';
import { Icon } from '../core/Icon';

type Props = {
	title: string;
	content: string;
	color?: boolean;
	bg?: boolean;
	big?: boolean;
};

export const Blockquote: FC<Props> = ({ title, content, color = false, bg = false, big = false }) => {
	if (!title && !content) return null;

	return (
		<blockquote className={clsx('blockquote', color && 'blockquote--color', bg && 'blockquote--bg', big && 'blockquote--lg')}>
			<p className="blockquote__text">
				{title !== '' && (
					<>
						<span className="blockquote__title">{title}</span>
						<br />
					</>
				)}
				{content}
			</p>
			<span className="blockquote__quotes">
				<Icon name="quotes" />
			</span>
		</blockquote>
	);
};

Blockquote.displayName = 'Blockquote';
