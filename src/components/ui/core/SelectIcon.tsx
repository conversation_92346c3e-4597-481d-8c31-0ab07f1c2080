import clsx from 'clsx';
import { FormError } from 'components/ui/core/FormError';
import { FC, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { Icon } from '../core/Icon';
import { Label } from './Label';
import { formValidationInltMessages } from '../../../i18n/commonIntlMessages/formValidation';
import { useIntl } from 'react-intl';
import { SelectItem } from 'types/common';

type Props = {
	items: SelectItem[];
	label?: string;
	name: string;
	required?: boolean;
};

export const SelectIcon: FC<Props> = ({ label, name, items, required }) => {
	const {
		setValue,
		register,
		getValues,
		formState: { errors },
	} = useFormContext();
	const { formatMessage } = useIntl();
	const [selectedItem, setSelectedItem] = useState<SelectItem>(items.find((item) => getValues()[name] === item.value) || items[0]);
	const { selectAtLeastOneIntlString } = formValidationInltMessages;

	const handleItemClick = (item: SelectItem) => () => setSelectedItem(item);

	useEffect(() => {
		setValue(name, selectedItem.value);
	}, [name, selectedItem, setValue]);

	return (
		<div className={clsx('inp-row', errors[name] && 'has-error')}>
			{label && <Label required={required}>{label}</Label>}
			<input type="hidden" {...register(name, { required: required ? formatMessage(selectAtLeastOneIntlString) : false })} />
			<div className="inp-items">
				<ul className="inp-items__list grid grid--x-sm">
					{items.map((item) => (
						<li className="inp-items__item grid__cell size--auto" key={item.value}>
							<button
								type="button"
								className={clsx('inp-items__btn', 'btn', item.value === selectedItem.value && 'is-selected')}
								onClick={handleItemClick(item)}
							>
								<span className="item-icon">
									{item.icon && (
										<span className="item-icon__icon">
											<Icon name={item.icon} />
										</span>
									)}
									<span className="item-icon__text">{item.label}</span>
								</span>
							</button>
						</li>
					))}
				</ul>
			</div>
			<FormError name={name} />
		</div>
	);
};

SelectIcon.displayName = 'SelectIcon';
