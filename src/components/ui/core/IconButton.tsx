import { Tooltip } from 'components/ui/tooltip/Tooltip';
import { FC, HTMLAttributes } from 'react';
import { Icon } from './Icon';

type Props = {
	icon: string;
	text: string;
	withTooltip?: boolean;
} & HTMLAttributes<HTMLButtonElement>;

export const IconButton: FC<Props> = ({ icon, text, withTooltip = false, ...rest }) => {
	const button = (
		<button {...rest}>
			<Icon name={icon} />
			<span className="u-vhide">{text}</span>
		</button>
	);

	if (withTooltip) return <Tooltip handle={button}>{text}</Tooltip>;
	return button;
};

IconButton.displayName = 'IconButton';
