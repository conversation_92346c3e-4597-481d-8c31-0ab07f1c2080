import clsx from 'clsx';
import { FC } from 'react';
import { SelectItem } from 'types/common';
import { ItemIcon } from './ItemIcon';

type Props = {
	item: SelectItem;
	isSelected: boolean;
	handleItemClick: (item: SelectItem) => () => void;
};

export const MultiSelectItem: FC<Props> = ({ item, isSelected, handleItemClick }) => {
	return (
		<div className="select__option-wrapper" key={item.value}>
			<button type="button" className={clsx('select__option', isSelected && 'is-selected')} onMouseDown={handleItemClick(item)}>
				{item.icon ? <ItemIcon name={item.icon} text={item.label} /> : item.label}
			</button>
		</div>
	);
};
