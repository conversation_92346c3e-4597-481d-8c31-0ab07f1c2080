import { FC } from 'react';
import { Link } from 'components/Link';
import { Tooltip } from '../tooltip/Tooltip';
import { FlagPremiumAlone } from './FlagPremiumAlone';

type Props = {
	verticalFix?: boolean;
	text?: string;
	tooltip?: string;
};

export const FlagPremium: FC<Props> = ({ verticalFix = false, text, tooltip }) => {
	const content = (
		<Tooltip
			handle={
				<Link href="/premium-ucet">
					<FlagPremiumAlone text={text} />
				</Link>
			}
		>
			{tooltip ? tooltip : <>Dostupné od tarifu {text}.</>}
		</Tooltip>
	);

	return verticalFix ? <span style={{ position: 'relative', top: -4 + 'px' }}>{content}</span> : <>{content}</>;
};

FlagPremium.displayName = 'FlagPremium';
