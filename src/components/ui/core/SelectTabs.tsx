import clsx from 'clsx';
import { FormError } from 'components/ui/core/FormError';
import { FC, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { SelectItem } from 'types/common';
import { formValidationInltMessages } from '../../../i18n/commonIntlMessages/formValidation';

type Props = {
	name: string;
	label?: string;
	items: SelectItem[];
	required?: boolean;
};

export const SelectTabs: FC<Props> = ({ name, items, required }) => {
	const {
		setValue,
		register,
		getValues,
		formState: { errors },
	} = useFormContext();
	const { formatMessage } = useIntl();
	const [selectedItem, setSelectedItem] = useState<SelectItem>(items.find((item) => getValues()[name] === item.value) || items[0]);
	const { selectAtLeastOneIntlString } = formValidationInltMessages;

	const handleItemClick = (item: SelectItem) => () => setSelectedItem(item);

	useEffect(() => {
		setValue(name, selectedItem.value);
	}, [name, selectedItem, setValue]);

	return (
		<div className={clsx('inp-row', errors[name] && 'has-error')}>
			<div className="b-tabs__menu">
				<input type="hidden" {...register(name, { required: required ? formatMessage(selectAtLeastOneIntlString) : false })} />
				<div className="b-tabs__container">
					<ul className="b-tabs__list">
						{items.map((item) => (
							<li className="b-tabs__item" key={item.value}>
								<button
									type="button"
									className={clsx('b-tabs__link', 'btn', item.value === selectedItem.value && 'is-active')}
									onClick={handleItemClick(item)}
								>
									{item.label}
								</button>
							</li>
						))}
					</ul>
				</div>
				<FormError name={name} />
			</div>
		</div>
	);
};

SelectTabs.displayName = 'SelectTabs';
