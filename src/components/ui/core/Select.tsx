import clsx from 'clsx';
import { FormError } from 'components/ui/core/FormError';
import { ItemIcon } from 'components/ui/core/ItemIcon';
import { Label } from 'components/ui/core/Label';
import { useClickOutside } from 'hooks/useClickOutside';
import { formValidationInltMessages } from 'i18n/commonIntlMessages/formValidation';
import { FC, useEffect, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { SelectItem } from 'types/common';

const { selectAtLeastOneIntlString } = formValidationInltMessages;

type Props = {
	name: string;
	label: string;
	items: SelectItem[];
	required?: boolean;
	allowFilterMode?: boolean;
};

export const Select: FC<Props> = ({ name, label, items, required = false, allowFilterMode = false }) => {
	const {
		setValue,
		register,
		getValues,
		formState: { errors },
	} = useFormContext();
	const { formatMessage } = useIntl();
	const wrapperRef = useRef<HTMLDivElement>(null);
	const [isOpened, setIsOpened] = useState<boolean>(false);
	const [visibleItems, setVisibleItems] = useState(items);
	const [selectedItem, setSelectedItem] = useState<SelectItem>(
		visibleItems.find((item) => getValues()[name] === item.value) || visibleItems[0],
	);
	const [isInFilterMode, setIsInFilterMode] = useState(false);
	const [filterInputValue, setFilterInputValue] = useState(selectedItem.label);

	const toggleSelectOptions = () => {
		setIsOpened(!isOpened);
		if (allowFilterMode) {
			setIsInFilterMode(!isInFilterMode);
		}
	};

	const handleItemClick = (item: SelectItem) => () => {
		setSelectedItem(item);
		setIsOpened(false);
		if (allowFilterMode) {
			setVisibleItems(items);
			setIsInFilterMode(false);
			setFilterInputValue(item.label);
		}
	};

	const filterItems = (input: string) => {
		const filteredItems = items.filter((item) => item.label.toLowerCase().includes(input.toLowerCase()));
		setVisibleItems(filteredItems);
	};

	const onFilterInputChange = (value: string) => {
		filterItems(value);
		setFilterInputValue(value);
	};

	useClickOutside(wrapperRef, () => {
		if (isOpened) setIsOpened(false);
		if (allowFilterMode && isInFilterMode) setIsInFilterMode(false);
	});

	useEffect(() => {
		setValue(name, selectedItem.value);
	}, [name, selectedItem, setValue]);

	useEffect(() => {
		if (allowFilterMode && isInFilterMode) {
			setIsOpened(true);
		}
	}, [isInFilterMode, allowFilterMode]);

	return (
		<div className={clsx('inp-row', errors[name] && 'has-error')}>
			<Label onClick={toggleSelectOptions} required={required}>
				{label}
			</Label>
			<div className={clsx('select', isOpened && 'is-open')} ref={wrapperRef}>
				<input type="hidden" {...register(name, { required: required ? formatMessage(selectAtLeastOneIntlString) : false })} />
				{allowFilterMode && isInFilterMode ? (
					<input
						type="text"
						id="surname"
						className="inp-text"
						value={filterInputValue}
						autoFocus
						{...(register(name), { onChange: (e) => onFilterInputChange(e.target.value) })}
					/>
				) : (
					<button className="select__inp" type="button" onClick={toggleSelectOptions}>
						{selectedItem.icon ? <ItemIcon name={selectedItem.icon} text={selectedItem.label} /> : selectedItem.label}
					</button>
				)}

				<div className="select__options">
					{visibleItems.map((item) => (
						<div className="select__option-wrapper" key={item.value}>
							<button
								type="button"
								className={clsx('select__option', selectedItem.value === item.value && 'is-selected')}
								onMouseDown={handleItemClick(item)}
							>
								{item.icon ? <ItemIcon name={item.icon} text={item.label} /> : item.label}
							</button>
						</div>
					))}
				</div>
			</div>
			<FormError name={name} />
		</div>
	);
};

Select.displayName = 'Select';
