import { FC } from 'react';
import { FacebookShareButton, TwitterShareButton, EmailShareButton } from 'react-share';
import { Icon } from './Icon';

type Props = {
	title: string;
	canonical: string;
};

export const Socials: FC<Props> = ({ canonical, title }) => {
	return (
		<div className="socials">
			<ul className="socials__list">
				<li className="socials__item">
					<FacebookShareButton url={canonical}>
						<a href={canonical} className="socials__link">
							<Icon name="facebook" />
						</a>
					</FacebookShareButton>
				</li>
				<li className="socials__item">
					<TwitterShareButton url={canonical}>
						<a href={canonical} className="socials__link">
							<Icon name="twitter" />
						</a>
					</TwitterShareButton>
				</li>
				<li className="socials__item">
					<EmailShareButton url={canonical} subject={title}>
						<a href={canonical} className="socials__link">
							<Icon name="share" />
						</a>
					</EmailShareButton>
				</li>
			</ul>
		</div>
	);
};

Socials.displayName = 'Socials';
