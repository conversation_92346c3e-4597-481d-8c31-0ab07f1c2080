import clsx from 'clsx';
import { FC } from 'react';

export type HeadingElementNames = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

type Props = {
	tagName: HeadingElementNames;
	margin?: '0' | 'xs' | 'sm' | 'md' | 'lg';
	decorated?: boolean;
	align?: 'left' | 'center' | 'right';
	heading?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
};

export const Title: FC<Props> = ({ tagName: Wrapper, margin, decorated, align, heading, children }) => (
	<Wrapper
		className={clsx(
			'title',
			margin && `u-mb-${margin}`,
			decorated && `title--semicircle`,
			align && `u-text-${align}`,
			heading && `${heading}`,
		)}
	>
		{children}
	</Wrapper>
);

Title.displayName = 'Title';
