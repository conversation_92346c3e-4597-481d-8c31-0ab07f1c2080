import clsx from 'clsx';
import { FC, ReactNode, useState } from 'react';

type Props = {
	title: string;
	content: ReactNode;
};

export const AccordionItem: FC<Props> = ({ title, content }) => {
	const [isOpen, setIsOpen] = useState<boolean>(false);
	const handleClick = () => {
		setIsOpen(!isOpen);
	};

	return (
		<article className={clsx('b-accordion__item', isOpen && 'is-open')}>
			<h2 className="b-accordion__title">
				<button className="btn b-accordion__link h6" onClick={handleClick}>
					{title}
					<span className="b-accordion__indicator" />
				</button>
			</h2>
			<div className="b-accordion__content u-mb-last-0">{content}</div>
		</article>
	);
};

AccordionItem.displayName = 'AccordionItem';
