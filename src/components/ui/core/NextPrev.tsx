import { Button } from 'components/ui/core/Button';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';

type Props = {
	page: number;
	onChange: (page: number) => void;
	showNext: boolean;
	disabled?: boolean;
};

export const NextPrev: FC<Props> = ({ page, onChange, showNext, disabled }) => {
	return (
		<div className="m-next-prev">
			{page > 1 && (
				<Button className="m-next-prev__prev" onClick={() => onChange(page - 1)} disabled={disabled}>
					<FormattedMessage description={'paging-prevPage'} defaultMessage={'Předchozí stránka'} />
				</Button>
			)}
			{showNext && (
				<Button className="m-next-prev__next" onClick={() => onChange(page + 1)} disabled={disabled}>
					<FormattedMessage description={'paging-nextPage'} defaultMessage={'Násled<PERSON><PERSON><PERSON><PERSON><PERSON> strán<PERSON>'} />
				</Button>
			)}
		</div>
	);
};

NextPrev.displayName = 'NextPrev';
