import clsx from 'clsx';
import { Tooltip } from 'components/ui/tooltip/Tooltip';
import { FC, MouseEventHandler } from 'react';

type Props = {
	id?: string;
	required?: boolean;
	onClick?: MouseEventHandler;
	checkbox?: boolean;
};

export const Label: FC<Props> = ({ id, required = false, checkbox, onClick, children }) => {
	const label = (
		<label
			className={clsx('inp-label', required && 'inp-label--required', checkbox && 'inp-item inp-item--checkbox')}
			htmlFor={id}
			onClick={onClick}
		>
			{children}
		</label>
	);

	if (required)
		return (
			<Tooltip handle={label} position="right">
				Povinné
			</Tooltip>
		);

	return label;
};

Label.displayName = 'Label';
