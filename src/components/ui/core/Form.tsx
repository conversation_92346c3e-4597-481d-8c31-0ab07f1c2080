import { PropsWithChildren } from 'react';
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, UseFormReturn } from 'react-hook-form';

type Props<Inputs extends Record<string, unknown>> = {
	context: UseFormReturn<Inputs>;
	onSubmit: SubmitHandler<Inputs>;
};

export const Form = <Inputs extends Record<string, unknown>>({ context, onSubmit, children }: PropsWithChildren<Props<Inputs>>) => {
	const { handleSubmit } = context;
	return (
		<FormProvider {...context}>
			<form className="form" onSubmit={handleSubmit(onSubmit)} action="">
				{children}
			</form>
		</FormProvider>
	);
};

Form.displayName = 'Form';
