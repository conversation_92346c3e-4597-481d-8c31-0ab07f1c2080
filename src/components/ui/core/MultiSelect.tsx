import clsx from 'clsx';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { useClickOutside } from 'hooks/useClickOutside';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { formValidationInltMessages } from 'i18n/commonIntlMessages/formValidation';
import { FC, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { FormattedMessage, useIntl, MessageDescriptor } from 'react-intl';
import { SelectItem } from 'types/common';
import { sortStringsAsc, uniques } from 'utils/arrays';
import { MultiSelectItem } from './MultiSelectItem';

const { anyChoiceIntlString } = commonInltMessages;
const { selectAtLeastOneIntlString } = formValidationInltMessages;

type Props = {
	name: string;
	label: string;
	items: SelectItem[];
	required?: boolean;
	anyChoiceMessage?: MessageDescriptor;
};

export const MultiSelect: FC<Props> = ({ name, label, items, required = false, anyChoiceMessage }) => {
	const wrapperRef = useRef<HTMLDivElement>(null);
	const { formatMessage } = useIntl();
	const {
		formState: { errors },
		register,
		setValue,
		watch,
	} = useFormContext();
	const values: string[] = watch(name);
	const [isOpened, setIsOpened] = useState<boolean>(false);

	const toggleSelectOptions = () => {
		setIsOpened(!isOpened);
	};

	const handleItemClick = (item: SelectItem) => () => {
		if (item.value === '') {
			setValue(name, []);
		} else if (values.includes(`${item.value}`)) {
			setValue(
				name,
				values.filter((value) => value !== `${item.value}`),
			);
		} else {
			setValue(name, sortStringsAsc(uniques([...values, `${item.value}`])));
		}
	};

	useClickOutside(wrapperRef, () => {
		if (isOpened) setIsOpened(false);
	});

	return (
		<div className={clsx('inp-row', errors[name] && 'has-error')}>
			<Label onClick={toggleSelectOptions} required={required}>
				{label}
			</Label>
			<div className={clsx('select', isOpened && 'is-open')} ref={wrapperRef}>
				<input type="hidden" {...register(name, { required: required ? formatMessage(selectAtLeastOneIntlString) : false })} />

				<button className="select__inp" type="button" onClick={toggleSelectOptions}>
					{values.length === 0 ? (
						<FormattedMessage {...(required ? selectAtLeastOneIntlString : anyChoiceMessage ?? anyChoiceIntlString)} />
					) : (
						items
							.filter(({ value }) => values.includes(`${value}`))
							.map(({ label }) => label)
							.join(', ')
					)}
				</button>

				<div className="select__options">
					{!required && (
						<MultiSelectItem
							item={{
								value: '',
								label: formatMessage(anyChoiceMessage ?? anyChoiceIntlString),
							}}
							isSelected={values.length === 0}
							handleItemClick={handleItemClick}
						/>
					)}
					{items.map((item) => (
						<MultiSelectItem
							key={item.value}
							item={item}
							isSelected={values.includes(`${item.value}`)}
							handleItemClick={handleItemClick}
						/>
					))}
				</div>
			</div>
			<FormError name={name} />
		</div>
	);
};

MultiSelect.displayName = 'MultiSelect';
