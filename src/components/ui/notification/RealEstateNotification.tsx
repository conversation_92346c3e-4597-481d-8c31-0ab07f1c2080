import { Icon } from 'components/ui/core/Icon';
import { ItemIcon } from 'components/ui/core/ItemIcon';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { OfferPrice } from 'components/ui/offer/OfferPrice';
import { OfferTitle } from 'components/ui/offer/OfferTitle';
import { useTimeout } from 'hooks/useTimeout';
import { useShortLocale } from 'i18n/useShortLocale';
import { FC, useState } from 'react';
import { RealEstateNotificationProps } from 'types/notification';
import { getRelativeTimeString } from 'utils/getRelativeTimeString';

type Props = RealEstateNotificationProps;

export const RealEstateNotification: FC<Props> = ({ timeout, ...realEstate }) => {
	const [rendered, setRendered] = useState(false);
	const { closeNotification } = useNotifications();
	const handleClose = () => closeNotification(realEstate.id);
	useTimeout(handleClose, timeout);
	const shortLocale = useShortLocale();

	useTimeout(() => {
		setRendered(true);
	}, 10);

	return (
		<div className="notification notification--offer">
			<div className="notification__inner">
				<div className="notification__img">
					<span className="notification__flags">
						<ItemIcon name="clock" text={getRelativeTimeString(realEstate.createdAt, shortLocale, 'short')} />
					</span>
					{/* eslint-disable-next-line @next/next/no-img-element */}
					<img src={realEstate.imageUrl} alt="" loading="lazy" decoding="async" width="100" height="75" />
				</div>
				<div className="notification__content">
					<p className="notification__title">
						<OfferTitle className="notification__link" {...realEstate} />
					</p>
					<p className="notification__address">{realEstate.location?.name}</p>
					<span className="notification__details">
						<span className="notification__detail">
							<OfferPrice {...realEstate} />
						</span>
					</span>
				</div>
			</div>
			<span className="notification__close" onClick={handleClose}>
				<Icon name="close" />
			</span>
			{timeout && (
				<span className="notification__timer">
					<span
						className="notification__timer-inner"
						style={{ transitionDuration: `${Math.floor(timeout / 1000)}s`, width: rendered ? undefined : '100%' }}
					/>
				</span>
			)}
		</div>
	);
};

RealEstateNotification.displayName = 'RealEstateNotification';
