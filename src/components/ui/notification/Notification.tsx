import { Icon } from 'components/ui/core/Icon';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useTimeout } from 'hooks/useTimeout';
import { FC, useState } from 'react';
import { NotificationProps } from 'types/notification';

type Props = NotificationProps;

export const Notification: FC<Props> = ({ id, icon, message, timeout }) => {
	const [rendered, setRendered] = useState(false);
	const { closeNotification } = useNotifications();
	const handleClose = () => closeNotification(id);
	useTimeout(handleClose, timeout);

	useTimeout(() => {
		setRendered(true);
	}, 10);

	return (
		<p className="notification">
			<span className="notification__icon">
				<Icon name={icon ?? 'logotype-visidoo'} />
			</span>
			<span className="notification__text">{message}</span>
			<span className="notification__close" onClick={handleClose}>
				<Icon name="close" />
			</span>
			{timeout && (
				<span className="notification__timer">
					<span
						className="notification__timer-inner"
						style={{ transitionDuration: `${Math.floor(timeout / 1000)}s`, width: rendered ? undefined : '100%' }}
					/>
				</span>
			)}
		</p>
	);
};

Notification.displayName = 'Notification';
