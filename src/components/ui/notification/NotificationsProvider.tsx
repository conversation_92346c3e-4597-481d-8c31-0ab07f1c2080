import { Notification } from 'components/ui/notification/Notification';
import { RealEstateNotification } from 'components/ui/notification/RealEstateNotification';
import { createContext, FC, useCallback, useContext, useState } from 'react';
import { NotificationProps, Notifications, RealEstateNotificationProps } from 'types/notification';
import { uuid } from 'utils/uuid';

const NotificationsContext = createContext({
	addNotification: (props: Omit<NotificationProps, 'id'>) => void props,
	addRealEstateNotification: (props: RealEstateNotificationProps) => void props,
	closeNotification: (id: string) => void id,
});

export const useNotifications = () => useContext(NotificationsContext);

const isNotification = (notification: NotificationProps | RealEstateNotificationProps): notification is NotificationProps =>
	'message' in notification;

export const NotificationsProvider: FC = ({ children }) => {
	const [notifications, setNotifications] = useState<Notifications>([]);

	const addNotification = useCallback((props: Omit<NotificationProps, 'id'>) => {
		setNotifications((notifications) => [...notifications, { id: uuid(), ...props }]);
		return undefined;
	}, []);

	const addRealEstateNotification = useCallback((props: RealEstateNotificationProps) => {
		setNotifications((notifications) => [...notifications, props]);
		return undefined;
	}, []);

	const closeNotification = useCallback((id: string) => {
		setNotifications((notifications) => notifications.filter((notification) => notification.id !== id));
		return undefined;
	}, []);

	return (
		<NotificationsContext.Provider value={{ addNotification, addRealEstateNotification, closeNotification }}>
			{children}
			{notifications.length > 0 && (
				<div className="notification-container">
					{notifications.map((notification) => {
						if (isNotification(notification)) return <Notification key={notification.id} {...notification} />;
						return <RealEstateNotification key={notification.id} {...notification} />;
					})}
				</div>
			)}
		</NotificationsContext.Provider>
	);
};
