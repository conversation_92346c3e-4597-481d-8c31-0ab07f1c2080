import { useShortLocale } from 'i18n/useShortLocale';
import { FC } from 'react';
import { useIntl } from 'react-intl';
import { Offer } from 'types/offer';
import { uppercaseFirst } from 'utils/formats';
import { getFiltersLocalizedStrings } from 'utils/getFiltersLocalizedStrings';

type Props = Offer & { className?: string; showLink?: boolean };

export const OfferTitle: FC<Props> = ({
	url,
	adType,
	realEstateType,
	disposition,
	area,
	landArea,
	landType,
	className,
	showLink = true,
}) => {
	const shortLocale = useShortLocale();
	const intl = useIntl();
	const { generateTitle } = getFiltersLocalizedStrings(intl, shortLocale);
	const title = uppercaseFirst(generateTitle({ adType, realEstateType, area, landArea, landType, disposition }), shortLocale);

	if (showLink) {
		return (
			<a href={url} className={className} target="_blank" rel="noopener noreferrer">
				{title}
			</a>
		);
	}

	return <>{title}</>;
};

OfferTitle.displayName = 'OfferTitle';
