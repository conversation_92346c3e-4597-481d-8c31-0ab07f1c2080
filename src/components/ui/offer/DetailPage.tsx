import { FC, PropsWithChildren } from 'react';
import { PageSeo } from 'components/meta/PageSeo';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import {
	CityPerformerStats,
	Offer,
	PossibleOfferTypeForDetailPage,
	StatsPayload,
	StatsSource,
	ValuoStats,
	VisidooPriceStats,
} from 'types/offer';
import { AnalysisSection } from 'components/ui/offer/AnalysisSection';
import { HistorySection } from 'components/insertions/HistorySection';
import { PrivateOfferNote } from './PrivateOfferNote';

type OfferVariant = 'public' | 'private';

type Props = {
	realEstate: Offer;
	offerVariant: OfferVariant;
};

export enum WidgetStatus {
	SUCCESS = 'Success',
	ERROR = 'Error',
	LOADING = 'Loading',
}

export const DetailPage: FC<PropsWithChildren<Props>> = ({ realEstate, offerVariant, children }) => {
	const { formatMessage } = useIntl();

	const [showHistorySectionWith, setShowHistorySectionWith] = useState<PossibleOfferTypeForDetailPage | null>(null);

	const cityPerformerStats = realEstate?.stats?.find((item) => item.source === StatsSource.CityPerformer && item.stats) as
		| StatsPayload<CityPerformerStats>
		| undefined;
	const valuoStats = realEstate?.stats?.find((item) => item.source === StatsSource.Valuo && item.stats) as
		| StatsPayload<ValuoStats>
		| undefined;
	const visidooPriceStats = realEstate?.stats?.find((item) => item.source === StatsSource.VisidooPrice && item.stats) as
		| StatsPayload<VisidooPriceStats>
		| undefined;

	const closeHistorySection = () => {
		if (showHistorySectionWith) setShowHistorySectionWith(null);
	};

	const onHistoryClick = (offerType: PossibleOfferTypeForDetailPage) => {
		setShowHistorySectionWith(offerType);
	};

	return (
		<>
			<PageSeo
				title={formatMessage({
					defaultMessage: 'Detail nemovitosti',
					description: 'lpDetail-title',
				})}
			/>

			<div className="row-main row-main--detail">
				{children}

				<AnalysisSection
					cityPerformerStats={cityPerformerStats}
					visidooPriceStats={visidooPriceStats}
					valuoStats={valuoStats}
					onHistoryClick={onHistoryClick}
					realEstate={realEstate}
				/>

				{offerVariant === 'private' && <PrivateOfferNote />}
			</div>

			{(visidooPriceStats?.stats?.sale?.sample || visidooPriceStats?.stats?.rent?.sample) && realEstate != null ? (
				<HistorySection
					offerToCompareWith={realEstate}
					isVisible={showHistorySectionWith != null}
					closeHistorySection={closeHistorySection}
					offerType={showHistorySectionWith}
					visidooPriceStats={visidooPriceStats}
				/>
			) : null}
		</>
	);
};

DetailPage.displayName = 'DetailPage';
