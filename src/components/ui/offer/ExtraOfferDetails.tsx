import { FC, ReactNode } from 'react';
import { Offer } from 'types/offer';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { FormattedDate, FormattedDateTimeRange, FormattedMessage, useIntl } from 'react-intl';
import { calculateDistanceBetweenPoints } from 'utils/calculateDistanceBetweenPoints';
import { ownershipTypeMessageMap } from 'i18n/intlMessageMaps';

type IntlObj = {
	description: string;
	defaultMessage: string;
};

type Props = {
	offer: Offer;
	offerToCompareWith?: Offer;
};

export const ExtraOfferDetails: FC<Props> = ({ offer, offerToCompareWith }) => {
	const { disposition, area, landArea, extended, gps } = offer;
	const { formatMessage } = useIntl();
	const offerToCompareWithGps = offerToCompareWith ? offerToCompareWith.gps : null;
	const {
		dispositionIntlString,
		areaIntlString,
		landAreaIntlString,
		insertedAtIntlString,
		floorNumberIntlString,
		ownershipTypeIntlString,
		distanceBetweenPointsIntlString,
	} = commonInltMessages;
	const offerLifetime = getOfferLifeRange(offer);
	const distanceBetweenMainProperty = calculateDistanceBetweenPoints(offerToCompareWithGps, gps);
	const ownershipTypeIntlValue = extended?.ownershipType ? formatMessage(ownershipTypeMessageMap[extended?.ownershipType]) : null;

	return (
		<dl>
			{addDescriptionRow(disposition, dispositionIntlString)}
			{addDescriptionRow(area, areaIntlString, 'm²')}
			{addDescriptionRow(landArea, landAreaIntlString, 'm²')}
			{addDescriptionRow(offerLifetime, insertedAtIntlString)}
			{extended?.floor && addDescriptionRow(extended.floor, floorNumberIntlString)}
			{ownershipTypeIntlValue && addDescriptionRow(ownershipTypeIntlValue, ownershipTypeIntlString)}
			{addDescriptionRow(distanceBetweenMainProperty, distanceBetweenPointsIntlString)}
		</dl>
	);
};

const addDescriptionRow = (offerFeature: ReactNode, intlObj: IntlObj, unit?: string) => {
	if (!offerFeature) return null;
	return (
		<>
			<dt>
				<FormattedMessage {...intlObj} />
			</dt>
			<dd>
				{offerFeature} {unit}
			</dd>
		</>
	);
};

const getOfferLifeRange = (offer: Offer) => {
	const { createdAt, deletedAt } = offer;
	if (!createdAt) return null;
	if (!deletedAt) return <FormattedDate value={createdAt} />;
	return <FormattedDateTimeRange from={createdAt} to={deletedAt} />;
};
