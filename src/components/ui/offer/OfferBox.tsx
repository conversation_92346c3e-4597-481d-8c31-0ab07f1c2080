import { fetchReportContact } from 'api/contact/fetchReportContact';
import { PhotoCarousel } from 'components/insertions/PhotoCarousel';
import { ExternalImage } from 'components/ui/core/ExternalImage';
import { Icon } from 'components/ui/core/Icon';
import { RealEstateLocation } from 'components/ui/filter/RealEstateLocation';
import { OfferPrice } from 'components/ui/offer/OfferPrice';
import { OfferTitle } from 'components/ui/offer/OfferTitle';
import { useUser } from 'components/user/UserProvider';
import { useShortLocale } from 'i18n/useShortLocale';
import { FC, useState } from 'react';
import { ContactType } from 'types/admin';
import { RealEstateType } from 'types/filter';
import { Offer, OfferType } from 'types/offer';
import { getRelativeTimeString } from 'utils/getRelativeTimeString';
import { Button } from '../core/Button';
import { ExtraOfferDetails } from './ExtraOfferDetails';
import { OfferPriceM2 } from './OfferPriceM2';
import { OfferSource } from './OfferSource';

type Props = {
	offer: Offer;
	variant?: 'detailed';
	offerToCompareWith?: Offer;
};

export const OfferBox: FC<Props> = ({ offer, variant, offerToCompareWith }) => {
	const { createdAt, id, imageUrl, location, realEstateType, adType, contact } = offer;
	const { isAdvanced } = useUser();
	const shortLocale = useShortLocale();
	const [disabled, setDisabled] = useState(false);
	const isDetailed = variant === 'detailed';
	const relativeCreationTime = getRelativeTimeString(createdAt, shortLocale);

	const handleReport = (contactId: string) => async () => {
		setDisabled(true);
		await fetchReportContact(contactId);
	};

	return (
		<article className={`b-offer link-extend u-mb-last-0 ${isDetailed && 'b-offer--full'}`}>
			{isDetailed ? (
				<PhotoCarousel offer={offer}>
					<span className="b-offer__flag2">
						<span className="flag flag--purple">
							<span className="flag__inner">
								<span className="flag__icon">
									<Icon name="clock" />
								</span>
								<span className="flag__text">{relativeCreationTime}</span>
							</span>
						</span>
					</span>
				</PhotoCarousel>
			) : (
				<div className="b-offer__img">
					<ExternalImage src={imageUrl} alt="" width="260" height="200" realEstateId={id} />
					<span className="b-offer__flag2">
						<span className="flag flag--purple">
							<span className="flag__inner">
								<span className="flag__icon">
									<Icon name="clock" />
								</span>
								<span className="flag__text">{relativeCreationTime}</span>
							</span>
						</span>
					</span>
				</div>
			)}
			<div className="b-offer__content">
				<h3 className="b-offer__title">
					<OfferTitle className={`b-offer__link ${!isDetailed && 'link-extend__link'}`} {...offer} />
				</h3>
				{location && (
					<p className="b-offer__address">
						<RealEstateLocation location={location} />
					</p>
				)}
				{contact && (
					<>
						<p className="b-offer__address">
							{contact.email && (
								<span>
									E-mail:{' '}
									<a href={`mailto:${contact.email}`} title={contact.email}>
										{contact.email}
									</a>
								</span>
							)}{' '}
							<br />
							{contact.telephone && (
								<span>
									Telefon:{' '}
									<a href={`tel:${contact.telephone}`} title={contact.telephone}>
										{contact.telephone}
									</a>
								</span>
							)}
						</p>
						{isAdvanced && contact.contactType === ContactType.PRIVATE && (
							<p className="link-extend__unmask">
								<Button variant="outline" full onClick={handleReport(contact.id)} disabled={disabled}>
									Nahlásit realitku
								</Button>
							</p>
						)}
					</>
				)}

				{isDetailed && <ExtraOfferDetails offer={offer} offerToCompareWith={offerToCompareWith} />}

				<div className="b-offer__details">
					<OfferPrice {...offer} />
					{adType === OfferType.SALE &&
						(realEstateType === RealEstateType.APARTMENT || realEstateType === RealEstateType.LAND) && (
							<OfferPriceM2 {...offer} />
						)}
					{/* <ItemIcon name="flame" text="40 %" variant="low" /> */}
				</div>
				{isDetailed && (
					<div className="u-mt-xxs u-mb-last-0">
						<OfferSource realEstate={offer} />
					</div>
				)}
			</div>
		</article>
	);
};

OfferBox.displayName = 'OfferBox';
