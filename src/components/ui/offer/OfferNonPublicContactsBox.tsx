import { FC } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Offer } from 'types/offer';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
const { orIntlString } = commonInltMessages;

type Props = {
	realEstate: Offer;
};

export const OfferNonPublicContactsBox: FC<Props> = ({ realEstate }) => {
	const { formatMessage } = useIntl();

	return (
		<div className="b-box u-font-sm u-mb-smd">
			<div className="b-box__inner u-mb-last-0">
				<p className="h6">
					<FormattedMessage
						description="detail-nonpublic-cta-title"
						defaultMessage="Zaujala vás nabídka? Chcete více informací?"
					/>
				</p>

				{generateContactDetails(realEstate, formatMessage(orIntlString))}
			</div>
		</div>
	);
};

const generateEmailAddress = (offer: Offer) => {
	const emailToUse = offer.contact?.email ?? '<EMAIL>';
	return <a href={`mailto:${emailToUse}?subject=Zájem o neveřejnou nabídku`}>{emailToUse}</a>;
};

const generateContactDetails = (offer: Offer, connectorString: string) => {
	const emailElement = generateEmailAddress(offer);
	const telephone = offer.contact?.telephone;

	const details = telephone ? (
		<>
			{telephone} <span className="u-font-regular"> {connectorString} </span> {emailElement}
		</>
	) : (
		emailElement
	);
	return <p className="u-font-lg u-font-bold">{details}</p>;
};
