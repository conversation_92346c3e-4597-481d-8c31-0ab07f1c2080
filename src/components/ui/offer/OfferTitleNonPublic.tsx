import { useShortLocale } from 'i18n/useShortLocale';
import { FC } from 'react';
import { useIntl } from 'react-intl';
import { Offer } from 'types/offer';
import { uppercaseFirst } from 'utils/formats';
import { getFiltersLocalizedStrings } from 'utils/getFiltersLocalizedStrings';
import { FormattedMessage } from 'react-intl';

type Props = Offer & { className?: string };

export const OfferTitleNonPublic: FC<Props> = ({ adType, realEstateType, disposition, area, landArea, landType }) => {
	const shortLocale = useShortLocale();
	const intl = useIntl();
	const { generateTitle } = getFiltersLocalizedStrings(intl, shortLocale);

	return (
		<>
			<span className="h5 u-color-secondary">
				<FormattedMessage description="detail-nonpublic-title" defaultMessage="Neveřejná nabídka" />
				<sup>*</sup>
				<br />
			</span>
			{uppercaseFirst(generateTitle({ adType, realEstateType, area, landArea, landType, disposition }), shortLocale)}
		</>
	);
};

OfferTitleNonPublic.displayName = 'OfferTitleNonPublic';
