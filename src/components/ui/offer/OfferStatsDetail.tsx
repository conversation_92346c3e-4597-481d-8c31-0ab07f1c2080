import clsx from 'clsx';
import { useUser } from 'components/user/UserProvider';
import Link from 'next/link';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import { CityPerformerStats, Offer, StatsPayload } from 'types/offer';
import { PREMIUM_DETAIL_EXAMPLE_ID } from 'utils/constants';
import { BlockLoader } from '../core/BlockLoader';
import { Message } from '../core/Message';
import { WidgetStatus } from './DetailPage';

type Props = {
	widgetStatus: WidgetStatus;
	isShowingDetailView: boolean;
	cityPerformerStats: StatsPayload<CityPerformerStats> | undefined;
	updateWidgetStatus: (status: WidgetStatus) => void;
	realEstate: Offer;
};

export const OfferStatsDetail: FC<Props> = ({ widgetStatus, cityPerformerStats, isShowingDetailView, updateWidgetStatus, realEstate }) => {
	const { isPayingUser } = useUser();

	const locationContent = (
		<BlockLoader loading={widgetStatus === WidgetStatus.LOADING}>
			{cityPerformerStats?.stats.shareUrl ? (
				<iframe
					src={cityPerformerStats.stats.shareUrl}
					style={{
						width: '100%',
						height: '100%',
						border: 0,
						opacity: widgetStatus === WidgetStatus.SUCCESS ? 1 : 0,
					}}
					onLoad={() => updateWidgetStatus(WidgetStatus.SUCCESS)}
				/>
			) : (
				<div
					style={{
						position: 'absolute',
						top: '50%',
						left: '20px',
						right: '20px',
						transform: 'translateY(-50%)',
						textAlign: 'center',
					}}
				>
					<div style={{ display: 'inline-block' }}>
						<Message variant="warning" icon="exclamation" noclose>
							<FormattedMessage
								description="detail-missingLocationData"
								defaultMessage="Bohužel nemáme k dispozici informace k této lokalitě."
							/>
						</Message>
					</div>
				</div>
			)}
		</BlockLoader>
	);

	return (
		<div className={clsx('b-detail-side', 'b-detail-side--cityperformer', isShowingDetailView && 'is-visible')}>
			{isPayingUser || realEstate.id === PREMIUM_DETAIL_EXAMPLE_ID ? (
				locationContent
			) : (
				<>
					<div
						style={{
							filter: 'blur(4px)',
							pointerEvents: 'none',
							height: '100%',
						}}
					>
						{locationContent}
					</div>
					<div
						style={{
							position: 'absolute',
							top: '50%',
							left: '20px',
							right: '20px',
							transform: 'translateY(-50%)',
							textAlign: 'center',
						}}
					>
						<div style={{ display: 'inline-block' }}>
							<Message variant="warning" icon="exclamation" noclose>
								<strong>
									Pro zobrazení analýzy lokality je potřeba mít aktivní jeden z{' '}
									<Link href="/premium-ucet">placených účtů</Link>
								</strong>
							</Message>
						</div>
					</div>
				</>
			)}
		</div>
	);
};
