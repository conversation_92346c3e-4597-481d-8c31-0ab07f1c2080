import { FC } from 'react';
import { RealEstateType } from 'types/filter';
import { Offer } from 'types/offer';
import { formatNumber } from 'utils/formats';

type Props = Offer;

export const OfferPriceM2: FC<Props> = ({ price, area, landArea, realEstateType }) => {
	if (!price || (realEstateType === RealEstateType.LAND && !landArea) || (realEstateType !== RealEstateType.LAND && !area)) return null;
	const selectedArea = realEstateType === RealEstateType.LAND ? landArea : area;

	return <span className="u-color-grey u-font-sm">{formatNumber(price / selectedArea)} Kč/m²</span>;
};

OfferPriceM2.displayName = 'OfferPriceM2';
