import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import { Offer } from 'types/offer';
import { formatDateTime } from 'utils/formats';

type Props = {
	realEstate: Offer;
};

export const OfferSource: FC<Props> = ({ realEstate }) => {
	return (
		<p className="u-font-xs u-color-grey-light">
			<FormattedMessage
				description="detail-foundAt"
				defaultMessage="Nabídka nalezena na {origin} {datetime}"
				values={{ origin: realEstate.origin, datetime: formatDateTime(new Date(realEstate.createdAt)) }}
			/>
		</p>
	);
};
