import { ItemIcon } from 'components/ui/core/ItemIcon';
import { FC } from 'react';
import { useIntl } from 'react-intl';
import { Offer, OfferType } from 'types/offer';
import { formatNumber } from 'utils/formats';
import { filterInltMessages } from '../filter/filterIntlMessages';

type Props = Offer;

export const OfferPrice: FC<Props> = ({ price, priceText, priceNote, adType }) => {
	const { formatMessage } = useIntl();
	const { monthlyIntlString } = filterInltMessages;
	const nopriceIntlString = formatMessage({ description: 'offerPrice-noPriceInfo', defaultMessage: 'Žádné informace o ceně' });
	const priceFormatted = price ? formatNumber(price) : priceText || priceNote || nopriceIntlString;
	return (
		<ItemIcon
			name="money"
			text={`${priceFormatted}${price ? ' Kč' : ''}${adType === OfferType.RENT ? ` ${formatMessage(monthlyIntlString)}` : ''}`}
		/>
	);
};

OfferPrice.displayName = 'OfferPrice';
