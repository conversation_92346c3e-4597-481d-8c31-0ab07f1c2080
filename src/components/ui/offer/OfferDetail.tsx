import { RealEstateLocation } from 'components/ui/filter/RealEstateLocation';
import { OfferPrice } from 'components/ui/offer/OfferPrice';
import { OfferPriceM2 } from 'components/ui/offer/OfferPriceM2';
import { OfferTitle } from 'components/ui/offer/OfferTitle';
import { useUser } from 'components/user/UserProvider';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import { RealEstateType } from 'types/filter';
import { Offer, OfferType } from 'types/offer';
import { OfferSource } from './OfferSource';

export const OfferDetail: FC<Offer> = (realEstate) => {
	const { isPayingUser } = useUser();

	return (
		<div className="b-product-detail u-mb-sm">
			<div className="b-product-detail__img">
				{isPayingUser ? (
					<a href={realEstate.url} target="_blank" rel="noopener noreferrer">
						<img src={realEstate.imageUrl} loading="lazy" decoding="async" alt="" width="545" height="400" />
					</a>
				) : (
					<img src={realEstate.imageUrl} loading="lazy" decoding="async" alt="" width="545" height="400" />
				)}
			</div>
			<div className="b-product-detail__content">
				<h1 className="b-product-detail__title h4">
					<OfferTitle {...realEstate} />
				</h1>
				{realEstate.location && (
					<p className="b-product-detail__address">
						<RealEstateLocation location={realEstate.location} />
					</p>
				)}
				<p className="b-product-detail__details">
					<OfferPrice {...realEstate} />
					{realEstate.adType === OfferType.SALE &&
						(realEstate.realEstateType === RealEstateType.APARTMENT || realEstate.realEstateType === RealEstateType.LAND) && (
							<OfferPriceM2 {...realEstate} />
						)}
				</p>
				<OfferSource realEstate={realEstate} />
				{!isPayingUser && (
					<p>
						<a href={realEstate.url} className="btn" target="_blank" rel="noopener noreferrer">
							<span className="btn__text">
								<FormattedMessage description="detail-contact-seller" defaultMessage="Kontaktovat prodejce" />
							</span>
						</a>
					</p>
				)}
			</div>
		</div>
	);
};

OfferDetail.displayName = 'OfferDetail';
