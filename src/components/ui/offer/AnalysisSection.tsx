import { LinkToHistorySection } from 'components/insertions/LinkToHistorySection';
import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import {
	CityPerformerStats,
	Offer,
	OfferType,
	PossibleOfferTypeForDetailPage,
	StatsPayload,
	ValuoStats,
	VisidooPriceStats,
} from 'types/offer';
import { formatCurrency } from 'utils/formats';
import { mean } from 'utils/statistics';
import { Message } from '../core/Message';

type Props = {
	valuoStats: StatsPayload<ValuoStats> | undefined;
	visidooPriceStats: StatsPayload<VisidooPriceStats> | undefined;
	cityPerformerStats: StatsPayload<CityPerformerStats> | undefined;
	realEstate: Offer;
	onHistoryClick: (offerType: PossibleOfferTypeForDetailPage) => void;
	switchToDetailView?: () => void;
};

export const AnalysisSection: FC<Props> = ({ visidooPriceStats, realEstate, onHistoryClick }) => {
	const currency = 'CZK'; // FIXME - Get currency from new price field

	const getPriceDiff = (value: number, denominator: number | null | undefined | (number | null | undefined)[]) => {
		denominator = Array.isArray(denominator)
			? mean(denominator.filter((item): item is number => typeof item === 'number'))
			: denominator;

		if (!denominator) return;

		return {
			value: Math.abs(value / denominator - 1),
			positive: value > denominator,
		};
	};

	const priceContent = (
		<>
			{visidooPriceStats ? (
				<div className="u-mb-sm">
					<div className="b-box u-font-sm u-mb-smd">
						{visidooPriceStats?.stats && (
							<div className="b-box__inner u-mb-last-0">
								{realEstate.price ? (
									realEstate.adType === OfferType.SALE && visidooPriceStats.stats.sale.average ? (
										<p>
											<FormattedMessage
												description="detail-PriceDiff"
												defaultMessage="Cena je o <strong>{value, number, ::% x100 .##} {positive, select, true {dražší} other {levnější}}</strong>, než podobné nabídky v této lokalitě."
												values={getPriceDiff(realEstate.price, visidooPriceStats.stats.sale.average)}
											/>
										</p>
									) : realEstate.adType === OfferType.RENT && visidooPriceStats.stats.rent.average ? (
										<p>
											<FormattedMessage
												description="detail-PriceDiff"
												defaultMessage="Cena je o <strong>{value, number, ::% x100 .##} {positive, select, true {dražší} other {levnější}}</strong>, než podobné nabídky v této lokalitě"
												values={getPriceDiff(realEstate.price, visidooPriceStats.stats.rent.average)}
											/>
										</p>
									) : null
								) : null}
								{realEstate.adType === OfferType.SALE && visidooPriceStats.stats.sale.range ? (
									<p>
										<FormattedMessage
											description="detail-priceFromTo"
											defaultMessage="Podobné nemovitosti v této lokalitě se {adType, select, Sale {prodávají} Rent {pronajímají} other {nabízí}} <strong>od {formattedFrom} do {formattedTo}</strong>."
											values={{
												adType: realEstate.adType,
												formattedFrom: formatCurrency(visidooPriceStats.stats.sale.range[0], currency),
												formattedTo: formatCurrency(visidooPriceStats.stats.sale.range[1], currency),
											}}
										/>{' '}
										{visidooPriceStats.stats.sale.average && visidooPriceStats.stats.sale.median && (
											<FormattedMessage
												description="detail-priceMeanMedian"
												defaultMessage="(průměr {formattedMean}, medián {formattedMedian})"
												values={{
													formattedMean: formatCurrency(visidooPriceStats.stats.sale.average, currency),
													formattedMedian: formatCurrency(visidooPriceStats.stats.sale.median, currency),
												}}
											/>
										)}
									</p>
								) : realEstate.adType === OfferType.RENT && visidooPriceStats.stats.rent.range ? (
									<p>
										<FormattedMessage
											description="detail-priceFromTo"
											defaultMessage="Podobné nemovitosti v této lokalitě se {adType, select, Sale {prodávají} Rent {pronajímají} other {nabízí}} <strong>od {formattedFrom} do {formattedTo}</strong>."
											values={{
												adType: realEstate.adType,
												formattedFrom: formatCurrency(visidooPriceStats.stats.rent.range[0], currency),
												formattedTo: formatCurrency(visidooPriceStats.stats.rent.range[1], currency),
											}}
										/>{' '}
										{visidooPriceStats.stats.rent.average && visidooPriceStats.stats.rent.median && (
											<FormattedMessage
												description="detail-priceMeanMedian"
												defaultMessage="(průměr {formattedMean}, medián {formattedMedian})"
												values={{
													formattedMean: formatCurrency(visidooPriceStats.stats.rent.average, currency),
													formattedMedian: formatCurrency(visidooPriceStats.stats.rent.median, currency),
												}}
											/>
										)}
									</p>
								) : null}
								{realEstate.adType === OfferType.SALE && visidooPriceStats.stats.rent.range && (
									<p>
										<FormattedMessage
											description="detail-rentFromTo"
											defaultMessage="{realEstateType, select, Apartment {Tento byt} House {Tento dům} Land {Tento pozemek} other {Tuto nemovitost}} můžete pronajmout <strong>od {formattedFrom} do {formattedTo}</strong>."
											values={{
												realEstateType: realEstate.realEstateType,
												formattedFrom: formatCurrency(visidooPriceStats.stats.rent.range[0], currency),
												formattedTo: formatCurrency(visidooPriceStats.stats.rent.range[1], currency),
											}}
										/>
										{visidooPriceStats.stats.rent.average && visidooPriceStats.stats.rent.median && (
											<FormattedMessage
												description="detail-priceMeanMedian"
												defaultMessage="(průměr {formattedMean}, medián {formattedMedian})"
												values={{
													formattedMean: formatCurrency(visidooPriceStats.stats.rent.average, currency),
													formattedMedian: formatCurrency(visidooPriceStats.stats.rent.median, currency),
												}}
											/>
										)}
									</p>
								)}
								{visidooPriceStats.stats.sale.sample > 0 && (
									<LinkToHistorySection
										onClick={(e) => {
											e.preventDefault();
											onHistoryClick(OfferType.SALE);
										}}
										scope={'sale'}
										visidooPriceStats={visidooPriceStats}
									/>
								)}

								{visidooPriceStats.stats.rent.sample > 0 && (
									<LinkToHistorySection
										onClick={(e) => {
											e.preventDefault();
											onHistoryClick(OfferType.RENT);
										}}
										scope={'rent'}
										visidooPriceStats={visidooPriceStats}
									/>
								)}
								<p className="u-font-sm u-color-grey">
									<FormattedMessage
										description="detail-VisidooDesc"
										defaultMessage="Na základě nalezených inzerátů za poslední 3 měsíce."
									/>
								</p>
							</div>
						)}
					</div>
				</div>
			) : (
				<Message variant="warning" icon="exclamation" noclose>
					<FormattedMessage
						description="detail-missingPriceData"
						defaultMessage="Bohužel nemáme k dispozici informace o cenách."
					/>
				</Message>
			)}
		</>
	);

	return (
		<>
			<h2 className="h5">
				<FormattedMessage description="detail-priceAnalysisTitle" defaultMessage="Visidoo analýza ceny" />
			</h2>

			{priceContent}
		</>
	);
};
