import { PhotoCarousel } from 'components/insertions/PhotoCarousel';
import { RealEstateLocation } from 'components/ui/filter/RealEstateLocation';
import { OfferPrice } from 'components/ui/offer/OfferPrice';
import { OfferPriceM2 } from 'components/ui/offer/OfferPriceM2';
import { OfferTitleNonPublic } from 'components/ui/offer/OfferTitleNonPublic';
import { FC } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { RealEstateType } from 'types/filter';
import { Offer, OfferType } from 'types/offer';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { formatMeter } from 'utils/formats';
import { ownershipTypeMessageMap, conditionTypeMessageMap, buildingTypeMessageMap, teraceTypeMessageMap } from 'i18n/intlMessageMaps';

const { yesIntlString } = commonInltMessages;

export const OfferDetailNonPublic: FC<Offer> = (realEstate) => {
	const { formatMessage } = useIntl();

	return (
		<div className="b-product-detail u-mb-sm">
			<div className="b-product-detail__img">
				<PhotoCarousel offer={realEstate}>
					<span className="b-offer__flag3">
						<span className="flag flag--purple">
							<span className="flag__inner">
								<span className="flag__text">
									<FormattedMessage description="detail-nonpublic-title" defaultMessage="Neveřejná nabídka" />
								</span>
							</span>
						</span>
					</span>
				</PhotoCarousel>
			</div>
			<div className="b-product-detail__content">
				<h2 className="b-product-detail__title h4">
					<OfferTitleNonPublic {...realEstate} />
				</h2>
				{realEstate.location && (
					<p className="b-product-detail__address">
						<RealEstateLocation location={realEstate.location} />
					</p>
				)}
				<p className="b-product-detail__details">
					<OfferPrice {...realEstate} />
					{realEstate.adType === OfferType.SALE &&
						(realEstate.realEstateType === RealEstateType.APARTMENT || realEstate.realEstateType === RealEstateType.LAND) && (
							<OfferPriceM2 {...realEstate} />
						)}
				</p>
			</div>

			{realEstate.extended?.description && (
				<div className="u-font-sm u-mb-xs" dangerouslySetInnerHTML={{ __html: realEstate.extended.description }}></div>
			)}

			<dl className="two-columns">
				{realEstate.extended?.ownershipType
					? handleFeaturePresentation(
							formatMessage({ defaultMessage: 'Vlastnictví', description: 'offerDetail-ownershipType' }),
							formatMessage(ownershipTypeMessageMap[realEstate.extended.ownershipType]),
					  )
					: null}

				{realEstate.extended?.conditionType
					? handleFeaturePresentation(
							formatMessage({ defaultMessage: 'Stav', description: 'offerDetail-conditionType' }),
							formatMessage(conditionTypeMessageMap[realEstate.extended.conditionType]),
					  )
					: null}

				{realEstate.disposition
					? handleFeaturePresentation(
							formatMessage({ defaultMessage: 'Dispozice', description: 'offerDetail-disposition' }),
							realEstate.disposition,
					  )
					: null}

				{realEstate.extended?.buildingType
					? handleFeaturePresentation(
							formatMessage({ defaultMessage: 'Konstrukce', description: 'offerDetail-buildingType' }),
							formatMessage(buildingTypeMessageMap[realEstate.extended.buildingType]),
					  )
					: null}

				{realEstate.area
					? handleFeaturePresentation(
							formatMessage({ defaultMessage: 'Užitná plocha', description: 'offerDetail-area' }),
							`${formatMeter(realEstate.area)}²`,
					  )
					: null}

				{realEstate.extended?.terraceType
					? handleFeaturePresentation(
							formatMessage(teraceTypeMessageMap[realEstate.extended.terraceType]),
							formatMessage(yesIntlString),
					  )
					: null}

				{realEstate.extended?.elevator
					? handleFeaturePresentation(
							formatMessage({ defaultMessage: 'Výtah', description: 'offerDetail-elevator' }),
							formatMessage(yesIntlString),
					  )
					: null}

				{realEstate.extended?.basement
					? handleFeaturePresentation(
							formatMessage({ defaultMessage: 'Sklep', description: 'offerDetail-basement' }),
							formatMessage(yesIntlString),
					  )
					: null}
			</dl>
		</div>
	);
};

OfferDetailNonPublic.displayName = 'OfferDetailNonPublic';

const handleFeaturePresentation = (label: string, feature: string) => {
	return (
		<div>
			<dt>{label}:</dt>
			<dd>{feature}</dd>
		</div>
	);
};
