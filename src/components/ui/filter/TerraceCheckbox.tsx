import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import { Checkbox } from '../core/Checkbox';
import { useUser } from 'components/user/UserProvider';

export const TerraceCheckbox: FC = () => {
	const { payload, loggedIn } = useUser();

	const showBadgeStandard = (loggedIn && payload.role >= 5) || !loggedIn;

	return (
		<>
			<Checkbox
				onlyPremium={!showBadgeStandard}
				label={
					<>
						<FormattedMessage description={'filterForm-withTerrace'} defaultMessage={'S terasou, lodžií nebo balkonem'} />
					</>
				}
				name="withTerraceOnly"
				showBadgePremium={true}
			/>
		</>
	);
};
