import { FC } from 'react';
import { useIntl } from 'react-intl';
import { CheckboxVariant } from 'types/common';
import { LandType } from 'types/filter';
import { CheckboxOrRadioGroup } from '../core/CheckboxOrRadioGroup';
import { filterInltMessages } from './filterIntlMessages';

type Props = {
	variant?: CheckboxVariant;
	required?: boolean;
	name?: string;
};

export const LandTypeSelector: FC<Props> = ({ variant = 'multiple-choice', required = false, name = 'landType' }) => {
	const { formatMessage } = useIntl();
	const isMultipleChoice = variant === 'multiple-choice';
	const { landTypeIntlString, commercialIntlString, forLivingIntlString, otherIntlString, everythingIntlString } = filterInltMessages;

	return (
		<CheckboxOrRadioGroup
			items={[
				{ value: LandType.BUILD_PLOT, label: formatMessage(forLivingIntlString) },
				{ value: LandType.COMMERCIAL, label: formatMessage(commercialIntlString) },
				{ value: LandType.OTHER, label: formatMessage(otherIntlString) },
			]}
			label={formatMessage(landTypeIntlString)}
			emptyLabel={isMultipleChoice ? formatMessage(everythingIntlString) : undefined}
			name={name}
			size="md"
			inputType={isMultipleChoice ? 'checkbox' : 'radio'}
			required={required}
		/>
	);
};
