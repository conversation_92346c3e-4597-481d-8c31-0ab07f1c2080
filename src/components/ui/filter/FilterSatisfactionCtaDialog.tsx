import { Dialog } from 'components/ui/dialog/Dialog';
import { FilterSatisfactionCta } from 'components/ui/filter/FilterSatisfactionCta';
import { FC } from 'react';

type Props = {
	isOpen: boolean;
	close: () => void;
	filterId: string;
};

export const FilterSatisfactionCtaDialog: FC<Props> = ({ isOpen, close }) => {
	return (
		<Dialog isOpen={isOpen} close={close}>
			<FilterSatisfactionCta />
		</Dialog>
	);
};

FilterSatisfactionCtaDialog.displayName = 'FilterSatisfactionCtaDialog';
