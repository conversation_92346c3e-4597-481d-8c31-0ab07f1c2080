import { fetchFilterEstimate } from 'api/filter/fetchFilterEstimate';
import clsx from 'clsx';
import { Inputs } from 'components/ui/filter/FilterForm';
import { Tooltip } from 'components/ui/tooltip/Tooltip';
import { FC, useEffect, useState } from 'react';
import { DeepPartial, useFormContext } from 'react-hook-form';
import { Estimate, EstimateRequest, Filter } from 'types/filter';
import { FormattedMessage } from 'react-intl';
import { DateRange } from 'types/time';

const watchedInputs: (keyof Inputs)[] = [
	'adType',
	'agent',
	'area.gte',
	'area.lte',
	'disposition',
	'elevator',
	'exactMatch',
	'excludedOrigins',
	'houseType',
	'landArea.gte',
	'landArea.lte',
	'landType',
	'locationId',
	'ownershipType',
	'price.gte',
	'price.lte',
	'radius',
	'realEstateType',
	'terraceType',
];

type Props = {
	filterValues?: Partial<Filter>;
};

export const EstimateTooltip: FC<Props> = ({ filterValues }) => {
	const [estimate, setEstimate] = useState<Estimate>({ range: DateRange.DAY, value: 0 });
	const [estimateLoading, setEstimateLoading] = useState(false);
	const { watch } = useFormContext<Inputs>();

	const DailyFormattedMessage = <FormattedMessage description={'estimateTooltip-daily'} defaultMessage={'denně'} />;
	const WeeklyFormattedMessage = <FormattedMessage description={'estimateTooltip-weekly'} defaultMessage={'týdně'} />;
	const MonthlyFormattedMessage = <FormattedMessage description={'estimateTooltip-monthly'} defaultMessage={'měsíčně'} />;
	const YearlyFormattedMessage = <FormattedMessage description={'estimateTooltip-yearly'} defaultMessage={'ročně'} />;

	const fetchEstimate = async (data: DeepPartial<Inputs>) => {
		if (data.locationId && data.locationId.length > 0) {
			setEstimateLoading(true);
			try {
				const response = await fetchFilterEstimate(data as EstimateRequest);
				setEstimate(response.data.estimate);
			} catch (e) {
				// Show message
			}
			setEstimateLoading(false);
		}
	};

	useEffect(() => {
		const subscription = watch((values, { name }) => {
			if (name && watchedInputs.includes(name as keyof Inputs)) {
				fetchEstimate(values);
			}
		});
		return () => subscription.unsubscribe();
	}, [watch]);

	useEffect(() => {
		if (filterValues) {
			fetchEstimate({ ...filterValues, locationId: filterValues?.locationId ?? filterValues?.location?.id } as unknown as Inputs);
		}
	}, [filterValues]);

	return (
		<Tooltip
			title={
				estimate.value === 0 ? (
					<>
						<FormattedMessage
							description={'estimateTooltip-offerProbability'}
							defaultMessage={'Pravděpodobnost obdržení nabídky <strong>je nízká</strong> '}
							values={{
								strong: (...chunks: string[]) => (
									<strong className={clsx('inline-loader', estimateLoading && 'is-loading')}>{chunks}</strong>
								),
							}}
						/>{' '}
					</>
				) : (
					<>
						<FormattedMessage
							description={'estimateTooltip-resultsEstimate'}
							defaultMessage={
								'cca <strong>{count} {count, plural, one {nemovitost} few {nemovitosti} other {nemovitostí}}</strong>'
							}
							values={{
								count: estimate.value,
								strong: (...chunks: string[]) => (
									<strong className={clsx('inline-loader', estimateLoading && 'is-loading')}>{chunks}</strong>
								),
							}}
						/>{' '}
						{estimate.range === DateRange.DAY
							? DailyFormattedMessage
							: estimate.range === DateRange.WEEK
							? WeeklyFormattedMessage
							: estimate.range === DateRange.MONTH
							? MonthlyFormattedMessage
							: YearlyFormattedMessage}
					</>
				)
			}
		>
			{estimate.value === 0 ? (
				<FormattedMessage
					description={'estimateTooltip-expandFilters'}
					defaultMessage={'Pro více nabídek, můžete více zobecnit filtr'}
				/>
			) : (
				<FormattedMessage description={'estimateTooltip-sentEmails'} defaultMessage={'Předpoklad zaslaných e‑mailů'} />
			)}
		</Tooltip>
	);
};

EstimateTooltip.displayName = 'EstimateTooltip';
