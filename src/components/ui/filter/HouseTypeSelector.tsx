import { FC } from 'react';
import { useIntl } from 'react-intl';
import { CheckboxVariant } from 'types/common';
import { HouseType } from 'types/offer';
import { CheckboxOrRadioGroup } from '../core/CheckboxOrRadioGroup';
import { filterInltMessages } from './filterIntlMessages';

type Props = {
	variant?: CheckboxVariant;
	required?: boolean;
	name?: string;
};

export const HouseTypeSelector: FC<Props> = ({ variant = 'multiple-choice', required = false, name = 'houseType' }) => {
	const { formatMessage } = useIntl();
	const isMultipleChoice = variant === 'multiple-choice';
	const {
		houseTypeIntlString,
		terracedHouseIntlString,
		detachedHouseIntlString,
		cottageHouseIntlString,
		hutHouseIntlString,
		everythingIntlString,
	} = filterInltMessages;

	return (
		<CheckboxOrRadioGroup
			items={[
				{ value: HouseType.TERRACED, label: formatMessage(terracedHouseIntlString) },
				{ value: HouseType.DETACHED, label: formatMessage(detachedHouseIntlString) },
				{ value: HouseType.COTTAGE, label: formatMessage(cottageHouseIntlString) },
				{ value: HouseType.HUT, label: formatMessage(hutHouseIntlString) },
			]}
			label={formatMessage(houseTypeIntlString)}
			emptyLabel={isMultipleChoice ? formatMessage(everythingIntlString) : undefined}
			name={name}
			size="auto"
			inputType={isMultipleChoice ? 'checkbox' : 'radio'}
			required={required}
		/>
	);
};
