import { defineMessages } from 'react-intl';

export const filterInltMessages = defineMessages({
	saleIntlString: {
		description: 'filterForm-tabName-sale',
		defaultMessage: 'Prodej',
	},
	rentIntlString: {
		description: 'filterForm-tabName-rent',
		defaultMessage: 'Pronájem',
	},
	roommatesIntlString: {
		description: 'filterForm-tabName-roommates',
		defaultMessage: 'Spolubydlení',
	},
	moreConcreteIntlString: {
		description: 'filterForm-moreConcrete',
		defaultMessage: 'K tomuto novému filtru jsme u vás nalezli jiný konkrétnější, který jsme vám tímto nahradili.',
	},
	alreadyReceivingIntlString: {
		description: 'filterForm-alreadyReceiving',
		defaultMessage: 'Pro tento výběr už od nás upozornění dostáváte.',
	},
	receivingFromAnotherIntlString: {
		description: 'filterForm-receivingFromAnother',
		defaultMessage: 'Upozornění pro tento výběr už od nás dostáváte na základě jiného Vašeho filtru.',
	},
	apartmentIntlString: {
		description: 'filterForm-apartment',
		defaultMessage: 'Byt',
	},
	houseIntlString: {
		description: 'filterForm-house',
		defaultMessage: 'Dům',
	},
	landIntlString: {
		description: 'filterForm-land',
		defaultMessage: 'Pozemek',
	},
	elevatorIntlString: {
		description: 'filterForm-elevator',
		defaultMessage: 'Pouze s výtahem',
	},
	enterLocationIntlString: {
		description: 'filterForm-enterLocation',
		defaultMessage: 'V jaké lokalitě?',
	},
	otherIntlString: {
		description: 'filterForm-other',
		defaultMessage: 'Ostatní',
	},
	layoutIntlString: {
		description: 'filterForm-layout',
		defaultMessage: 'Dispozice',
	},
	forLivingIntlString: {
		description: 'filterForm-forLiving',
		defaultMessage: 'Pro bydlení',
	},
	commercialIntlString: {
		description: 'filterForm-commercial',
		defaultMessage: 'Komerční',
	},
	landTypeIntlString: {
		description: 'filterForm-landType',
		defaultMessage: 'Druh pozemku',
	},
	houseTypeIntlString: {
		description: 'filterForm-houseType',
		defaultMessage: 'Typ domu',
	},
	priceIntlString: {
		description: 'filterForm-price',
		defaultMessage: 'Cena',
	},
	monthlyIntlString: {
		description: 'filterForm-monthly',
		defaultMessage: 'za měsíc',
	},
	propertyIntlString: {
		description: 'filterForm-property',
		defaultMessage: 'nemovitosti',
	},
	offersNopriceIntlString: {
		description: 'filterForm-offersNoPrice',
		defaultMessage: 'Zasílejte mi i nabídky bez uvedené ceny',
	},
	areaSqmIntlString: {
		description: 'filterForm-areaSqm',
		defaultMessage: 'Plocha v m²',
	},
	landareaSqmIntlString: {
		description: 'filterForm-landAreaSqm',
		defaultMessage: 'Plocha pozemku v m²',
	},
	noSpamIntlString: {
		description: 'filterForm-noSpam',
		defaultMessage: 'Nebojte, žádný spam neposíláme',
	},
	emailFormatIntlString: {
		description: 'filterForm-emailFormat',
		defaultMessage: 'Zadejte prosím e-mail ve správném formátu, např. <EMAIL>',
	},
	editFiltersIntlString: {
		description: 'filterForm-editFilters',
		defaultMessage: 'Upravit filtr',
	},
	trackSearchIntlString: {
		description: 'filterForm-trackSearch',
		defaultMessage: 'Hlídat nemovitosti',
	},
	forceCreateIntlString: {
		description: 'filterForm-forceCreate',
		defaultMessage: 'Ano, vytvořit',
	},
	editSearchIntlString: {
		description: 'filterForm-editSearch',
		defaultMessage: 'Upravit hledání',
	},
	bedsIntlString: {
		description: 'filterForm-1bed',
		defaultMessage: '{count} {count, plural, one {lůžko} few {lůžka} other {lůžek}}',
	},
	separateRoomIntlString: {
		description: 'filterForm-room',
		defaultMessage: 'Samostatný pokoj',
	},
	terracedHouseIntlString: {
		description: 'filterForm-terracedHouse',
		defaultMessage: 'Řadový',
	},
	detachedHouseIntlString: {
		description: 'filterForm-detachedHouse',
		defaultMessage: 'Samostatný',
	},
	cottageHouseIntlString: {
		description: 'filterForm-cottageHouse',
		defaultMessage: 'Chalupa',
	},
	hutHouseIntlString: {
		description: 'filterForm-hutHouse',
		defaultMessage: 'Chata',
	},
	everythingIntlString: {
		description: 'filterForm-everything',
		defaultMessage: 'Všechny',
	},
});
