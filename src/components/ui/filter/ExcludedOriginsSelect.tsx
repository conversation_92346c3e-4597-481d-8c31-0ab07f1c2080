import { fetchScraperData } from 'api/realEstate/fetchScraperData';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { FC, useCallback, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { SelectItem } from 'types/common';
import { ScraperData, ScraperLocale } from 'types/admin';
import { MultiSelect } from '../core/MultiSelect';

const { wantAllIntlString } = commonInltMessages;

type Props = {
	locale: ScraperLocale;
};

export const ExcludedOriginsSelect: FC<Props> = ({ locale }) => {
	const { formatMessage } = useIntl();
	const [availableOrigins, setAvailableOrigins] = useState<ScraperData[]>([]);

	const options: SelectItem[] = availableOrigins.map((origin) => ({
		value: origin.url,
		label: origin.name,
	}));

	const loadOrigins = useCallback(async () => {
		try {
			const res = await fetchScraperData(locale);
			if (res.statusCode !== 200 || !res.data.scrapers) {
				throw new Error();
			}

			setAvailableOrigins(res.data.scrapers);
		} catch (error) {
			setAvailableOrigins([]);
		}
	}, [locale]);

	useEffect(() => {
		loadOrigins();
	}, [loadOrigins]);

	return (
		<MultiSelect
			name="excludedOrigins"
			label={formatMessage({ defaultMessage: 'Nechci nabídky z těchto stránek', description: 'filterForm-excludedOrigins' })}
			items={options}
			anyChoiceMessage={wantAllIntlString}
		/>
	);
};
