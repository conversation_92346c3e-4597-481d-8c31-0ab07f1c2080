import { FC } from 'react';
import { useIntl } from 'react-intl';
import { AREA_HIGH_DEFAULT } from 'types/filter';
import { RangeSlider } from '../core/RangeSlider';
import { filterInltMessages } from './filterIntlMessages';

export const AreaSelector: FC = () => {
	const { areaSqmIntlString } = filterInltMessages;
	const { formatMessage } = useIntl();

	return <RangeSlider label={formatMessage(areaSqmIntlString)} max={AREA_HIGH_DEFAULT} name="area" unit="m²" focus="gte" />;
};
