import { FC } from 'react';
import { useIntl } from 'react-intl';
import { CheckboxVariant } from 'types/common';
import { CheckboxOrRadioGroup } from '../core/CheckboxOrRadioGroup';
import { filterInltMessages } from './filterIntlMessages';

type Props = {
	variant?: CheckboxVariant;
	required?: boolean;
	name?: string;
};

export const DispositionSelector: FC<Props> = ({ variant = 'multiple-choice', required = false, name = 'disposition' }) => {
	const { formatMessage } = useIntl();
	const { layoutIntlString, everythingIntlString } = filterInltMessages;
	const isMultipleChoice = variant === 'multiple-choice';

	return (
		<CheckboxOrRadioGroup
			items={[
				{ value: '1+1', label: '1 + 1' },
				{ value: '1+kk', label: '1 + kk' },
				{ value: '2+1', label: '2 + 1' },
				{ value: '2+kk', label: '2 + kk' },
				{ value: '3+1', label: '3 + 1' },
				{ value: '3+kk', label: '3 + kk' },
				{ value: '4+1', label: '4 + 1' },
				{ value: '4+kk', label: '4 + kk' },
				{ value: '5+1', label: '5 + 1' },
				{ value: '5+kk', label: '5 + kk' },
				{ value: 'Other', label: 'Ostatní' },
			]}
			label={formatMessage(layoutIntlString)}
			emptyLabel={isMultipleChoice ? formatMessage(everythingIntlString) : undefined}
			name={name}
			inputType={isMultipleChoice ? 'checkbox' : 'radio'}
			required={required}
		/>
	);
};
