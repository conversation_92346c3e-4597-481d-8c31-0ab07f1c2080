import { Dialog } from 'components/ui/dialog/Dialog';
import { FilterForm } from 'components/ui/filter/FilterForm';
import { FC } from 'react';
import { Filter } from 'types/filter';

type Props = {
	filterValues?: Filter;
	isOpen: boolean;
	close: () => void;
};

export const FilterFormDialog: FC<Props> = ({ filterValues, isOpen, close }) => (
	<Dialog isOpen={isOpen} close={close} className="b-dialog--md">
		<FilterForm filterValues={filterValues} dialogClose={close} />
	</Dialog>
);

FilterFormDialog.displayName = 'FilterFormDialog';
