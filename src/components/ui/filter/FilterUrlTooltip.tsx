import { Inputs } from 'components/ui/filter/FilterForm';
import { useUser } from 'components/user/UserProvider';
import { encodeURI } from 'js-base64';
import { FC, MouseEventHandler, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { ONE_SECOND } from 'utils/constants';

export const FilterUrlTooltip: FC = () => {
	const { formatMessage } = useIntl();
	const DEFAULT_TITLE = formatMessage({ defaultMessage: 'Zkopírovat url pro filtr', description: 'filter-copyUrl' });
	const COPIED_TITLE = formatMessage({ defaultMessage: 'Zkopírováno', description: 'filter-urlCopied' });

	const { isAdmin } = useUser();
	const { watch } = useFormContext<Inputs>();
	const [data, setData] = useState('');
	const [title, setTitle] = useState(DEFAULT_TITLE);

	const handleClick: Mouse<PERSON>vent<PERSON><PERSON><PERSON><HTMLAnchorElement> = async (event) => {
		event.preventDefault();
		await navigator.clipboard.writeText(`/lp-ppc?filter=${data}`);
		setTitle(COPIED_TITLE);
		setTimeout(() => {
			setTitle(DEFAULT_TITLE);
		}, 2 * ONE_SECOND);
	};

	useEffect(() => {
		const subscription = watch((values) => {
			setData(encodeURI(JSON.stringify({ ...values, email: '' })));
		});
		return () => subscription.unsubscribe();
	}, [watch]);

	if (!isAdmin) return null;

	return (
		<a href="#" onClick={handleClick}>
			{title}
		</a>
	);
};

FilterUrlTooltip.displayName = 'FilterUrlTooltip';
