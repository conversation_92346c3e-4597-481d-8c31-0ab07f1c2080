import { FC } from 'react';
import { useIntl } from 'react-intl';
import { LAND_AREA_HIGH_DEFAULT } from 'types/filter';
import { RangeSlider } from '../core/RangeSlider';
import { filterInltMessages } from './filterIntlMessages';

export const LandAreaSelector: FC = () => {
	const { landareaSqmIntlString } = filterInltMessages;
	const { formatMessage } = useIntl();

	return <RangeSlider label={formatMessage(landareaSqmIntlString)} max={LAND_AREA_HIGH_DEFAULT} name="landArea" unit="m²" focus="gte" />;
};
