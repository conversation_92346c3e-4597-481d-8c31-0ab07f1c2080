import { fetchFilterUpdate as fetchAdminFilterUpdate } from 'api/admin/fetchFilterUpdate';
import { fetchFilterUpdate } from 'api/filter/fetchFilterUpdate';
import { fetchNewFilter } from 'api/filter/fetchNewFilter';
import clsx from 'clsx';
import { contactTypes } from 'components/admin/ContactList';
import { Link } from 'components/Link';
import { FilterDataContext } from 'components/search/FilterDataProvider';
import { Button } from 'components/ui/core/Button';
import { Checkbox } from 'components/ui/core/Checkbox';
import { CheckboxOrRadioGroup } from 'components/ui/core/CheckboxOrRadioGroup';
import { EmailInput } from 'components/ui/core/EmailInput';
import { Form } from 'components/ui/core/Form';
import { Label } from 'components/ui/core/Label';
import { RangeSlider } from 'components/ui/core/RangeSlider';
import { SelectIcon } from 'components/ui/core/SelectIcon';
import { Suggest } from 'components/ui/core/Suggest';
import { EstimateTooltip } from 'components/ui/filter/EstimateTooltip';
import { filterInltMessages } from 'components/ui/filter/filterIntlMessages';
import { FilterUrlTooltip } from 'components/ui/filter/FilterUrlTooltip';
import { RadiusSlider } from 'components/ui/filter/RadiusSlider';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { Tooltip } from 'components/ui/tooltip/Tooltip';
import { useUser } from 'components/user/UserProvider';
import { useOpen } from 'hooks/useOpen';
import { useSelectIcons } from 'hooks/useSelectIcons';
import { defaultLocale } from 'i18n/supportedLocales';
import { useRouter } from 'next/router';
import { FC, useContext, useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage, useIntl } from 'react-intl';
import { ContactType } from 'types/admin';
import { SelectItem } from 'types/common';
import {
	Filter,
	FilterRelation,
	FilterRequest,
	FilterState,
	Location,
	PRICE_HIGH_RENT,
	PRICE_HIGH_ROOMMATES,
	PRICE_HIGH_SALE,
	RealEstateType,
} from 'types/filter';
import { OfferType, TerraceType } from 'types/offer';
import { identifyUser, trackEvent, trackFilterCount } from 'utils/analytics';
import { TEN_SECONDS } from 'utils/constants';
import { Dialog } from '../dialog/Dialog';
import { AreaSelector } from './AreaSelector';
import { DispositionSelector } from './DispositionSelector';
import { ExcludedOriginsSelect } from './ExcludedOriginsSelect';
import { HouseTypeSelector } from './HouseTypeSelector';
import { LandAreaSelector } from './LandAreaSelector';
import { LandTypeSelector } from './LandTypeSelector';
import { OwnershipTypeSelect } from './OwnershipTypeSelect';
import { RealEstateTypeSelector } from './RealEstateTypeSelector';
import { TerraceCheckbox } from './TerraceCheckbox';
import { Modal } from '../modal/Modal';
import { PreviewMap } from 'components/map/PreviewMap';
import { areValidPolygonCoords, Geometry, PolygonCoords } from 'types/map';
import { InteractiveMap } from 'components/map/InteractiveMap';
import { Polygon } from '@turf/turf';

type Props = {
	filterValues?: Partial<Filter>;
	dialogClose?: () => void; // Also indicates that we're editing our filter instead of creating a new one
	onFilterAdded?: () => void;
	redirectPath?: string;
	showExtra?: boolean;
};

export type Inputs = FilterRequest & {
	'area.gte': number;
	'area.lte': number | null;
	'landArea.gte': number;
	'landArea.lte': number | null;
	'price.gte': number;
	'price.lte': number | null;
	conditions: boolean;
	withTerraceOnly: boolean;
	force: boolean;
};

export const FilterForm: FC<Props> = ({ filterValues, dialogClose, onFilterAdded, redirectPath = '/dekujeme', showExtra }) => {
	const {
		trackSearchIntlString,
		editFiltersIntlString,
		forceCreateIntlString,
		alreadyReceivingIntlString,
		editSearchIntlString,
		noSpamIntlString,
		offersNopriceIntlString,
		propertyIntlString,
		monthlyIntlString,
		priceIntlString,
		enterLocationIntlString,
		rentIntlString,
		saleIntlString,
		everythingIntlString,
		elevatorIntlString,
	} = filterInltMessages;

	const router = useRouter();
	const isAdministration = router.route.startsWith('/administrace');
	const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
	const { token, payload, loggedIn, isAdvanced, isAdmin, isEnterprise, isProfi } = useUser();
	const showBadgeStandard = (loggedIn && payload.role >= 5) || !loggedIn;
	const { addNotification } = useNotifications();
	const { formatMessage } = useIntl();
	const defaultValues = {
		adType: filterValues?.adType ?? OfferType.SALE,
		agent: filterValues?.agent ?? [],
		realEstateType: filterValues?.realEstateType ?? RealEstateType.APARTMENT,
		locationId: (filterValues?.locationId ?? filterValues?.location?.id ?? '').split('__')[0],
		disposition: filterValues?.disposition ?? [],
		houseType: filterValues?.houseType ?? [],
		landType: filterValues?.landType ?? [],
		radius: filterValues?.radius ?? 0,
		area: filterValues?.area ?? {
			gte: 0,
			lte: null,
		},
		landArea: filterValues?.landArea ?? {
			gte: 0,
			lte: null,
		},
		price: filterValues?.price ?? {
			gte: 0,
			lte: null,
		},
		elevator: filterValues?.elevator ?? false,
		email: filterValues?.email ?? payload.email ?? '',
		recipientEmail: filterValues?.recipientEmail ?? payload.email ?? '',
		exactMatch: filterValues?.exactMatch ?? false,
		excludedOrigins: filterValues?.excludedOrigins ?? [],
		state: filterValues?.state ?? FilterState.ACTIVE,
		conditions: !!filterValues,
		timezone,
		locale: router.locale,
		createWebhook: filterValues?.createWebhook ?? null,
		deleteWebhook: filterValues?.deleteWebhook ?? null,
		updateWebhook: filterValues?.updateWebhook ?? null,
		withTerraceOnly: !!(filterValues?.terraceType && filterValues.terraceType.length > 0),
		terraceType: filterValues?.terraceType ?? [],
		ownershipType: filterValues?.ownershipType ?? [],
		force: false,
	};

	const formContext = useForm<Inputs>({ defaultValues });
	const {
		formState: { isSubmitting, errors },
		register,
		setValue,
		watch,
	} = formContext;

	const [isRadiusVisible, setRadiusVisible] = useState(true);
	const [extraVisible, setExtraVisible] = useState<boolean>(typeof dialogClose !== 'undefined');
	const [isMapModalOpen, setIsMapModalOpen] = useState(false);
	// const [customLocationCoords, setCustomLocationCoords] = useState<PolygonCoords | null>(null);

	const watchAdType = watch('adType');
	const watchRealEstateType = watch('realEstateType');
	const watchLocation = watch('locationId');
	const watchEmail = watch('email');
	const watchRecipientEmail = watch('recipientEmail');
	const watchTerraceOnlyCheckbox = watch('withTerraceOnly');
	const watchCustomLocationCoords = watch('customLocationCoords');
	const customLocationCoordsAreValid = areValidPolygonCoords(watchCustomLocationCoords);

	const { changeData, updateFilter } = useContext(FilterDataContext);
	const { isOpen: isOpenForceCreationModal, open: openForceCrationModal, close: closeForceCreationModal } = useOpen();
	const selectIcons: SelectItem[] = useSelectIcons({ fullSet: watchAdType !== OfferType.ROOMMATES });

	const closeMapModal = () => setIsMapModalOpen(false);

	const onSubmit: SubmitHandler<Inputs> = async (values) => {
		const { customLocationCoords, ...rest } = values;
		const data = { ...defaultValues, ...rest };

		if (!data.recipientEmail) {
			data.recipientEmail = data.email;
		}

		const isSendingCustomCoords = areValidPolygonCoords(customLocationCoords);

		if (isSendingCustomCoords) {
			const polygon: Polygon = {
				type: 'Polygon',
				coordinates: customLocationCoords as PolygonCoords,
			};
			const response = await fetchNewFilter({ ...data, customLocationCoords: polygon, locationId: undefined });
			console.log('🚀 ~ onSubmit ~ response:', response);

			return;
		}

		console.log('🚀 ~I WILL SEND GEOs:', data);

		if (filterValues && filterValues.id && dialogClose) {
			let response;
			if (isAdministration) {
				response = await fetchAdminFilterUpdate(filterValues.id, data, token);
			} else {
				response = await fetchFilterUpdate(filterValues.id, data, token);
			}
			updateFilter && updateFilter(response.data.filter, filterValues.id);
			trackEvent({ category: 'filter_form', action: 'update', label: response.data.filter.id });
			const activeFilters = response.data.user?.activeFilters;
			if (activeFilters) {
				trackFilterCount(activeFilters);
			}
			dialogClose();
		} else {
			const response = await fetchNewFilter(data);

			if (
				response.statusCode === 201 ||
				(response.statusCode === 200 &&
					response.data.filter.state === FilterState.ACTIVE &&
					response.data.originalFilterState !== FilterState.ACTIVE)
			) {
				changeData && changeData(response.data.filter);
				identifyUser(loggedIn, response.data.user?.state);
				localStorage.setItem('visidoo:userInfo', JSON.stringify(response.data.user));

				const activeFilters = response.data.user?.activeFilters;
				if (activeFilters) {
					trackFilterCount(activeFilters);
				}

				const totalFilters = response.data.user?.totalFilters;
				if (totalFilters) {
					trackEvent({ category: 'filter_form', action: 'create', label: totalFilters === 1 ? 'first' : 'repeate' });
				}

				const relation = response.data.relation;
				if (relation && relation === FilterRelation.CONTAINS && response.data.originalFilterState === FilterState.ACTIVE) {
					openForceCrationModal();
				}

				if (onFilterAdded) {
					onFilterAdded();
				} else {
					await router.push({
						pathname: redirectPath,
						query: { isSubscribed: response.data.user?.isSubscribed ?? false },
					});
				}
			}

			if (response.statusCode === 200) {
				const relation = response.data.relation;
				if (relation) {
					if (relation === FilterRelation.EXACT_MATCH && response.data.originalFilterState === FilterState.ACTIVE) {
						addNotification({
							timeout: TEN_SECONDS,
							message: formatMessage(alreadyReceivingIntlString),
						});
					}
					if (relation === FilterRelation.WITHIN && response.data.originalFilterState === FilterState.ACTIVE) {
						openForceCrationModal();
					}
				}
			}
		}
	};

	useEffect(() => {
		if (watchLocation) {
			setExtraVisible(true);
		}
	}, [watchLocation]);

	useEffect(() => {
		if (!watchEmail) {
			setValue('email', payload.email ?? '');
		}
	}, [watchEmail, setValue, payload.email, filterValues?.recipientEmail]);

	useEffect(() => {
		if (!watchRecipientEmail) {
			setValue('recipientEmail', filterValues?.recipientEmail ?? filterValues?.email ?? payload.email ?? '');
		}
	}, [watchRecipientEmail, setValue, payload.email, filterValues?.recipientEmail, filterValues?.email]);

	useEffect(() => {
		if (watchTerraceOnlyCheckbox === true) {
			setValue('terraceType', [TerraceType.BALCONY, TerraceType.LOGGIA, TerraceType.TERRACE]);
		} else {
			setValue('terraceType', []);
		}
	}, [watchTerraceOnlyCheckbox, setValue]);

	const onLocationSelect = (location: Location | null) => {
		if (!location || location.level > 6) {
			setRadiusVisible(true);
		} else {
			setRadiusVisible(false);
			setValue('radius', 0);
		}
	};

	return (
		<>
			<Form<Inputs> context={formContext} onSubmit={onSubmit}>
				<Dialog isOpen={isOpenForceCreationModal} close={closeForceCreationModal} className="b-dialog--md">
					Vypadá to, že na podobné parametry již máte nastavené hlídání nemovitostí. Doopravy si přejete vytvořit hlídacího psa?
					<div className="grid grid--center u-mt-xs">
						<p className="btn__wrapper">
							<Button
								type="button"
								onClick={() => {
									setValue('force', true);
									formContext.handleSubmit(onSubmit)();
								}}
								className={clsx('btn-loader', isSubmitting && 'is-loading')}
								disabled={!extraVisible || isSubmitting}
								iconAfter="arrow-right"
								text={formatMessage(forceCreateIntlString)}
							/>
						</p>
						<p className="btn__wrapper">
							<Button
								type="button"
								onClick={() => closeForceCreationModal()}
								variant="secondary"
								className={clsx('btn-loader', isSubmitting && 'is-loading')}
								disabled={!extraVisible || isSubmitting}
								iconAfter="arrow-right"
								text={formatMessage(editSearchIntlString)}
							/>
						</p>
					</div>
				</Dialog>
				<div className="form__container">
					<SelectIcon
						items={[
							{
								label: formatMessage(saleIntlString),
								value: OfferType.SALE,
							},
							{
								label: formatMessage(rentIntlString),
								value: OfferType.RENT,
							},
						]}
						label={formatMessage({
							description: 'filterForm-adType',
							defaultMessage: 'O jaký typ inzerátu máte zájem?',
						})}
						name="adType"
						required
					/>
					<RealEstateTypeSelector selectIcons={selectIcons} />
					{customLocationCoordsAreValid ? (
						<div style={{ display: 'flex', marginBottom: 20 }}>
							<PreviewMap startingArea={watchCustomLocationCoords} />
							<Button iconOnly="trashcan" style={{ marginLeft: 10 }} onClick={() => setValue('customLocationCoords', null)} />
						</div>
					) : (
						<Suggest
							label={formatMessage(enterLocationIntlString)}
							name="locationId"
							required
							onSelect={onLocationSelect}
							extraContent={
								<Button size="xs" onClick={() => setIsMapModalOpen(true)}>
									Choose on map
								</Button>
							}
						/>
					)}
					<div className={clsx('form__extra-container', (extraVisible || showExtra) && 'is-visible')}>
						<RadiusSlider disabled={!isRadiusVisible} />
						{watchRealEstateType === RealEstateType.APARTMENT && watchAdType !== OfferType.ROOMMATES && <DispositionSelector />}
						{watchRealEstateType === RealEstateType.LAND && <LandTypeSelector />}
						{watchAdType !== OfferType.ROOMMATES && watchRealEstateType === RealEstateType.HOUSE && <HouseTypeSelector />}
						<RangeSlider
							label={`${formatMessage(priceIntlString)} ${
								watchAdType === OfferType.RENT || watchAdType === OfferType.ROOMMATES
									? formatMessage(monthlyIntlString)
									: formatMessage(propertyIntlString)
							}`}
							max={
								watchAdType === OfferType.RENT
									? PRICE_HIGH_RENT
									: watchAdType === OfferType.ROOMMATES
									? PRICE_HIGH_ROOMMATES
									: PRICE_HIGH_SALE
							}
							name="price"
							logarithmic
							roundTo={watchAdType === OfferType.RENT ? 1_000 : watchAdType === OfferType.ROOMMATES ? 500 : 50_000}
							unit="Kč"
						/>
						<Checkbox label={formatMessage(offersNopriceIntlString)} name="exactMatch" reversed />
						{watchAdType !== OfferType.ROOMMATES &&
							(watchRealEstateType === RealEstateType.APARTMENT || watchRealEstateType === RealEstateType.HOUSE) && (
								<AreaSelector />
							)}
						{watchAdType !== OfferType.ROOMMATES &&
							(watchRealEstateType === RealEstateType.HOUSE || watchRealEstateType === RealEstateType.LAND) && (
								<LandAreaSelector />
							)}
						{watchAdType === OfferType.SALE && <OwnershipTypeSelect />}
						<CheckboxOrRadioGroup
							items={contactTypes.filter(({ value }) => ![ContactType.UNKNOWN, ContactType.IN_REVIEW].includes(value))}
							name="agent"
							label="Zdroj inzerátu"
							size="md"
							emptyLabel={formatMessage(everythingIntlString)}
							onlyPremium={!showBadgeStandard}
							showBadgePremium={true}
						/>
						{(watchRealEstateType === RealEstateType.APARTMENT || watchRealEstateType === RealEstateType.HOUSE) && (
							<TerraceCheckbox />
						)}
						{watchRealEstateType === RealEstateType.APARTMENT && (
							<Checkbox
								label={formatMessage(elevatorIntlString)}
								name="elevator"
								onlyPremium={!showBadgeStandard}
								showBadgePremium={true}
							/>
						)}
						{!filterValues?.email && !payload.email && (
							<Checkbox
								label={
									<>
										<FormattedMessage description={'filterForm-agree'} defaultMessage={'Souhlasím s'} />{' '}
										<Link href="/obchodni-podminky" target="_blank">
											<FormattedMessage description={'filterForm-t&c'} defaultMessage={'obchodními podmínkami'} />{' '}
										</Link>
									</>
								}
								name="conditions"
								required
							/>
						)}
						{isAdvanced && <ExcludedOriginsSelect locale={defaultLocale} />}
						{(isAdmin || isEnterprise) && (
							<>
								<div className={clsx('inp-row', errors.email && 'has-error')}>
									<span className="inp-row__top">
										<Label id="createWebhook">
											<FormattedMessage description="filterForm-yourEmail" defaultMessage={'Webhook pro vytvoření'} />
										</Label>
										<Tooltip forInput title="Pokud chcete dostávat nabídky přes webhook">
											<FormattedMessage
												description={'filterForm-webhookTooltip'}
												defaultMessage="Vložte url svého webhooku, doména musí být na seznamu podporovaných. Formát: http metoda (post, get, delete nebo put) a url požadavku."
											/>
										</Tooltip>
									</span>
									<div className="inp-fix">
										<input id="createWebhook" className="inp-text" {...register('createWebhook')} />
									</div>
								</div>

								<div className={clsx('inp-row', errors.email && 'has-error')}>
									<span className="inp-row__top">
										<Label id="deleteWebhook">
											<FormattedMessage description="filterForm-yourEmail" defaultMessage={'Webhook pro smazání'} />
										</Label>
										<Tooltip forInput title="Pokud chcete rušit nabídky přes webhook">
											<FormattedMessage
												description={'filterForm-webhookTooltip'}
												defaultMessage="Vložte url svého webhooku, doména musí být na seznamu podporovaných. Formát: http metoda (post, get, delete nebo put) a url požadavku."
											/>
										</Tooltip>
									</span>
									<div className="inp-fix">
										<input id="deleteWebhook" className="inp-text" {...register('deleteWebhook')} />
									</div>
								</div>

								<div className={clsx('inp-row', errors.email && 'has-error')}>
									<span className="inp-row__top">
										<Label id="updateWebhook">
											<FormattedMessage
												description="filterForm-yourEmail"
												defaultMessage={'Webhook pro aktualizace'}
											/>
										</Label>
										<Tooltip forInput title="Pokud chcete dostávat upozornění změnu nabídky">
											<FormattedMessage
												description={'filterForm-webhookTooltip'}
												defaultMessage="Vložte url svého webhooku, doména musí být na seznamu podporovaných. Formát: http metoda (post, get, delete nebo put) a url požadavku."
											/>
										</Tooltip>
									</span>
									<div className="inp-fix">
										<input id="updateWebhook" className="inp-text" {...register('updateWebhook')} />
									</div>
								</div>
							</>
						)}
						{isProfi && (
							<div className={clsx('inp-row', errors.email && 'has-error')}>
								<span className="inp-row__top">
									<Label id="recipientEmail">
										<FormattedMessage
											description="filterForm-recipientEmail"
											defaultMessage={'E-mail pro příjem nemovitostí'}
										/>
									</Label>
									<Tooltip forInput title="">
										<FormattedMessage
											description={'filterForm-webhookTooltip'}
											defaultMessage="Slouží k zasílání emailů s informacemi o nalezených nemovitostech na jiný email než váš."
										/>
									</Tooltip>
								</span>
								<div className="inp-fix">
									<input id="recipientEmail" className="inp-text" {...register('recipientEmail')} />
								</div>
							</div>
						)}
					</div>
					<input type="hidden" {...register('customLocationCoords')} />
					{filterValues?.email || payload.email ? (
						<input type="hidden" {...register('email', { required: true })} />
					) : (
						<div className={clsx('inp-row', errors.email && 'has-error')}>
							<span className="inp-row__top">
								<Label id="email" required>
									<FormattedMessage description="filterForm-yourEmail" defaultMessage={'Váš e\u2011mail'} />
								</Label>
								<Tooltip forInput title={formatMessage(noSpamIntlString)}>
									<FormattedMessage
										description={'filterForm-noEmailMisuse'}
										defaultMessage={'Slibujeme, že váš e\u2011mail nikdy nezneužijeme!'}
									/>
								</Tooltip>
							</span>
							<div className="inp-fix">
								<EmailInput needsSuggestions={true} />
							</div>
						</div>
					)}
					<div className="form__bottom-container">
						<p className="btn__wrapper">
							<Button
								type="submit"
								className={clsx('btn-loader', isSubmitting && 'is-loading')}
								disabled={(!extraVisible || isSubmitting) && !customLocationCoordsAreValid}
								iconAfter="arrow-right"
								text={
									filterValues && dialogClose
										? formatMessage(editFiltersIntlString)
										: formatMessage(trackSearchIntlString)
								}
							/>
						</p>
						<div className={clsx('form__extra-container', extraVisible && watchLocation && 'is-visible')}>
							<EstimateTooltip filterValues={defaultValues} />
						</div>
						<FilterUrlTooltip />
						{/* {router.locale === Locale.cs && (
						<div
							className={clsx(
								'form__extra-container form__extra-container--bottom u-color-grey u-font-xs',
								(extraVisible || showExtra) && 'is-visible',
							)}
						>
							Nevyhovuje vám vyhledávání? Chtěli byste hledat podle jiných parametrů?{' '}
							<a href="mailto:<EMAIL>?subject=Návrh nových parametrů">Napište&nbsp;nám</a>
						</div>
					)} */}
					</div>
				</div>
			</Form>
			{isMapModalOpen && (
				<Modal onClose={closeMapModal}>
					<InteractiveMap
						onCancelClick={closeMapModal}
						updateCustomLocationCoords={(data) => {
							setValue('customLocationCoords', data);
						}}
					/>
				</Modal>
			)}
		</>
	);
};

FilterForm.displayName = 'FilterForm';
