import { FC } from 'react';
import { useIntl } from 'react-intl';
import { SelectItem } from 'types/common';
import { SelectIcon } from '../core/SelectIcon';

type Props = {
	selectIcons: SelectItem[];
	label?: string;
	name?: string;
};

export const RealEstateTypeSelector: FC<Props> = ({ selectIcons, label, name = 'realEstateType' }) => {
	const { formatMessage } = useIntl();
	const regularLabel = formatMessage({
		description: 'filterForm-chooseType',
		defaultMessage: 'Jaký typ nemovitosti hledáte?',
	});

	const labelToUse = label ?? regularLabel;

	return <SelectIcon items={selectIcons} label={labelToUse} name={name} required />;
};
