import { useUser } from 'components/user/UserProvider';
import { FC } from 'react';
import { useFormContext } from 'react-hook-form';
import InputRange from 'react-input-range';
import { FormattedMessage } from 'react-intl';
import { FlagPremium } from '../core/FlagPremium';
import { Label } from '../core/Label';

type Props = {
	disabled?: boolean;
	showStandardBadge?: boolean;
};

export const RadiusSlider: FC<Props> = ({ disabled, showStandardBadge = true }) => {
	const { loggedIn, payload } = useUser();
	const { setValue, watch } = useFormContext();
	const watchedRadius = watch('radius');
	const formatLabel = (radius: number) => `${radius.toString()} km`;

	const message = <FormattedMessage description="filterForm-inRadius" defaultMessage="Hledat v okolí" />;
	const input = (
		<InputRange
			minValue={0}
			maxValue={50}
			value={watchedRadius}
			onChange={(value) => setValue('radius', value)}
			step={1}
			formatLabel={formatLabel}
			disabled={disabled}
		/>
	);

	const DisableForRoleStandard = (loggedIn && payload.role >= 5) || !loggedIn;

	return (
		<div className="inp-row">
			<Label>
				{DisableForRoleStandard ? message : <span className="premium-enabled">{message}</span>}
				{watchedRadius > 0 && <span className="u-color-secondary"> {watchedRadius && watchedRadius + ' km'}</span>}{' '}
				{showStandardBadge && <FlagPremium text="Standard" />}
			</Label>
			{DisableForRoleStandard ? input : <span className="premium-enabled">{input}</span>}
		</div>
	);
};
