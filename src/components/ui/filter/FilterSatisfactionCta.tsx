import { Title } from 'components/ui/core/Title';
import { FC } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';

export const FilterSatisfactionCta: FC = () => {
	const { formatMessage } = useIntl();
	return (
		<>
			<div className="u-mb-lg u-text-center u-font-lg u-font-medium">
				<Title tagName="h2" margin="xs">
					<FormattedMessage
						defaultMessage="Jak jste byli <strong>spokojeni?</strong>"
						description="filterSatisfactionCta-title"
						values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
					/>
				</Title>
				<p>
					<FormattedMessage
						defaultMessage={'Můžeme Vás požádat o\u00a02\u00a0minuty k\u00a0vyplnění zpětné vazby?'}
						description="filterSatisfactionCta-description"
					/>
				</p>
				<p>
					<a
						href={formatMessage({
							defaultMessage: 'https://forms.gle/qKYQxmqBehojNuP97',
							description: 'filterSatisfactionCta-feedbackLink',
						})}
						className="btn btn--lg"
						target="_blank"
						rel="noreferrer"
					>
						<span className="btn__text">
							<FormattedMessage defaultMessage="Vyplnit krátký dotazník" description="filterSatisfactionCta-button" />
						</span>
					</a>
				</p>
			</div>
			<div className="u-mb-lg u-text-center u-font-lg u-font-medium">
				<Title tagName="h3" margin="xs">
					<FormattedMessage
						defaultMessage="A protože každý <strong>like</strong> se počítá!"
						description="filterSatisfactionCta-share-title"
						values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
					/>
				</Title>
				<p>
					<FormattedMessage
						defaultMessage="Podpoříte nás sdílením, za to budeme pro vás službu dál zlepšovat."
						description="filterSatisfactionCta-share-description"
					/>
				</p>
				<p className="grid grid--x-xs grid--y-xs grid--center">
					<span className="grid__cell size--auto">
						<a
							href={formatMessage({
								defaultMessage: 'https://www.facebook.com/sharer/sharer.php?u=https://www.visidoo.cz',
								description: 'filterSatisfactionCta-facebookShareLink',
							})}
							className="btn btn--facebook"
							target="_blank"
							rel="noreferrer noopener"
						>
							<span className="btn__text">
								<FormattedMessage
									defaultMessage="Sdílet na Facebooku"
									description="filterSatisfactionCta-share-onFacebook"
								/>
							</span>
						</a>
					</span>
					<span className="grid__cell size--auto">
						<a
							href={formatMessage({
								defaultMessage:
									'https://twitter.com/intent/tweet?url=https://www.visidoo.cz&text=Hled%C3%A1te%20bydlen%C3%AD?%20Vyzkou%C5%A1ejte%20Visidoo.%0AHl%C3%ADd%C3%A1%20za%20v%C3%A1s%20realitn%C3%AD%20trh%20a%20upozor%C5%88ujeme%20na%20nejnov%C4%9Bj%C5%A1%C3%AD%20inzer%C3%A1ty.',
								description: 'filterSatisfactionCta-twitterShareLink',
							})}
							className="btn btn--twitter"
							target="_blank"
							rel="noreferrer noopener"
						>
							<span className="btn__text">
								<FormattedMessage defaultMessage="Na Twitter" description="filterSatisfactionCta-share-onTwitter" />
							</span>
						</a>
					</span>
					<span className="grid__cell size--auto">
						<a
							href={formatMessage({
								defaultMessage: 'https://www.linkedin.com/shareArticle?mini=true&url=https://www.visidoo.cz',
								description: 'filterSatisfactionCta-linkedinShareLink',
							})}
							className="btn btn--linkedin"
							target="_blank"
							rel="noreferrer noopener"
						>
							<span className="btn__text">
								<FormattedMessage defaultMessage="Na LinkedIn" description="filterSatisfactionCta-share-onLinkedin" />
							</span>
						</a>
					</span>
				</p>
			</div>
		</>
	);
};

FilterSatisfactionCta.displayName = 'FilterSatisfactionCta';
