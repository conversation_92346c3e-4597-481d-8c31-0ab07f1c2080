import { fetchFilterDelete } from 'api/filter/fetchFilterDelete';
import { fetchFilterStateUpdate } from 'api/filter/fetchFilterStateUpdate';
import clsx from 'clsx';
import { contactTypes } from 'components/admin/ContactList';
import { useFormattedPriceString } from 'components/intl/prices';
import { FilterDataContext } from 'components/search/FilterDataProvider';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Icon } from 'components/ui/core/Icon';
import { IconButton } from 'components/ui/core/IconButton';
import { RealEstateLocation } from 'components/ui/filter/RealEstateLocation';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { useShortLocale } from 'i18n/useShortLocale';
import { useRouter } from 'next/router';
import { FC, useContext, useState } from 'react';
import { FormattedMessage, IntlShape, useIntl } from 'react-intl';
import { Filter, FilterRelation, FilterState, RealEstateType } from 'types/filter';
import { OfferType, ownershipTypeMessageMap, TerraceType } from 'types/offer';
import { DateRange } from 'types/time';
import { trackFilterCount } from 'utils/analytics';
import { TEN_SECONDS } from 'utils/constants';
import { formatDate, formatNumber, uppercaseFirst } from 'utils/formats';
import { getFiltersLocalizedStrings } from 'utils/getFiltersLocalizedStrings';
import { FilterSwitch } from './FilterSwitch';
import { Button } from '../core/Button';
import { FlagPremium } from '../core/FlagPremium';
import { useUserFilterCount } from 'hooks/useUserFilterCount';

const { terraceIntlString, loggiaIntlString, balconyIntlString, ownershipTypeIntlString } = commonInltMessages;

const icons = {
	[RealEstateType.APARTMENT]: 'sofa',
	[RealEstateType.HOUSE]: 'house-1',
	[RealEstateType.LAND]: 'signpost',
	[RealEstateType.COMMERCIAL]: 'building',
	[RealEstateType.OTHER]: 'garage',
};

type Props = Filter & {
	onFilterEdit?: (filter: Filter) => void;
	onAdminEdit?: (filter: Filter) => void;
	onAdminEditFinish?: (filter: Filter) => void;
	isFilterDetailPage?: boolean;
	setCtaDialogOpen?: (id: string) => void;
	hideEditLink?: boolean;
};

export const FilterBox: FC<Props> = ({
	onFilterEdit,
	onAdminEdit,
	onAdminEditFinish,
	isFilterDetailPage,
	setCtaDialogOpen,
	hideEditLink = false,
	...filter
}) => {
	const {
		id,
		area,
		elevator,
		landArea,
		landType,
		houseType,
		price,
		createdAt,
		adType,
		realEstateType,
		location,
		disposition,
		exactMatch,
		notificationsSent,
		estimate,
		state,
		radius,
		terraceType,
	} = filter;
	const { token, isAdmin, isEnterprise, isAdvanced, payload, loggedIn } = useUser();
	const showBadgePremium = (loggedIn && payload.role >= 6) || !loggedIn;
	const { addNotification } = useNotifications();
	const shortLocale = useShortLocale();
	const intl = useIntl();
	const router = useRouter();
	const { pathname } = router;
	const { generateLabel } = getFiltersLocalizedStrings(intl, shortLocale);
	const { updateFilter, removeFilter } = useContext(FilterDataContext);
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const { isFull } = useUserFilterCount();

	const formattedPriceGte = useFormattedPriceString(price.gte);
	const formattedPriceLte = useFormattedPriceString(price.lte);
	const notSpecifiedLabel = intl.formatMessage({ defaultMessage: 'Nespecifikováno', description: 'filter-notSpecifiedLabel' });

	const DayFormattedMessage = <FormattedMessage description={'filterbox-day'} defaultMessage={'den'} />;
	const WeekFormattedMessage = <FormattedMessage description={'filterbox-week'} defaultMessage={'týden'} />;
	const MonthFormattedMessage = <FormattedMessage description={'filterbox-month'} defaultMessage={'měsíc'} />;
	const YearFormattedMessage = <FormattedMessage description={'filterbox-year'} defaultMessage={'rok'} />;

	const shouldShowEditingButtons = typeof onFilterEdit !== 'undefined' || typeof onAdminEdit !== 'undefined' || !!isFilterDetailPage;

	const handleEditClick = () => {
		if (isFilterDetailPage) {
			router.push('/moje-filtry');
		}
		if (onFilterEdit) {
			onFilterEdit(filter);
		} else if (onAdminEdit) {
			onAdminEdit(filter);
		}
	};

	const handleStateChange = (newState: FilterState) => {
		if (!token) return;
		setIsLoading(true);

		if (isAdmin && onAdminEditFinish) {
			fetchFilterStateUpdate(id, newState, token, true)
				.then((result) => {
					if (result.statusCode === 200) {
						onAdminEditFinish(result.data.filter);
					}
				})
				.finally(() => setIsLoading(false));
		} else {
			fetchFilterStateUpdate(id, newState, token)
				.then((result) => {
					if (result.statusCode === 200 && updateFilter) {
						const activeFilters = result.data.user?.activeFilters;
						if (activeFilters) {
							trackFilterCount(activeFilters);
						}
						updateFilter(result.data.filter, id);
					}
					const relation = result.data.relation;
					if (relation) {
						if (relation === FilterRelation.CONTAINS) {
							addNotification({
								timeout: TEN_SECONDS,
								message: 'Tento filtr plně nahrazuje jiný Váš filtr, který jsme pro vás smazali.',
							});
						}
						if (relation === FilterRelation.WITHIN) {
							addNotification({
								timeout: TEN_SECONDS,
								message: 'Tento filtr nelze aktivovat, protože máte aktivní jiný filtr, který jej plně nahrazuje.',
							});
						}
					}
				})
				.finally(() => setIsLoading(false));
		}
	};

	const handleActiveClick = async () => {
		const isActive = state === FilterState.ACTIVE;
		await handleStateChange(isActive ? FilterState.DISABLED : FilterState.ACTIVE);
		if (isActive) {
			setCtaDialogOpen?.(id);
		}
	};

	// const handleCopyClick = () => {
	// 	// handle copy click
	// };

	const handleDeleteClick = () => {
		setIsLoading(true);
		fetchFilterDelete(id, token)
			.then((result) => {
				trackFilterCount(result.data.user.activeFilters);
				if (removeFilter) {
					removeFilter(id);
					return;
				}
			})
			.catch(() => setIsLoading(false));
	};

	return (
		<BlockLoader loading={isLoading}>
			<article className={clsx('b-product', state === FilterState.ACTIVE ? 'b-product--online' : 'b-product--disabled')}>
				<div className="b-product__header">
					<div className="b-annot-product">
						<div className="b-annot-product__icon">
							<Icon name={icons[realEstateType]} />
						</div>
						<div className="b-annot-product__content">
							<p className="b-annot-product__caption">
								<FormattedMessage
									description={'filterBox.iWant'}
									defaultMessage={'Chci {adType} {realEstateType}'}
									values={{
										adType: generateLabel({ adType }),
										realEstateType: generateLabel({ realEstateType }).toLowerCase(),
									}}
								/>
							</p>
							<h2 className="b-annot-product__title">
								<RealEstateLocation location={location} radius={radius} />
							</h2>
						</div>
						{shouldShowEditingButtons && (
							<FilterSwitch
								isChecked={state === FilterState.ACTIVE}
								handleChange={handleActiveClick}
								disabled={state !== FilterState.ACTIVE && isFull}
								isFull={state !== FilterState.ACTIVE && isFull}
							/>
						)}
					</div>
				</div>
				<div className="b-product__content">
					<div className="b-product__content-left">
						<div className="b-product__description">
							<div className="b-description">
								<ul className="b-description__list">
									{adType !== OfferType.ROOMMATES && (
										<>
											{realEstateType === RealEstateType.APARTMENT && (
												<li className="b-description__item">
													<span className="u-font-medium">
														<FormattedMessage
															description={'filterBox-flatLayout'}
															defaultMessage={'Dispozice: '}
														/>
													</span>{' '}
													{disposition.length > 0 ? disposition.join(', ') : notSpecifiedLabel}
												</li>
											)}
											{realEstateType === RealEstateType.HOUSE && (
												<li className="b-description__item">
													<span className="u-font-medium">
														<FormattedMessage description="filterBox-houseType" defaultMessage="Druh domu: " />
													</span>{' '}
													{houseType.length > 0
														? houseType
																.map((type) =>
																	uppercaseFirst(generateLabel({ houseType: type }), shortLocale),
																)
																.join(', ')
														: notSpecifiedLabel}
												</li>
											)}
											{realEstateType === RealEstateType.LAND && (
												<li className="b-description__item">
													<span className="u-font-medium">
														<FormattedMessage
															description={'filterBox-landType'}
															defaultMessage={'Druh pozemku: '}
														/>
													</span>{' '}
													{landType.length > 0
														? landType
																.map((type) =>
																	uppercaseFirst(generateLabel({ landType: type }), shortLocale),
																)
																.join(', ')
														: notSpecifiedLabel}
												</li>
											)}
											{(realEstateType === RealEstateType.APARTMENT || realEstateType === RealEstateType.HOUSE) && (
												<li className="b-description__item">
													<span className="u-font-medium">
														<FormattedMessage description={'filterBox-area'} defaultMessage={'Plocha: '} />
													</span>{' '}
													{area.gte > 0 || area.lte ? (
														<>
															{area.gte > 0 && <FromMetersFormattedMessage areaValue={area.gte} />}{' '}
															{area.lte && <ToMetersFormattedMessage areaValue={area.lte} />}{' '}
														</>
													) : (
														notSpecifiedLabel
													)}
												</li>
											)}
											{(realEstateType === RealEstateType.HOUSE || realEstateType === RealEstateType.LAND) && (
												<li className="b-description__item">
													<span className="u-font-medium">
														<FormattedMessage
															description={'filterBox-landArea'}
															defaultMessage={'Plocha pozemku: '}
														/>
													</span>{' '}
													{landArea.gte > 0 || landArea.lte ? (
														<>
															{landArea.gte > 0 && <FromMetersFormattedMessage areaValue={landArea.gte} />}{' '}
															{landArea.lte && <ToMetersFormattedMessage areaValue={landArea.lte} />}{' '}
														</>
													) : (
														notSpecifiedLabel
													)}
												</li>
											)}
										</>
									)}
									<li className="b-description__item">
										<span className="u-font-medium">
											<FormattedMessage description={'filterBox-price'} defaultMessage={'Cena: '} />
										</span>{' '}
										{price.gte > 0 || price.lte ? (
											<>
												{price.gte > 0 && (
													<>
														<FormattedMessage
															description={'filterBox-priceFrom'}
															defaultMessage={'od {price}'}
															values={{ price: formattedPriceGte }}
														/>{' '}
													</>
												)}{' '}
												{price.lte && (
													<>
														<FormattedMessage
															description={'filterBox-priceTo'}
															defaultMessage={'do {price}'}
															values={{ price: formattedPriceLte }}
														/>
													</>
												)}
											</>
										) : (
											notSpecifiedLabel
										)}
										{exactMatch && (
											<>
												<br />
												<FormattedMessage
													description={'filterBox-exactMatchText'}
													defaultMessage={'Pouze nabídky s uvedenou cenou'}
												/>
											</>
										)}
									</li>
									{realEstateType === RealEstateType.APARTMENT && elevator && (
										<li className="b-description__item">
											<span className="u-font-medium">
												<FormattedMessage description="filterBox-elevator" defaultMessage="Výtah: " />
											</span>{' '}
											<FormattedMessage description="common-yes" defaultMessage="Ano" />
										</li>
									)}
									{isAdvanced && filter.agent && filter.agent.length > 0 && (
										<li className="b-description__item">
											<span className="u-font-medium">Zdroj inzerátu:</span>{' '}
											{filter.agent
												.map((agent) => contactTypes.find(({ value }) => agent === value)?.label)
												.join(', ')}
										</li>
									)}

									{isAdvanced && filter.ownershipType && filter.ownershipType.length > 0 && (
										<li className="b-description__item">
											<span className="u-font-medium">
												<FormattedMessage {...ownershipTypeIntlString} />
											</span>{' '}
											{filter.ownershipType
												.map((type) => intl.formatMessage(ownershipTypeMessageMap[type]))
												.join(', ')}
										</li>
									)}

									{(isEnterprise || isAdmin) && filter.createWebhook && (
										<li className="b-description__item">
											<span className="u-font-medium">Webhook pro vytvoření:</span> {filter.createWebhook}
										</li>
									)}
									{(isEnterprise || isAdmin) && filter.deleteWebhook && (
										<li className="b-description__item">
											<span className="u-font-medium">Webhook pro smazání:</span> {filter.deleteWebhook}
										</li>
									)}
									{(isEnterprise || isAdmin) && filter.updateWebhook && (
										<li className="b-description__item">
											<span className="u-font-medium">Webhook pro aktualizace:</span> {filter.updateWebhook}
										</li>
									)}

									{terraceType && terraceType.length > 0 && (
										<li className="b-description__item">
											<span className="u-font-medium">
												<FormattedMessage
													description={'filterBox-terracesRequired'}
													defaultMessage={'S těmito typy teras: '}
												/>
											</span>{' '}
											{formatTerraceTypes(terraceType, intl)}
										</li>
									)}
								</ul>
								{shouldShowEditingButtons && (
									<p className="b-description__btn">
										<a onClick={handleEditClick}>
											<FormattedMessage description={'filterBox-editParams'} defaultMessage={'Upravit parametry'} />
										</a>
									</p>
								)}
								{pathname !== '/moje-filtry/[filterId]' && pathname !== '/uzivatel/[id]' && (
									<>
										{showBadgePremium ? (
											<p className="b-description__btn u-mt-xs">
												<Button href={`/moje-filtry/${filter.id}`}>
													<FormattedMessage
														description={'filterBox-showRealEstates'}
														defaultMessage={'Zobrazit historii inzerátů'}
													/>
												</Button>
											</p>
										) : (
											<p className="b-description__btn u-mt-xs">
												<span className="grid grid--middle grid--x-xs">
													<span className="grid__cell size--auto premium-enabled">
														<Button>
															<FormattedMessage
																description={'filterBox-showRealEstates'}
																defaultMessage={'Zobrazit historii inzerátů'}
															/>
														</Button>
													</span>
													<span className="grid__cell size--auto">
														<FlagPremium text="Profi" />
													</span>
												</span>
											</p>
										)}
									</>
								)}
							</div>
						</div>
					</div>
					<div className="b-product__content-right">
						<div className="b-counter">
							<ul className="b-counter__list">
								<li className={clsx('b-counter__item', estimate.value === 0 && 'b-counter__item--warning')}>
									{estimate.value > 0 && <span className="b-counter__value">≈ {formatNumber(estimate.value)}</span>}
									<span className="b-counter__text">
										{estimate.value === 0 ? (
											<>
												<strong>
													<FormattedMessage
														description={'filterBox-lowProbability'}
														defaultMessage={'Pravděpodobnost obdržení nabídky je nízká.'}
													/>
												</strong>
												<br />
												{hideEditLink ? (
													<FormattedMessage
														description={'filterBox-moreOffers'}
														defaultMessage={
															'Pro více nabídek, můžete více upravit filtr ve <a>svém profilu</a>'
														}
														values={{
															a: (...chunks: string[]) => <a href="moje-filtry">{chunks}</a>,
														}}
													/>
												) : (
													<FormattedMessage
														description={'filterBox-moreOffers'}
														defaultMessage={'Pro více nabídek, můžete více <a>zobecnit filtr</a>'}
														values={{
															a: (...chunks: string[]) => <a onClick={handleEditClick}>{chunks}</a>,
														}}
													/>
												)}
											</>
										) : (
											<>
												<FormattedMessage
													description={'filterBox-offersEstimate'}
													defaultMessage={
														'{count, plural, one {nová nabídka} few {nové nabídky} other {nových nabídek}}'
													}
													values={{
														count: estimate.value,
													}}
												/>{' '}
												<FormattedMessage description={'filterBox-for'} defaultMessage={'za'} />{' '}
												{estimate.range === DateRange.DAY
													? DayFormattedMessage
													: estimate.range === DateRange.WEEK
													? WeekFormattedMessage
													: estimate.range === DateRange.MONTH
													? MonthFormattedMessage
													: YearFormattedMessage}
											</>
										)}
									</span>
								</li>
								{notificationsSent > 0 && (
									<li className="b-counter__item">
										<span className="b-counter__value">{formatNumber(notificationsSent)}</span>
										<span className="b-counter__text">
											<FormattedMessage
												description={'filterBox-notificationsSent'}
												defaultMessage={'odeslaných upozornění'}
											/>
										</span>
									</li>
								)}
							</ul>
						</div>
					</div>
				</div>
				<div className="b-product__bottom">
					<div className="b-product__bottom-left">
						<div className="b-dates">
							<ul className="b-dates__list">
								<li className="b-dates__item">
									<p>
										<FormattedMessage description={'filterBox-creationDate'} defaultMessage={'Datum vytvoření'} />:{' '}
										<span className="u-font-medium">{formatDate(new Date(createdAt))}</span>
									</p>
								</li>
								{/* <li className="b-dates__item">
								<p>
									Datum expirace: <span className="u-font-medium">{dateUpdated}</span>
								</p>
							</li> */}
							</ul>
						</div>
						<span className="b-product__flags" />
					</div>
					{shouldShowEditingButtons && (
						<div className="b-product__bottom-right">
							<div className="b-actions">
								<ul className="b-actions__list">
									<li className="b-actions__item">
										<IconButton
											className="b-actions__btn"
											icon="filter"
											text={intl.formatMessage({
												description: 'filterBox-buttonFilters',
												defaultMessage: 'Upravit parametry',
											})}
											withTooltip
											onClick={handleEditClick}
										/>
									</li>
									{/* <li className="b-actions__item">
								<button
									className="b-actions__btn"
									onClick={handleCopyClick}
									data-tip="Kopírovat"
									data-for="tooltip-buttons"
								>
									<Icon name="copy" />
									<span className="u-vhide">
										<FormattedMessage description={'filterBox-copy'} defaultMessage={'Kopírovat'} />
									</span>
								</button>
							</li> */}
									<li className="b-actions__item">
										<IconButton
											className="b-actions__btn"
											icon="trashcan"
											text={intl.formatMessage({
												description: 'filterBox-delete',
												defaultMessage: 'Vymazat',
											})}
											withTooltip
											onClick={handleDeleteClick}
										/>
									</li>
								</ul>
							</div>
						</div>
					)}
				</div>
			</article>
		</BlockLoader>
	);
};

FilterBox.displayName = 'FilterBox';

const FromMetersFormattedMessage = ({ areaValue }: { areaValue: number }) => {
	return (
		<FormattedMessage
			description={'filterBox-metersFrom'}
			defaultMessage={'od {area} m² '}
			values={{ area: formatNumber(areaValue) }}
		/>
	);
};

const ToMetersFormattedMessage = ({ areaValue }: { areaValue: number }) => {
	return (
		<FormattedMessage description={'filterBox-metersTo'} defaultMessage={'do {area} m² '} values={{ area: formatNumber(areaValue) }} />
	);
};

const formatTerraceTypes = (terraceType: TerraceType[], intl: IntlShape) => {
	const formattedTerraceMessages = [];
	if (terraceType.includes(TerraceType.TERRACE)) formattedTerraceMessages.push(intl.formatMessage(terraceIntlString));
	if (terraceType.includes(TerraceType.LOGGIA)) formattedTerraceMessages.push(intl.formatMessage(loggiaIntlString));
	if (terraceType.includes(TerraceType.BALCONY)) formattedTerraceMessages.push(intl.formatMessage(balconyIntlString));

	return formattedTerraceMessages.join(', ');
};
