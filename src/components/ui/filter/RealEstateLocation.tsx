import { FC } from 'react';
import { Location } from 'types/filter';

type Props = {
	location?: Location;
	radius?: number;
	display?: ('part' | 'city' | 'district' | 'region' | 'cohesion' | 'country')[];
};

export const RealEstateLocation: FC<Props> = ({ location, radius, display = ['city', 'district'] }) => {
	if (!location) return null;
	const { name, part, city, district, region, cohesion, country } = location;

	const parts = [
		name,
		display.includes('part') && part && part !== name ? part : '',
		display.includes('city') && city && city !== name ? city : '',
		display.includes('district') && district && district !== name ? district : '',
		display.includes('region') && region && region !== name ? region : '',
		display.includes('cohesion') && cohesion && cohesion !== name ? cohesion : '',
		display.includes('country') && country && country !== name ? country : '',
	];

	return (
		<>
			{parts.filter((part) => part).join(', ')}
			{typeof radius !== 'undefined' && radius > 0 && ` + ${radius} km`}
		</>
	);
};

RealEstateLocation.displayName = 'RealEstateLocation';
