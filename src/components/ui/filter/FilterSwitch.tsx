import { FC } from 'react';

type Props = {
	isChecked: boolean;
	handleChange: () => void;
	disabled?: boolean;
	isFull?: boolean;
};

export const FilterSwitch: FC<Props> = ({ isChecked, handleChange, disabled, isFull = true }) => {
	return (
		<div className="b-annot-product__btn u-text-right">
			<div>
				<div className="switch u-ml-auto">
					<input
						type="checkbox"
						id="filter-active"
						name="filter-active"
						className="switch__btn"
						checked={isChecked}
						onChange={handleChange}
						disabled={disabled}
					/>
					<label htmlFor="filter-active" className="switch__label" />
				</div>
			</div>
			<div className="u-font-xs">
				{isFull && (
					<>
						Tento filtr nelze aktivovat,
						<br /> protože máte maximální počet aktivních filtrů pro váš tarif.
					</>
				)}
			</div>
		</div>
	);
};
