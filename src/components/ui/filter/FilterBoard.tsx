import { fetchUserFilters } from 'api/user/fetchUserFilters';
import { FilterDataContext } from 'components/search/FilterDataProvider';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Tabs } from 'components/ui/core/Tabs';
import { useUser } from 'components/user/UserProvider';
import { FC, useContext, useEffect, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';
import { Filter, FilterState } from 'types/filter';

type Props = {
	renderFilters: (filters: Filter[], isLoading?: boolean) => ReturnType<FC>;
};

export const FilterBoard: FC<Props> = ({ renderFilters }) => {
	const intl = useIntl();
	const [isLoading, setIsLoading] = useState<boolean>(true);
	const { filters, changeFilters } = useContext(FilterDataContext);
	const { loading, loggedOut, token } = useUser();

	const activeFilters = useMemo(() => filters.filter(({ state }) => state === FilterState.ACTIVE), [filters]);
	const disabledFilters = useMemo(() => filters.filter(({ state }) => state === FilterState.DISABLED), [filters]);

	const showAllFiltersIntlString = intl.formatMessage({
		description: 'myFilters-showAllFilters',
		defaultMessage: 'Všechny filtry',
	});
	const showActiveFiltersIntlString = intl.formatMessage({
		description: 'myFilters-showActiveFilters',
		defaultMessage: 'Aktivní filtry',
	});
	const filtersOffIntlString = intl.formatMessage({
		description: 'myFilters-filtersOff',
		defaultMessage: 'Vypnuté filtry',
	});

	useEffect(() => {
		if (loading || loggedOut) return;
		// GET /filter (with jwt token)
		fetchUserFilters(token).then((result) => {
			changeFilters(result.data.filters);
			setIsLoading(false);
		});
	}, [token, loading, loggedOut, changeFilters]);

	return (
		<BlockLoader loading={isLoading}>
			<Tabs className="b-tabs--transparent u-mb-lg">
				<div title={showAllFiltersIntlString}>{renderFilters(filters, isLoading)}</div>
				<div title={showActiveFiltersIntlString}>{renderFilters(activeFilters, isLoading)}</div>
				<div title={filtersOffIntlString}>{renderFilters(disabledFilters, isLoading)}</div>
			</Tabs>
		</BlockLoader>
	);
};

FilterBoard.displayName = 'FilterBoard';
