import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { FC } from 'react';
import { useIntl } from 'react-intl';
import { ownershipTypeMessageMap } from 'types/offer';
import { CheckboxOrRadioGroup } from '../core/CheckboxOrRadioGroup';
import { filterInltMessages } from 'components/ui/filter/filterIntlMessages';
import { useUser } from 'components/user/UserProvider';

const { ownershipTypeIntlString } = commonInltMessages;
const { everythingIntlString } = filterInltMessages;

export const OwnershipTypeSelect: FC = () => {
	const { loggedIn, payload } = useUser();
	const { formatMessage } = useIntl();

	const options = Object.entries(ownershipTypeMessageMap).map(([enumValue, intlLabel]) => ({
		value: enumValue,
		label: formatMessage(intlLabel),
	}));

	const showBadgeStandard = (loggedIn && payload.role >= 5) || !loggedIn;

	return (
		<CheckboxOrRadioGroup
			name="ownershipType"
			label={formatMessage(ownershipTypeIntlString)}
			items={options}
			size="md"
			emptyLabel={formatMessage(everythingIntlString)}
			onlyPremium={!showBadgeStandard}
			showBadgePremium={true}
		/>
	);
};
