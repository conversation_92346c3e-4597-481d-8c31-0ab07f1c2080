import { FC, ReactNode } from 'react';
import { Message } from 'components/ui/core/Message';
import { FormattedMessage } from 'react-intl';
import { MapOverlay } from './MapOverlay';

type Props = {
	onClose: () => void;
	message: ReactNode;
};

export const AlertOverlay: FC<Props> = ({ onClose, message }) => {
	return (
		<MapOverlay buttonText={<FormattedMessage id="map.calculateAgain" defaultMessage="Vypočítat novou plochu" />} onContinue={onClose}>
			<Message icon="exclamation" variant="error">
				{message}
			</Message>
		</MapOverlay>
	);
};
