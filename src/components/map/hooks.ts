import { MutableRefObject, useEffect, useRef, useState } from 'react';
import mapboxgl, { Map, LngLatBoundsLike } from 'mapbox-gl';
import MapboxDraw from '@mapbox/mapbox-gl-draw';
import * as turf from '@turf/turf';
import { Geometry, PolygonCoords } from 'types/map';

const initialSettings = {
	lng: 15.473,
	lat: 49.8175,
	zoom: 5,
};

const NORTH = 51.032;
const WEST = 12.0526;
const EAST = 18.5132;
const SOUTH = 48.3309;
const SELECTED_AREA_COLOR = '#FFAC1C';

const bounds: LngLatBoundsLike = [
	[WEST, SOUTH],
	[EAST, NORTH],
];

export const useMapSetup = ({ preview, Draw }: { preview: boolean; Draw: MapboxDraw }) => {
	const mapContainer = useRef<HTMLDivElement | null>(null);
	const map = useRef<Map | null>(null);

	const [mapLoading, setMapLoading] = useState(true);

	// Map initialization
	useEffect(() => {
		if (map.current) return;

		const initializeMap = () => {
			map.current = new mapboxgl.Map({
				container: mapContainer.current ?? '',
				style: 'mapbox://styles/andsco/cmff4sqwy00b901sdah5j4p29',
				center: [initialSettings.lng, initialSettings.lat],
				zoom: initialSettings.zoom,
				maxBounds: bounds,
				interactive: !preview,
			});

			if (!preview) {
				map.current.addControl(Draw);
			}
		};

		initializeMap();
	}, [Draw, preview]);

	// Map loading state
	useEffect(() => {
		if (!map.current) return;
		map.current.on('load', () => setMapLoading(false));
	}, []);

	return { mapContainer, map, mapLoading };
};

export const useHandlePresentArea = ({
	map,
	startingArea,
	preview,
	mapLoading,
	Draw,
}: {
	map: MutableRefObject<Map | null>;
	startingArea?: PolygonCoords | null;
	preview: boolean;
	mapLoading: boolean;
	Draw: MapboxDraw;
}) => {
	useEffect(() => {
		if (!map.current || !startingArea || mapLoading) return;

		handlePresentArea({ map, startingArea, preview, Draw });
	}, [startingArea, preview, mapLoading, map, Draw]);
};

const handlePresentArea = ({
	map,
	startingArea,
	preview,
	Draw,
}: {
	map: MutableRefObject<Map | null>;
	startingArea: PolygonCoords;
	Draw: MapboxDraw;
	preview: boolean;
}) => {
	if (!map.current) return;

	if (preview) {
		const sourceId = 'preview-polygon';

		const geojson = {
			type: 'Feature',
			geometry: {
				type: 'Polygon',
				coordinates: startingArea,
			},
			properties: {
				fill: SELECTED_AREA_COLOR,
				outline: SELECTED_AREA_COLOR,
			},
		} as GeoJSON.Feature;

		if (!map.current.getSource(sourceId)) {
			map.current.addSource(sourceId, {
				type: 'geojson',
				data: geojson,
			});

			map.current.addLayer({
				id: 'preview-polygon-fill',
				type: 'fill',
				source: sourceId,
				paint: {
					'fill-color': SELECTED_AREA_COLOR,
					'fill-opacity': 0.4,
				},
			});

			map.current.addLayer({
				id: 'preview-polygon-outline',
				type: 'line',
				source: sourceId,
				paint: {
					'line-color': SELECTED_AREA_COLOR,
					'line-width': 2,
				},
			});
		} else {
			(map.current.getSource(sourceId) as mapboxgl.GeoJSONSource).setData(geojson);
		}

		const polygon = turf.polygon(startingArea);
		const bbox = turf.bbox(polygon) as [number, number, number, number];
		map.current.fitBounds(bbox, { padding: 20, animate: false });
	} else {
		const geometry: Geometry = {
			type: 'Polygon',
			coordinates: startingArea,
		};

		Draw.add(geometry);

		const { lastDrawnPolygonId } = handleDrawnShapes(Draw);

		if (typeof lastDrawnPolygonId === 'string') {
			Draw.changeMode('direct_select', { featureId: lastDrawnPolygonId });
		} else {
			Draw.changeMode('simple_select');
		}
	}
};

const handleDrawnShapes = (Draw: MapboxDraw) => {
	const drawnShapes = Draw?.getAll();
	const lastDrawnPolygonId = drawnShapes?.features?.[drawnShapes.features.length - 1]?.id;

	const previouslyDrawnPolygonIds: string[] = drawnShapes?.features
		?.filter((feature) => feature.geometry.type === 'Polygon' && feature.id !== lastDrawnPolygonId)
		.map((item) => item.id as string);

	return { lastDrawnPolygonId, previouslyDrawnPolygonIds };
};
