import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import { MapOverlay } from './MapOverlay';

type Props = {
	onUnderstand: () => void;
};

// TODO: Rephrase and translate this
export const InstructionsOverlay: FC<Props> = ({ onUnderstand }) => {
	return (
		<MapOverlay onContinue={onUnderstand} buttonText={<FormattedMessage id="map.instructionsUnderstood" defaultMessage="Pochopil" />}>
			<h3>Instructions</h3>
			<p>Draw an area by clicking the cursor around.</p>
			<p>When the area is finalised, click twice and then press the CONFIRM button.</p>
			<p>To cancel the area, click on the trash bin at the top-right.</p>
			<p>To start drawing a new area, click on the rectangular button at the top-right.</p>
		</MapOverlay>
	);
};
