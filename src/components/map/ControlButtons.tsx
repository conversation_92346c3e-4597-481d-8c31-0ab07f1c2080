import { Button } from 'components/ui/core/Button';
import { FC } from 'react';

type Props = {
	onInstructionsClick: () => void;
	onCancelClick: () => void;
	onSaveAreaClick: () => void;
	showInstructionsButton: boolean;
	showSaveAreaButton: boolean;
};

export const ControlButtons: FC<Props> = ({
	onInstructionsClick,
	onCancelClick,
	onSaveAreaClick,
	showInstructionsButton,
	showSaveAreaButton,
}) => {
	return (
		<div
			style={{
				width: '100%',
				height: '50',
				position: 'absolute',
				zIndex: 10,
				padding: 20,
				bottom: 0,
				display: 'flex',
				justifyContent: 'center',
			}}
		>
			<div style={{ width: '30%', display: 'flex', justifyContent: 'space-around' }}>
				{showInstructionsButton && (
					<Button variant="secondary" size="xs" onClick={onInstructionsClick}>
						Intructions
					</Button>
				)}
				{showSaveAreaButton && <Button onClick={onSaveAreaClick}>Save area</Button>}
				<Button variant="secondary" size="xs" onClick={onCancelClick}>
					Cancel
				</Button>
			</div>
		</div>
	);
};
