import { Button } from 'components/ui/core/Button';
import { FC, ReactNode } from 'react';

type Props = {
	onContinue: () => void;
	buttonText: ReactNode;
};

export const MapOverlay: FC<Props> = ({ onContinue, buttonText, children }) => {
	return (
		<div
			style={{
				width: '100%',
				height: '100%',
				backgroundColor: 'rgba(27, 38, 49, 0.9)',
				position: 'absolute',
				top: 0,
				zIndex: 10,
				padding: 40,
				color: 'white',
				flexDirection: 'column',
				display: 'flex',
				justifyContent: 'center',
				alignItems: 'center',
			}}
		>
			{children}
			<Button style={{ marginTop: 10 }} onClick={onContinue}>
				{buttonText}
			</Button>
		</div>
	);
};
