import { FC } from 'react';
import mapboxgl from 'mapbox-gl';
import MapboxDraw from '@mapbox/mapbox-gl-draw';
import { PolygonCoords } from 'types/map';
import { MapUi } from './MapUi';
import { useHandlePresentArea, useMapSetup } from './hooks';
import { MAPBOX_TOKEN } from 'utils/constants';

mapboxgl.accessToken = MAPBOX_TOKEN;

const Draw = new MapboxDraw();

type Props = {
	startingArea: PolygonCoords;
};

export const PreviewMap: FC<Pick<Props, 'startingArea'>> = ({ startingArea }) => {
	const { mapContainer, map, mapLoading } = useMapSetup({ preview: true, Draw });

	useHandlePresentArea({ map, startingArea, preview: true, mapLoading, Draw });

	return <MapUi preview={true} mapLoading={mapLoading} mapContainer={mapContainer} />;
};
