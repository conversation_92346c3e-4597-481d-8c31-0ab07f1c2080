import { FC, ReactNode, useCallback, useEffect, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import MapboxDraw from '@mapbox/mapbox-gl-draw';
import { InstructionsOverlay } from './InstructionsOverlay';
import { ControlButtons } from './ControlButtons';
import * as turf from '@turf/turf';
import { convertArea } from '@turf/turf';
import { DrawData, PolygonCoords } from 'types/map';
import { AlertOverlay } from './AlertOverlay';
import { FormattedMessage } from 'react-intl';
import { formatSquareKm } from 'utils/formats';
import { MapUi } from './MapUi';
import { useHandlePresentArea, useMapSetup } from './hooks';

mapboxgl.accessToken = 'pk.eyJ1IjoiYW5kc2NvIiwiYSI6ImNtZmY0bHowdDBkbmUybXI5bGx3ZWlmazEifQ.Dc2ZQKcwcP-sXcS9su6y2g';

const MAX_SELECTABLE_AREA = 1500;
const SAVE_POLYGON_INTERVAL = 2000;
const HAS_READ_INSTRUCTIONS_KEY = 'userHasReadMapInstructions';

const Draw = new MapboxDraw({
	displayControlsDefault: false,
	controls: {
		polygon: true,
		trash: true,
	},
	defaultMode: 'draw_polygon',
});

type Props = {
	onCancelClick: () => void;
	updateCustomLocationCoords: (data: PolygonCoords | null) => void;
	startingArea?: PolygonCoords | null;
};

export const InteractiveMap: FC<Props> = ({ onCancelClick, updateCustomLocationCoords, startingArea }) => {
	const { mapContainer, map, mapLoading } = useMapSetup({ preview: false, Draw });

	const userHasAlreadyReadInstructions = localStorage.getItem(HAS_READ_INSTRUCTIONS_KEY) === 'true';

	const [polygonCoordinates, setPolygonCoordinates] = useState<PolygonCoords | null>(null);
	const [isShowingInstructions, setIsShowingInstructions] = useState(!userHasAlreadyReadInstructions);
	const [polygonSavedOn, setPolygonSavedOn] = useState<number | null>(null);
	const [errorMessage, setErrorMessage] = useState<ReactNode | null>(null);

	const handleAreaSaving = () => {
		if (polygonCoordinates) {
			updateCustomLocationCoords(polygonCoordinates);
			onCancelClick();
		}
	};

	const resetCoordinates = useCallback(() => {
		updateCustomLocationCoords(null);
		setPolygonCoordinates(null);
	}, [updateCustomLocationCoords]);

	const onInstructionsUnderstood = () => {
		setIsShowingInstructions(false);
		localStorage.setItem(HAS_READ_INSTRUCTIONS_KEY, 'true');
	};

	const handleDrawnShapes = () => {
		const drawnShapes = Draw?.getAll();
		const lastDrawnPolygonId = drawnShapes?.features?.[drawnShapes.features.length - 1]?.id;

		const previouslyDrawnPolygonIds: string[] = drawnShapes?.features
			?.filter((feature) => feature.geometry.type === 'Polygon' && feature.id !== lastDrawnPolygonId)
			.map((item) => item.id as string);

		return { lastDrawnPolygonId, previouslyDrawnPolygonIds };
	};

	const onPolygonReady = useCallback(
		(data: DrawData) => {
			const coords = data.features[0]?.geometry?.coordinates;
			const [areaIsSmallEnough, selectedArea] = validateSelectedArea(coords);
			if (!areaIsSmallEnough) {
				Draw.deleteAll();
				resetCoordinates();
				setErrorMessage(
					<FormattedMessage
						id="map.areaTooBig"
						defaultMessage={'Příliš velký! Vybrali jste oblast o velikosti {selectedArea}. Vyberte max. oblast {maxArea}.'}
						values={{
							selectedArea: formatSquareKm(selectedArea),
							maxArea: formatSquareKm(MAX_SELECTABLE_AREA),
						}}
					/>,
				);
				return;
			}

			setPolygonSavedOn(Date.now());
			setPolygonCoordinates(coords);
		},
		[resetCoordinates],
	);

	useHandlePresentArea({ map, startingArea, preview: false, mapLoading, Draw });

	// Polygon-ready handling
	useEffect(() => {
		if (!map.current || (polygonSavedOn && Date.now() < polygonSavedOn + SAVE_POLYGON_INTERVAL)) return;

		map.current.on('draw.create', (data: DrawData) => {
			onPolygonReady(data);
		});
	}, [polygonSavedOn, onPolygonReady, map]);

	// Handle polygon update
	useEffect(() => {
		if (!map.current || (polygonSavedOn && Date.now() < polygonSavedOn + SAVE_POLYGON_INTERVAL)) return;

		map.current.on('draw.update', (data: DrawData) => {
			onPolygonReady(data);
		});
	}, [polygonSavedOn, onPolygonReady, map]);

	// Handle drawing of multiple polygons
	useEffect(() => {
		if (!map.current) return;

		map.current.on('draw.modechange', () => {
			if (Draw.getMode() == 'draw_polygon') {
				const { previouslyDrawnPolygonIds } = handleDrawnShapes();

				Draw.delete(previouslyDrawnPolygonIds);
				resetCoordinates();
			}
		});
	});

	// Handle polygon deletion
	useEffect(() => {
		if (!map.current) return;
		map.current.on('draw.delete', () => {
			resetCoordinates();
		});
	});

	return (
		<MapUi preview={false} mapLoading={mapLoading} mapContainer={mapContainer}>
			{!mapLoading && (
				<ControlButtons
					showInstructionsButton={!isShowingInstructions}
					showSaveAreaButton={!!polygonCoordinates}
					onInstructionsClick={() => setIsShowingInstructions(true)}
					onCancelClick={onCancelClick}
					onSaveAreaClick={handleAreaSaving}
				/>
			)}

			{isShowingInstructions && <InstructionsOverlay onUnderstand={onInstructionsUnderstood} />}
			{errorMessage && <AlertOverlay message={errorMessage} onClose={() => setErrorMessage(null)} />}
		</MapUi>
	);
};

const validateSelectedArea = (polygonData: PolygonCoords): [boolean, number] => {
	const area = turf.area(turf.polygon(polygonData));
	const convertedArea = convertArea(area, 'meters', 'kilometers');
	return [convertedArea <= MAX_SELECTABLE_AREA, parseFloat(convertedArea.toFixed(2))];
};
