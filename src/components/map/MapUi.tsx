import { FC, MutableRefObject, PropsWithChildren } from 'react';
import { BlockLoader } from 'components/ui/core/BlockLoader';

type Props = {
	preview: boolean;
	mapLoading: boolean;
	mapContainer: MutableRefObject<HTMLDivElement | null>;
};
export const MapUi: FC<PropsWithChildren<Props>> = ({ preview, mapLoading, mapContainer, children }) => {
	return (
		<div
			style={{
				width: preview ? 300 : 1200,
				height: preview ? 150 : 700,
				position: 'relative',
			}}
		>
			{mapLoading && <BlockLoader loading />}
			<div ref={mapContainer} className="map-container" style={{ height: '100%', visibility: mapLoading ? 'hidden' : 'visible' }} />
			{children}
		</div>
	);
};
