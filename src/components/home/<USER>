import { Row } from 'components/ui/core/Row';
import { FC } from 'react';
import Image from 'next/image';
import Link from 'next/link';

export const SupportBox: FC = () => {
	return (
		<div className="b-cta u-bg-orange">
			<Row>
				<div className="b-cta__inner">
					<div className="b-cta__content">
						<h2>Potřebujete poradit?</h2>
						<p className="u-mb-xs">
							R<PERSON>di vám poradíme s nastavením upozornění na nové realitní nab<PERSON>, ať už hledáte prodej nebo pronájem bytu,
							domu či pozemeku.
						</p>
						<p className="u-font-lg">
							Napište nám na <a href="mailto:<EMAIL>"><EMAIL></a>
						</p>
						<p>
							Případně najdete většinu odpovědí v <Link href="/casto-kladene-otazky">často kladených dotazech</Link>.
						</p>
					</div>
					<div className="b-cta__img">
						<Image
							src="/img/illust/graphics-2.png"
							alt=""
							width="300"
							height="320"
							loading="lazy"
							quality="100"
							lazyBoundary="1000px"
						/>
					</div>
				</div>
			</Row>
		</div>
	);
};
