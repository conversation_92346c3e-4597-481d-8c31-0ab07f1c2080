import { fetchTelephonePost } from 'api/telephone/fetchTelephonePost';
import clsx from 'clsx';
import { Button } from 'components/ui/core/Button';
import { EmailInput } from 'components/ui/core/EmailInput';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Label } from 'components/ui/core/Label';
import { Row } from 'components/ui/core/Row';
import { TelephoneInput } from 'components/ui/core/TelephoneInput';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { useRouter } from 'next/router';
import { FC, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage, useIntl } from 'react-intl';
import { RegisterCtaInputs } from 'types/Auth';
import { TEN_SECONDS } from 'utils/constants';
import Image from 'next/image';
import { ErrorMessage } from 'components/ui/core/ErrorMessage';

export const TelephoneBox: FC = () => {
	const { payload, loggedOut, refresh } = useUser();
	const { addNotification } = useNotifications();
	const { locale } = useRouter();
	const { formatMessage } = useIntl();
	const { commonTelephoneIntlString } = commonInltMessages;
	const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
	const context = useForm<RegisterCtaInputs>({ defaultValues: { telephone: '', email: payload.email ?? '', locale, timezone } });

	const [errorMessage, setErrorMessage] = useState<string | null>(null);

	if (payload?.telephone != null) return null;

	const {
		formState: { isSubmitting, errors },
	} = context;

	const notifySuccess = () => {
		const notificationMessage = formatMessage({
			defaultMessage: 'Děkujeme, zavoláme co nejdříve to půjde.',
			description: 'telephoneBox-msgEmail',
		});
		addNotification({ message: notificationMessage, timeout: TEN_SECONDS });
	};

	const notifyError = () => {
		setErrorMessage(formatMessage({ defaultMessage: 'Nepodařilo se odeslat formulář', description: 'telephoneBox-msgCommon' }));
	};

	const onSubmit: SubmitHandler<RegisterCtaInputs> = async (data) => {
		try {
			setErrorMessage(null);
			const response = await fetchTelephonePost(data);

			switch (response.statusCode) {
				case 201:
					notifySuccess();
					break;
				case 200:
					notifySuccess();
					await refresh();
					break;
				default:
					notifyError();
			}
		} catch (error) {
			error instanceof Error ? setErrorMessage(error.message) : notifyError();
		}
	};

	return (
		<div className="b-cta u-bg-orange">
			<Row>
				<div className="b-cta__inner">
					<div className="b-cta__content">
						<h2>
							<FormattedMessage
								defaultMessage="Potřebujete poradit{br} s hledáním bydlení?"
								description="telephoneBox-title"
								values={{
									br: <br />,
								}}
							/>
						</h2>
						<p className="u-mb-xs">
							<FormattedMessage
								defaultMessage="Nechte nám váš kontakt, ozveme se vám{br} a poradíme s čímkoliv co se týká hledání a koupě bydlení."
								description="telephoneBox-desc"
								values={{
									br: <br />,
								}}
							/>
						</p>
						<Form<RegisterCtaInputs> onSubmit={onSubmit} context={context}>
							<div className="u-mw-6-12">
								{loggedOut && (
									<div className={clsx('inp-row', errors.email && 'has-error')}>
										<span className="inp-row__top">
											<Label id="email" required>
												<FormattedMessage description={'telephoneBox-email'} defaultMessage={'Váš e\u2011mail'} />
											</Label>
										</span>
										<span className="inp-fix">
											<EmailInput />
										</span>
										<FormError name="email" />
									</div>
								)}

								<div className={clsx('inp-row', errors.telephone && 'has-error')}>
									<span className="inp-row__top">
										<Label id="telephone" required>
											<FormattedMessage {...commonTelephoneIntlString} />
										</Label>
									</span>
									<span className="inp-fix">
										<TelephoneInput required />
									</span>
									<FormError name="telephone" />
								</div>
							</div>

							<p className="u-mb-xs">
								<Button
									type="submit"
									className={clsx('btn-loader', isSubmitting && 'is-loading')}
									disabled={isSubmitting}
									iconAfter="arrow-right"
									text={formatMessage({
										description: 'telephoneBox-register',
										defaultMessage: 'Ozvěte se mi',
									})}
								/>
							</p>
						</Form>
						{errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
					</div>
					<div className="b-cta__img">
						<Image
							src="/img/illust/graphics-2.png"
							alt=""
							width="300"
							height="320"
							loading="lazy"
							quality="100"
							lazyBoundary="1000px"
						/>
					</div>
				</div>
			</Row>
		</div>
	);
};
