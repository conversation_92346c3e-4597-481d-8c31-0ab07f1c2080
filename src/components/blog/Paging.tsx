import { FC } from 'react';
import { PagingList } from 'components/ui/blog/PagingList';
import { PagingItem } from 'components/blog/PagingItem';
import { FormattedMessage } from 'react-intl';

type Props = {
	current: number;
	total: number;
};

export const Paging: FC<Props> = ({ current, total }) => (
	<PagingList>
		{current > 1 && (
			<PagingItem page={current - 1}>
				<FormattedMessage description={'paging-prev'} defaultMessage={'Předchozí'} />
			</PagingItem>
		)}
		{Array.from({ length: total }, (_, i) => (
			<PagingItem page={i + 1} key={`page${i + 1}`} disabled={current === i + 1}>
				{i + 1}
			</PagingItem>
		))}
		{current < total && (
			<PagingItem page={current + 1}>
				<FormattedMessage description={'paging-next'} defaultMessage={'Další'} />
			</PagingItem>
		)}
	</PagingList>
);

Paging.displayName = 'Paging';
