import { FC } from 'react';
import { PagingListItem } from 'components/ui/blog/PagingListItem';

type Props = {
	page: number;
	disabled?: boolean;
};

export const PagingItem: FC<Props> = ({ page, disabled = false, children }) => (
	<PagingListItem href={`/blog${page > 1 ? '/[page]' : ''}`} as={page > 1 ? `/blog/${page}` : undefined} disabled={disabled}>
		{children}
	</PagingListItem>
);

PagingItem.displayName = 'PagingItem';
