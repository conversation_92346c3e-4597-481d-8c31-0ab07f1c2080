import { fetchPaymentGet } from 'api/payment/fetchPaymentGet';
import { usePremiumDialogs } from 'components/dialog/PremiumDialogsProvider';
import { Link } from 'components/Link';
import { Button } from 'components/ui/core/Button';
import { Row } from 'components/ui/core/Row';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
// import { AuthForm, useAuth } from 'components/user/AuthProvider';
import { useUser } from 'components/user/UserProvider';
import { usePaymentClick } from 'hooks/usePaymentClick';
import { useRouter } from 'next/router';
import { FC, useEffect, useMemo, useState } from 'react';
import { Filter, FilterResponse } from 'types/filter';
import { UserRole } from 'types/user';
import { IS_DEV, TEN_SECONDS } from 'utils/constants';

type Props = { count: number; data?: Filter; disableMini?: boolean };

export const PremiumTariffsCompare: FC<Props> = ({ data, disableMini = false }) => {
	const { openPriceUpdate, openNotifications, openFiltersExtend, openHistory, openStatsBasic, openFilters } = usePremiumDialogs();

	const {
		token,
		loggedIn,
		payload: { role, id, email },
	} = useUser();

	const [loading, setLoading] = useState(false);
	const [userState, setUserState] = useState<FilterResponse['user']>();
	const router = useRouter();
	const { addNotification } = useNotifications();

	useEffect(() => {
		const userInfo: NonNullable<FilterResponse['user']> = JSON.parse(localStorage.getItem('visidoo:userInfo') ?? '{}');
		if (Object.keys(userInfo).length > 0) {
			setUserState(userInfo);
		}
	}, []);

	const handlePaymentClick = async () => {
		setLoading(true);
		fetchPaymentGet(token)
			.then((response) => response.data.url)
			.then((url) => router.push(url))
			.catch((e) => {
				addNotification({ message: e.message, timeout: TEN_SECONDS });
			})
			.finally(() => setLoading(false));
	};
	const onPaymentClick = usePaymentClick(data);

	const stripeArguments = useMemo(() => {
		const query = new URLSearchParams({
			prefilled_email: email ?? data?.email,
			client_reference_id: (id ? id : userState?.id) ?? '',
		});

		return `?${query.toString()}`;
	}, [email, data?.email, id, userState?.id]);

	return (
		<>
			<Row lg>
				<section className="u-mb-lg b-pricing">
					<div className="u-mb-xs">
						<div className="grid grid--x-sm ">
							<div className="grid__cell size--6-12@md size--3-12@xl grid__cell--eq">
								<article className="b-pricing__inner">
									<h3 className="b-pricing__title">Mini</h3>
									<p>Pro jednotlivce, kteří hledají nemovitost v jedné konkrétní lokalitě.</p>
									<p className="b-pricing__price">
										<strong>249&nbsp;Kč</strong> za měsíc{' '}
										<span className="u-color-grey-light">
											(<del className="u-font-sm u-color-grey-light">299&nbsp;Kč</del>)
										</span>
									</p>
									<ul className="b-pricing__list b-pricing__list--tick">
										<li>
											<a className="btn u-td-dotted u-color-default a" onClick={openNotifications}>
												Okamžitá upozornění
											</a>{' '}
											na nejnovější inzeráty
										</li>
										<li>
											1{' '}
											<a className="btn u-td-dotted u-color-default a" onClick={openFilters}>
												hlídací pes
											</a>
										</li>
									</ul>
									<p className="b-pricing__btn">
										{role == UserRole.MINI ? (
											<span className="btn btn--full btn--disabled btn--lg">
												<span className="btn__text">
													Váš aktuální tarif
													<span className="b-pricing__btn-price">
														platíte <strong>249&nbsp;Kč</strong> měsíčně
													</span>
												</span>
											</span>
										) : disableMini ? (
											<span className="btn btn--full btn--disabled btn--lg">
												<span className="btn__text">
													Tarif nelze aktivovat
													<span className="b-pricing__btn-price">Bylo použito pokročilé vyhledávání</span>
												</span>
											</span>
										) : (
											<>
												{loggedIn && role > UserRole.USER ? (
													<Button onClick={handlePaymentClick} size="lg" full disabled={loading}>
														Změnit na tarif Mini
														<span className="b-pricing__btn-price">249&nbsp;Kč měsíčně</span>
													</Button>
												) : (
													<Link
														href={`${
															IS_DEV
																? 'https://buy.stripe.com/test_5kAeXk7RVchB3zW9AC'
																: 'https://buy.stripe.com/14AeVedmz28N0ird8RaZi03'
														}${stripeArguments}`}
														Element={(props) => <a {...props} onClick={onPaymentClick} />}
													>
														<span className="btn btn--full btn--lg">
															<span className="btn__text">
																Zdarma na <strong>7 dní</strong>
																<span className="b-pricing__btn-price">
																	poté <strong>249&nbsp;Kč</strong> měsíčně
																</span>
															</span>
														</span>
													</Link>
												)}
											</>
										)}
									</p>
								</article>
							</div>

							<div className="grid__cell size--6-12@md size--3-12@xl grid__cell--eq">
								<article className={`b-pricing__inner ${(role <= UserRole.USER || !role) && 'b-pricing__inner--premium'}`}>
									{(role < UserRole.PROFI || !role) && <div className="b-pricing__flag">Doporučujeme</div>}
									<h3 className="b-pricing__title">Standard</h3>
									<p>Pro aktivní zájemce o bydlení s možností širšího vyhledávání a vyhodnocení cen.</p>
									<p className="b-pricing__price">
										<strong>349&nbsp;Kč</strong> za měsíc{' '}
										<span className="u-color-grey-light">
											(<del className="u-font-sm u-color-grey-light">399&nbsp;Kč</del>)
										</span>
									</p>
									<ul className="b-pricing__list b-pricing__list--tick">
										<li>Vše z tarifu Mini. Plus navíc:</li>
										<li>
											Maximálně 5{' '}
											<a className="btn u-td-dotted u-color-default a" onClick={openFilters}>
												hlídacích psů
											</a>
										</li>
										<li>
											<a className="btn u-td-dotted u-color-default a" onClick={openFiltersExtend}>
												Detailní možnosti
											</a>{' '}
											nastavení vyhledávání nabídek
										</li>
										<li>
											<a className="btn u-td-dotted u-color-default a" onClick={openStatsBasic}>
												Vyhodnocení výhodnosti ceny
											</a>{' '}
										</li>
									</ul>
									<p className="b-pricing__btn">
										{role == UserRole.STANDARD ? (
											<span className="btn btn--full btn--disabled btn--lg">
												<span className="btn__text">
													Váš aktuální tarif
													<span className="b-pricing__btn-price">
														platíte <strong>349&nbsp;Kč</strong> měsíčně
													</span>
												</span>
											</span>
										) : (
											<>
												{loggedIn && role > UserRole.USER ? (
													<Button onClick={handlePaymentClick} size="lg" full disabled={loading}>
														Změnit na tarif Standard
														<span className="b-pricing__btn-price">349&nbsp;Kč měsíčně</span>
													</Button>
												) : (
													<Link
														href={`${
															IS_DEV
																? 'https://buy.stripe.com/test_5kA5mK0pt3L5c6s144'
																: 'https://buy.stripe.com/bIYcOn3MZd8Baqc9AB'
														}${stripeArguments}`}
														Element={(props) => <a {...props} onClick={onPaymentClick} />}
													>
														<span className="btn btn--full btn--lg">
															<span className="btn__text">
																Zdarma na <strong>7 dní</strong>
																<span className="b-pricing__btn-price">poté 349&nbsp;Kč měsíčně</span>
															</span>
														</span>
													</Link>
												)}
											</>
										)}
									</p>
								</article>
							</div>

							<div className="grid__cell size--6-12@md size--3-12@xl grid__cell--eq">
								<article className="b-pricing__inner">
									<h3 className="b-pricing__title">Premium</h3>
									<p>Pro náročné jednotlivce, kteří chtějí mít detailní přehled a historii nabídek.</p>
									<p className="b-pricing__price">
										<strong>699&nbsp;Kč</strong> za měsíc{' '}
										<span className="u-color-grey-light">
											(<del className="u-font-sm u-color-grey-light">799&nbsp;Kč</del>)
										</span>
									</p>
									<ul className="b-pricing__list b-pricing__list--tick">
										<li>Vše z tarifu Standard. Plus navíc:</li>
										<li>
											Maximálně 20{' '}
											<a className="btn u-td-dotted u-color-default a" onClick={openFilters}>
												hlídacích psů
											</a>
										</li>
										<li>
											<a className="btn u-td-dotted u-color-default a" onClick={openHistory}>
												Historie nabídek
											</a>
										</li>
									</ul>
									<p className="b-pricing__btn">
										{role == UserRole.PREMIUM ? (
											<span className="btn btn--full btn--disabled btn--lg">
												<span className="btn__text">
													Váš aktuální tarif
													<span className="b-pricing__btn-price">
														platíte <strong>699&nbsp;Kč</strong> měsíčně
													</span>
												</span>
											</span>
										) : (
											<>
												{loggedIn && role > UserRole.USER ? (
													<Button onClick={handlePaymentClick} size="lg" full disabled={loading}>
														Změnit na tarif Premium
														<span className="b-pricing__btn-price">699&nbsp;Kč měsíčně</span>
													</Button>
												) : (
													<Link
														href={`${
															IS_DEV
																? 'https://buy.stripe.com/test_5kA5mK0pt3L5c6s144'
																: 'https://buy.stripe.com/fZuaEY3LZfZD7KTd8RaZi04'
														}${stripeArguments}`}
														Element={(props) => <a {...props} onClick={onPaymentClick} />}
													>
														<span className="btn btn--full btn--lg">
															<span className="btn__text">
																Zdarma na <strong>7 dní</strong>
																<span className="b-pricing__btn-price">poté 699&nbsp;Kč měsíčně</span>
															</span>
														</span>
													</Link>
												)}
											</>
										)}
									</p>
								</article>
							</div>

							<div className="grid__cell size--6-12@md size--3-12@xl grid__cell--eq">
								<article className="b-pricing__inner b-pricing__inner--profi">
									<h3 className="b-pricing__title">Profi</h3>
									<p>Pro firmy, realitní kanceláře, makléře a investory.</p>
									<p className="b-pricing__price">
										<strong>2&nbsp;490&nbsp;Kč</strong> za měsíc{' '}
										<span className="u-color-grey-light">
											(<del className="u-font-sm">2&nbsp;990&nbsp;Kč</del>)
										</span>
									</p>
									<ul className="b-pricing__list b-pricing__list--tick">
										<li>Vše z tarifu Premium. Plus navíc:</li>
										<li>
											Neomezený počet{' '}
											<a className="btn u-td-dotted u-color-default a" onClick={openFilters}>
												hlídacích psů
											</a>
										</li>
										<li>
											Upozornění na{' '}
											<a className="btn u-td-dotted u-color-default a" onClick={openPriceUpdate}>
												změny cen inzerátů
											</a>
										</li>
										<li>Možnost nastavit až 5 různých e&#8209;mailů pro notifikace</li>
									</ul>
									<p className="b-pricing__btn">
										{role == UserRole.PROFI ? (
											<span className="btn btn--full btn--disabled btn--lg">
												<span className="btn__text">
													Váš aktuální tarif
													<span className="b-pricing__btn-price">
														platíte <strong>2&nbsp;490&nbsp;Kč</strong> měsíčně
													</span>
												</span>
											</span>
										) : (
											<>
												{loggedIn && role > UserRole.USER ? (
													<Button onClick={handlePaymentClick} size="lg" full disabled={loading}>
														Změnit na tarif Profi
														<span className="b-pricing__btn-price">2&nbsp;490&nbsp;Kč měsíčně</span>
													</Button>
												) : (
													<Link
														href={`${
															IS_DEV
																? 'https://buy.stripe.com/test_14k6qO3BF6Xhb2o6op'
																: 'https://buy.stripe.com/aEU4hRabnb0t8i47su'
														}${stripeArguments}`}
														Element={(props) => <a {...props} onClick={onPaymentClick} />}
													>
														<span className="btn btn--full btn--lg">
															<span className="btn__text">
																Zdarma na <strong>7 dní</strong>
																<span className="b-pricing__btn-price">
																	poté 2&nbsp;490&nbsp;Kč měsíčně
																</span>
															</span>
														</span>
													</Link>
												)}
											</>
										)}
									</p>
								</article>
							</div>
						</div>
					</div>
					<div className="u-text-center">
						<p>
							Všechny ceny jsou uvedeny s DPH. Možnost platby fakturou – kontaktujte{' '}
							<a href="mailto:<EMAIL>">podporu</a>.
						</p>
					</div>
				</section>
			</Row>
		</>
	);
};

PremiumTariffsCompare.displayName = 'PremiumTariffsCompare';
