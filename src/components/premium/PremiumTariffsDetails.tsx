import { fetchPaymentGet } from 'api/payment/fetchPaymentGet';
import { usePremiumDialogs } from 'components/dialog/PremiumDialogsProvider';
import { Link } from 'components/Link';
import { Button } from 'components/ui/core/Button';
import { Row } from 'components/ui/core/Row';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { usePaymentClick } from 'hooks/usePaymentClick';
import { useRouter } from 'next/router';
import { FC, useMemo, useState } from 'react';
import { UserRole } from 'types/user';
import { IS_DEV, TEN_SECONDS } from 'utils/constants';

type Props = { count: number };

export const PremiumTariffsDetails: FC<Props> = ({ count }) => {
	const { openPriceUpdate, openNotifications, openFiltersExtend, openHistory, openStatsDetail, openFilters, openServers } =
		usePremiumDialogs();

	const {
		token,
		loggedIn,
		payload: { role, id, email },
	} = useUser();

	const onPaymentClick = usePaymentClick();

	const stripeArguments = useMemo(() => {
		const query = new URLSearchParams({
			prefilled_email: email,
			client_reference_id: id,
		});

		return `?${query.toString()}`;
	}, [id, email]);

	const [loading, setLoading] = useState(false);
	const router = useRouter();
	const { addNotification } = useNotifications();

	const handlePaymentClick = async () => {
		setLoading(true);
		fetchPaymentGet(token)
			.then((response) => response.data.url)
			.then((url) => router.push(url))
			.catch((e) => {
				addNotification({ message: e.message, timeout: TEN_SECONDS });
			})
			.finally(() => setLoading(false));
	};

	return (
		<Row>
			<section className="u-mb-lg b-compare">
				<h2 className="title title--semicircle u-mb-md">Srovnání tarifů</h2>
				<div className="b-compare__wrap">
					<table>
						<thead>
							<tr>
								<th>Funkce</th>
								<th className="b-compare__tarif">Mini</th>
								<th className="b-compare__tarif">Standard</th>
								<th className="b-compare__tarif">Premium</th>
								<th className="b-compare__tarif">Profi</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<th>
									<a className="btn u-td-dotted u-color-default a" onClick={openServers}>
										Počet monitorovaných zdrojů
									</a>
								</th>
								<td colSpan={4}>{count}</td>
							</tr>
							<tr>
								<th>
									<a className="btn u-td-dotted u-color-default a" onClick={openFilters}>
										Počet hlídacích psů
									</a>
								</th>

								<td>1</td>
								<td>5</td>
								<td>20</td>
								<td>neomezeně</td>
							</tr>
							<tr>
								<th>
									<a className="btn u-td-dotted u-color-default a" onClick={openNotifications}>
										Okamžitá upozornění
									</a>
								</th>

								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
							</tr>
							<tr>
								<th>
									<a className="btn u-td-dotted u-color-default a" onClick={openFiltersExtend}>
										Rozšířené možnosti filtrování nabídek
									</a>
								</th>

								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
							</tr>
							<tr>
								<th>
									<a className="btn u-td-dotted u-color-default a" onClick={openStatsDetail}>
										Vyhodnocení výhodnosti ceny
									</a>
								</th>

								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
							</tr>
							<tr>
								<th>
									<a className="btn u-td-dotted u-color-default a" onClick={openFiltersExtend}>
										Bez reklam
									</a>
								</th>

								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
							</tr>
							<tr>
								<th>
									<a className="btn u-td-dotted u-color-default a" onClick={openHistory}>
										Historie nabídek
									</a>
								</th>

								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
							</tr>
							<tr>
								<th>
									<a className="btn u-td-dotted u-color-default a" onClick={openPriceUpdate}>
										Upozornění na změnu ceny inzerátů
									</a>
								</th>

								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
							</tr>
							<tr>
								<th>5 různých e-mailů pro notifikace</th>

								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__no">Ne</span>
								</td>
								<td>
									<span className="b-compare__tick">Ano</span>
								</td>
							</tr>
							<tr>
								<th className="u-align-bottom">Cena</th>
								<td>
									<del className="u-font-sm u-color-grey-light">299 Kč</del>
									<br /> 249 Kč
								</td>
								<td>
									<del className="u-font-sm u-color-grey-light">399 Kč</del>
									<br /> 349 Kč
								</td>
								<td>
									<del className="u-font-sm u-color-grey-light">799 Kč</del>
									<br /> 699 Kč
								</td>
								<td>
									<del className="u-font-sm u-color-grey-light">2&nbsp;990 Kč</del>
									<br />
									2&nbsp;490 Kč
								</td>
							</tr>
						</tbody>
						<tfoot>
							<tr>
								<td></td>

								<td>
									{role == UserRole.MINI ? (
										<span className="btn btn--disabled">
											<span className="btn__text">Váš tarif</span>
										</span>
									) : (
										<>
											{loggedIn && role > UserRole.USER ? (
												<Button onClick={handlePaymentClick} disabled={loading} size="sm">
													Změnit na Mini
													<span className="b-pricing__btn-price">249&nbsp;Kč měsíčně</span>
												</Button>
											) : (
												<Link
													href={`${
														IS_DEV
															? 'https://buy.stripe.com/test_5kAeXk7RVchB3zW9AC'
															: 'https://buy.stripe.com/14AeVedmz28N0ird8RaZi03'
													}${stripeArguments}`}
													className="btn"
													Element={(props) => <a {...props} onClick={onPaymentClick} />}
												>
													<span className="btn__text">
														<strong>7 dní zdarma</strong>
														<span className="b-pricing__btn-price">
															poté <strong>249 Kč</strong> měsíčně
														</span>
													</span>
												</Link>
											)}
										</>
									)}
								</td>
								<td>
									{role == UserRole.STANDARD ? (
										<span className="btn btn--disabled">
											<span className="btn__text">Váš tarif</span>
										</span>
									) : (
										<>
											{loggedIn && role > UserRole.USER ? (
												<Button onClick={handlePaymentClick} disabled={loading} size="sm">
													Změnit na Standard
													<span className="b-pricing__btn-price">349&nbsp;Kč měsíčně</span>
												</Button>
											) : (
												<Link
													href={`${
														IS_DEV
															? 'https://buy.stripe.com/test_5kA5mK0pt3L5c6s144'
															: 'https://buy.stripe.com/bIYcOn3MZd8Baqc9AB'
													}${stripeArguments}`}
													className="btn"
													Element={(props) => <a {...props} onClick={onPaymentClick} />}
												>
													<span className="btn__text">
														<strong>7 dní zdarma</strong>
														<span className="b-pricing__btn-price">poté 349&nbsp;Kč měsíčně</span>
													</span>
												</Link>
											)}
										</>
									)}
								</td>
								<td>
									{role == UserRole.PREMIUM ? (
										<span className="btn btn--disabled">
											<span className="btn__text">Váš tarif</span>
										</span>
									) : (
										<>
											{loggedIn && role > UserRole.USER ? (
												<Button onClick={handlePaymentClick} disabled={loading} size="sm">
													Změnit na Premium
													<span className="b-pricing__btn-price">699&nbsp;Kč měsíčně</span>
												</Button>
											) : (
												<Link
													href={`${
														IS_DEV
															? 'https://buy.stripe.com/test_5kA5mK0pt3L5c6s144'
															: 'https://buy.stripe.com/fZuaEY3LZfZD7KTd8RaZi04'
													}${stripeArguments}`}
													className="btn"
													Element={(props) => <a {...props} onClick={onPaymentClick} />}
												>
													<span className="btn__text">
														<strong>7 dní zdarma</strong>
														<span className="b-pricing__btn-price">poté 699&nbsp;Kč měsíčně</span>
													</span>
												</Link>
											)}
										</>
									)}
								</td>
								<td>
									{role == UserRole.PROFI ? (
										<span className="btn btn--disabled">
											<span className="btn__text">Váš tarif</span>
										</span>
									) : (
										<>
											{loggedIn && role > UserRole.USER ? (
												<Button onClick={handlePaymentClick} disabled={loading} size="sm">
													Změnit na Profi
													<span className="b-pricing__btn-price">2&nbsp;490&nbsp;Kč měsíčně</span>
												</Button>
											) : (
												<Link
													href={`${
														IS_DEV
															? 'https://buy.stripe.com/test_14k6qO3BF6Xhb2o6op'
															: 'https://buy.stripe.com/aEU4hRabnb0t8i47su'
													}${stripeArguments}`}
													className="btn"
													Element={(props) => <a {...props} onClick={onPaymentClick} />}
												>
													<span className="btn__text">
														<strong>7 dní zdarma</strong>
														<span className="b-pricing__btn-price">poté 2&nbsp;490&nbsp;Kč měsíčně</span>
													</span>
												</Link>
											)}
										</>
									)}
								</td>
							</tr>
						</tfoot>
					</table>
				</div>
			</section>
		</Row>
	);
};

PremiumTariffsDetails.displayName = 'PremiumTariffsDetails';
