import { fetchLeadPost } from 'api/lead/fetchLeadPost';
import clsx from 'clsx';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Button } from 'components/ui/core/Button';
import { CheckboxOrRadioGroup } from 'components/ui/core/CheckboxOrRadioGroup';
import { EmailInput } from 'components/ui/core/EmailInput';
import { ErrorMessage } from 'components/ui/core/ErrorMessage';
import { Form } from 'components/ui/core/Form';
import { FormError } from 'components/ui/core/FormError';
import { Icon } from 'components/ui/core/Icon';
import { Label } from 'components/ui/core/Label';
import { Row } from 'components/ui/core/Row';
import { TelephoneInput } from 'components/ui/core/TelephoneInput';
import { useUser } from 'components/user/UserProvider';
import { formValidationInltMessages } from 'i18n/commonIntlMessages/formValidation';
import { FC, useState } from 'react';
import { SubmitHandler, useForm, useFormContext } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { LeadFormType, LeadPostInputs } from 'types/lead';
import { UserRole, userRoleMap } from 'types/user';

type DefaultViewProps = {
	isLoading?: boolean;
	tariff: UserRole;
};

const interestOptions = [
	{ value: 'moreFilters', label: 'Více filtrů' },
	{ value: 'moreDetails', label: 'Zobrazení více detailů nemovitostí' },
	{ value: 'priceAndLocationStats', label: 'Vyhodnocení ceny a lokality' },
	{ value: 'updateNotifications', label: 'Upozornění na změnu ceny inzerátů' },
	{ value: 'extendedSearch', label: 'Rozšířené možnosti filtrování nabídek' },
	{ value: 'adsAggregation', label: 'Nastavení frekvence upozornění' },
	{ value: 'mobileApp', label: 'Mobilní aplikace' },
	{ value: 'offersHistory', label: 'Listování nabídkami' },
];

const DefaultView: FC<DefaultViewProps> = ({ isLoading, tariff }) => {
	const { formatMessage } = useIntl();
	const { register, formState } = useFormContext<LeadPostInputs>();
	const { errors } = formState;
	const { requiredNameIntlString, requiredSurnameIntlString } = formValidationInltMessages;

	return (
		<div className="f-filter__inner f-filter__inner--extend">
			<div className="u-mw-10-12 u-mx-auto u-mb-sm">
				<h2 className="h4 u-mb-xs">Proč jste se rozhodli pro vyšší tarif?</h2>
				<input type="checkbox" value={`__tariff__${tariff}`} {...register('interests')} className="u-hide" />
				<CheckboxOrRadioGroup items={interestOptions} name="interests" size="lg" required disabled={isLoading} />

				<h2 className="h4 u-mb-xs">Osobní údaje</h2>
				<div className="grid">
					<div className="grid__cell size--6-12@sm">
						<div className={clsx('inp-row u-mb-0', errors.firstName && 'has-error')}>
							<Label id="firstName" required>
								Jméno
							</Label>
							<span className="inp-fix">
								<input
									type="text"
									id="firstName"
									className="inp-text"
									{...register('firstName', {
										disabled: isLoading,
										required: formatMessage(requiredNameIntlString),
									})}
								/>
							</span>
							<FormError name="firstName" />
						</div>
					</div>
					<div className="grid__cell size--6-12@sm">
						<div className={clsx('inp-row u-mb-0', errors.lastName && 'has-error')}>
							<Label id="lastName" required>
								Příjmení
							</Label>
							<span className="inp-fix">
								<input
									type="text"
									id="lastName"
									className="inp-text"
									{...register('lastName', {
										disabled: isLoading,
										required: formatMessage(requiredSurnameIntlString),
									})}
								/>
							</span>
							<FormError name="lastName" />
						</div>
					</div>
					<div className="grid__cell size--6-12@sm">
						<div className={clsx('inp-row u-mb-0', errors.telephone && 'has-error')}>
							<Label id="telephone" required>
								Telefonní číslo
							</Label>
							<span className="inp-fix">
								<TelephoneInput required disabled={isLoading} />
							</span>
							<FormError name="telephone" />
						</div>
					</div>
					<div className="grid__cell size--6-12@sm">
						<div className={clsx('inp-row u-mb-0', errors.email && 'has-error')}>
							<Label id="email" required>
								E&#8209;mail
							</Label>
							<span className="inp-fix">
								<EmailInput disabled={isLoading} />
							</span>
							<FormError name="email" />
						</div>
					</div>
				</div>
			</div>

			<p className="u-text-center">
				<Button type="submit" size="lg" disabled={isLoading}>
					Potvrdit objednávku
				</Button>
			</p>
		</div>
	);
};

const SuccessView: FC = () => (
	<div className="f-filter__inner f-filter__inner--extend u-text-center">
		<div className="u-mw-10-12 u-mx-auto u-mb-xs">
			<h2 className="title u-mb-sm title--semicircle">
				<span className="u-color-secondary">Nepodařil se spárovat</span> zadaný e-mail s&nbsp;uživatelským profilem
			</h2>
			<p className="u-font-lg u-mb-0">V blízké době vás bude kontaktovat naše podpora.</p>
		</div>
	</div>
);

const SuccessUserView: FC = () => (
	<div className="f-filter__inner f-filter__inner--extend u-text-center">
		<section className="c-list u-mb-md">
			<h2 className="title title--semicircle u-mb-sm">
				Aktuálně jsme vám <span className="u-color-secondary">zpřístupnili</span>
			</h2>
			<ul className="c-list__list">
				<li className="c-list__item">
					<span className="c-list__icon icon-svg">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" className="icon-svg__svg">
							<path
								fill="#5620ff"
								d="M19.7-0.1c-6.9,0-12.4,5.6-12.4,12.4c0,3,1,5.7,2.8,7.9l-9.9,9.9l1.8,1.8l9.9-9.9c2.1,1.8,4.9,2.8,7.9,2.8 c6.9,0,12.4-5.6,12.4-12.4S26.6-0.1,19.7-0.1z M19.7,2.3c5.5,0,10,4.4,10,10s-4.4,10-10,10s-10-4.4-10-10S14.2,2.3,19.7,2.3z"
							/>
						</svg>
					</span>
					<span className="c-list__name">Více vyhledávacích filtrů</span>
				</li>
				<li className="c-list__item">
					<span className="c-list__icon icon-svg">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" className="icon-svg__svg">
							<path
								fill="#5620ff"
								d="M16,0l-0.7,0.4L0.6,10L0,10.4V32h32V10.4L31.4,10L16.7,0.4L16,0z M16,2.9l12.5,8.2L16,19.2L3.5,11.1L16,2.9z M2.5,13.3 l12.9,8.3l0.7,0.4l0.7-0.4l12.9-8.3v16.2H2.5V13.3z"
							/>
						</svg>
					</span>
					<span className="c-list__name">Zobrazení více detailů nemovitostí</span>
				</li>
				<li className="c-list__item">
					<span className="c-list__icon icon-svg">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" className="icon-svg__svg">
							<path
								fill="#5620ff"
								d="M16,0C11.1,0,6.6,2.2,3.7,5.8V1.2H1.2v9.8h9.8V8.6H4.7C7.1,4.9,11.2,2.5,16,2.5c7.5,0,13.5,6,13.5,13.5s-6,13.5-13.5,13.5 S2.5,23.5,2.5,16H0c0,8.9,7.1,16,16,16s16-7.1,16-16S24.9,0,16,0z"
							/>
						</svg>
					</span>
					<span className="c-list__name">Upozornění na změnu ceny inzerátů</span>
				</li>
				<li className="c-list__item">
					<span className="c-list__icon icon-svg">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" className="icon-svg__svg">
							<path
								fill="#5620ff"
								d="M25.3,0c-0.2,0-0.4,0-0.6,0.1L3,5.8C1.3,6.3,0,7.9,0,9.7V28c0,2.2,1.8,4,4,4h24c2.2,0,4-1.8,4-4V10.7c0-2.2-1.8-4-4-4H10.2 l15.2-4v2.7H28V2.7C28,1.2,26.7,0,25.3,0z M4,9.3h24c0.8,0,1.3,0.6,1.3,1.3V28c0,0.8-0.6,1.3-1.3,1.3H4c-0.8,0-1.3-0.6-1.3-1.3V10.7 C2.7,9.9,3.2,9.3,4,9.3z M24.7,17.3c-1.1,0-2,0.9-2,2c0,1.1,0.9,2,2,2s2-0.9,2-2C26.7,18.2,25.8,17.3,24.7,17.3z"
							/>
						</svg>
					</span>
					<span className="c-list__name">Vyhodnocení výhodnosti ceny</span>
				</li>
				<li className="c-list__item">
					<span className="c-list__icon icon-svg">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" className="icon-svg__svg">
							<path
								fill="#5620ff"
								d="M11.9,0v3.6c4,0.4,6.7,3.7,6.7,7.5c0,3.5-2.4,6.4-5.6,7.2l-1.1,3.5l-0.2,0.5c2-0.1,3.9-0.8,5.7-2l0.7,1 c1.9,0.7,3.4,2.2,4,3.6c0.7,1.5,0.7,2.2,3,4c2.3,1.7,3.8,3,4.2,3.1c0.4,0.1,1.2,0.1,2-0.7c0.8-0.8,0.9-1.6,0.7-2 c-0.1-0.4-1.4-1.9-3.1-4.2s-2.5-2.3-4-3c-1.5-0.7-3-2.1-3.6-4l-1-0.7c1.2-1.8,2-4,2-6.3C22.3,5.2,17.7,0.4,11.9,0z M9.6,0.1 C4.2,0.9,0,5.5,0,11.1c0,5.6,4,10.3,9.4,11.1v-3.7c-3.4-0.7-5.8-3.7-5.8-7.3c0-3.2,2-5.9,4.8-7l1-3.7L9.6,0.1z M10.7,0.8L6.9,14 l3.8-1.7v9.1l4.7-14.6l-4.7,2V0.8z M30,29.4c0.3,0,0.6,0.3,0.6,0.6c0,0.3-0.3,0.6-0.6,0.6c-0.3,0-0.5-0.2-0.5-0.6 C29.4,29.7,29.7,29.4,30,29.4z"
							/>
						</svg>
					</span>
					<span className="c-list__name">Rozšířené možnosti filtrování nabídek</span>
				</li>
				{/* <li className="c-list__item">
					<span className="c-list__icon icon-svg">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" className="icon-svg__svg">
							<path
								fill="#5620ff"
								d="M8,1.3v2.5L0,9.1v21.6h32V9.1l-8-5.2V1.3H8z M10.7,4h10.7v10.2L16,17.7l-5.3-3.5V4z M12,6.7v2.7h8V6.7H12z M8,7v5.5L3.8,9.8 L8,7z M24,7l4.2,2.7L24,12.5V7z M12,10.7v2.7h8v-2.7H12z M2.7,12.2L16,20.9l13.3-8.6V28H2.7V12.2z"
							/>
						</svg>
					</span>
					<span className="c-list__name">Nastavení frekvence upozornění</span>
				</li> */}
				<li className="c-list__item">
					<span className="c-list__icon icon-svg">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" className="icon-svg__svg">
							<path
								fill="#5620ff"
								d="M12.2,0.5c-1.1,0-2.1,0.6-2.8,1.3C8.7,2.6,8.2,3.5,7.7,4.6C7.1,6.1,6.6,7.8,6.1,9.5C4.7,9.9,3.5,10.4,2.6,11 c-1.1,0.7-2.1,1.7-2.1,3.1c0,1.2,0.7,2.1,1.6,2.8c0.8,0.6,1.7,1.1,2.8,1.5c0.1,0.3,0.2,0.6,0.3,0.9c-1.1,0.6-2.8,1.8-4.5,4.1L0,24.3 l1.1,0.8L5.3,28l-1.7,3.5h24.8L26.7,28l4.2-2.9l1.1-0.8l-0.8-1.1c-1.7-2.3-3.4-3.5-4.5-4.1c0.1-0.3,0.2-0.6,0.3-0.9 c1.1-0.4,2.1-0.9,2.8-1.5c0.9-0.7,1.6-1.6,1.6-2.8c0-1.3-0.9-2.3-2.1-3.1c-1-0.6-2.1-1.1-3.5-1.5c-0.5-1.7-1-3.4-1.7-5 c-0.4-1-0.9-1.9-1.6-2.7c-0.7-0.7-1.7-1.3-2.8-1.3c-0.8,0-1.3,0.2-1.9,0.4C17.2,1,16.6,1.2,16,1.2C14.8,1.2,13.7,0.5,12.2,0.5z  M12.2,3.1c0.3,0,1.9,0.6,3.8,0.6c1,0,1.8-0.2,2.5-0.4c0.7-0.2,1.2-0.3,1.3-0.3c0.3,0,0.5,0.1,0.9,0.5C21,4,21.5,4.7,21.8,5.5 c0.7,1.6,1.2,3.8,1.8,5.8c0,0,0.1-0.1-0.1,0c-0.3,0.2-1,0.4-1.8,0.5c-1.6,0.2-3.9,0.2-5.7,0.2c-1.8,0-4.1,0-5.7-0.3 c-0.8-0.1-1.5-0.3-1.8-0.5c-0.1-0.1-0.1,0-0.2,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0.1-0.2,0.2-0.4,0.2-0.6c0,0,0,0,0,0 c0.5-1.7,1-3.5,1.6-5c0.4-0.9,0.8-1.6,1.2-2C11.7,3.2,11.9,3.1,12.2,3.1z M5.9,12.3c0.3,0.6,0.8,1.1,1.3,1.4C8,14,8.9,14.2,9.9,14.4 c1.9,0.3,4.2,0.3,6.1,0.3c1.9,0,4.1,0,6.1-0.3c1-0.1,1.9-0.3,2.7-0.8c0.5-0.3,1-0.8,1.3-1.4c0.8,0.3,1.5,0.6,1.9,0.9 c0.8,0.5,0.9,0.8,0.9,0.9c0,0.1-0.1,0.3-0.6,0.7c-0.5,0.4-1.5,0.9-2.7,1.2c-2.4,0.8-5.8,1.2-9.6,1.2S8.8,16.8,6.4,16 c-1.2-0.4-2.1-0.8-2.7-1.2s-0.6-0.6-0.6-0.7c0-0.1,0.1-0.4,0.8-0.9C4.4,12.9,5.1,12.5,5.9,12.3z M9.3,19.3c0.4,0.1,0.8,0.2,1.3,0.2 c0.2,1.1,1,2.1,2.5,2.2c1.1,0.1,2.3-0.4,2.4-1.9c0.2,0,0.4,0,0.6,0s0.4,0,0.6,0c0.1,1.4,1.3,2,2.4,1.9c1.4-0.1,2.3-1.1,2.5-2.2 c0.4-0.1,0.9-0.2,1.3-0.2l-0.1,0.8c-0.4,2.1-1.3,4.1-2.5,5.4c-1.2,1.4-2.6,2.1-4.1,2.1c-1.5,0-2.9-0.8-4.1-2.1 c-1.2-1.3-2.1-3.3-2.5-5.4L9.3,19.3z M25,21.2c0.5,0.3,1.7,1.1,3.2,2.7l-3.9,2.7l-0.9,0.6l0.5,1l0.4,0.7h-4.1c0.7-0.5,1.3-1,1.9-1.7 c1.4-1.6,2.4-3.7,2.9-6C25,21.2,25,21.2,25,21.2z M6.9,21.2c0.1,0,0.1,0.1,0.2,0.1c0.6,2.3,1.5,4.3,2.9,5.9c0.6,0.7,1.3,1.2,2,1.7 H7.8l0.4-0.7l0.5-1l-0.9-0.6l-3.9-2.7C5.2,22.4,6.4,21.5,6.9,21.2z"
							/>
						</svg>
					</span>
					<span className="c-list__name">Historie nabídek</span>
				</li>
			</ul>
		</section>

		<section className="c-list u-mb-md">
			<h2 className="title title--semicircle u-mb-sm">
				Na co se můžete <span className="u-color-secondary">těšit</span>?
			</h2>
			<ul className="c-list__list">
				<li className="c-list__item">
					<span className="c-list__icon icon-svg">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" className="icon-svg__svg">
							<path
								fill="#5620ff"
								d="M11.9,0v3.6c4,0.4,6.7,3.7,6.7,7.5c0,3.5-2.4,6.4-5.6,7.2l-1.1,3.5l-0.2,0.5c2-0.1,3.9-0.8,5.7-2l0.7,1 c1.9,0.7,3.4,2.2,4,3.6c0.7,1.5,0.7,2.2,3,4c2.3,1.7,3.8,3,4.2,3.1c0.4,0.1,1.2,0.1,2-0.7c0.8-0.8,0.9-1.6,0.7-2 c-0.1-0.4-1.4-1.9-3.1-4.2s-2.5-2.3-4-3c-1.5-0.7-3-2.1-3.6-4l-1-0.7c1.2-1.8,2-4,2-6.3C22.3,5.2,17.7,0.4,11.9,0z M9.6,0.1 C4.2,0.9,0,5.5,0,11.1c0,5.6,4,10.3,9.4,11.1v-3.7c-3.4-0.7-5.8-3.7-5.8-7.3c0-3.2,2-5.9,4.8-7l1-3.7L9.6,0.1z M10.7,0.8L6.9,14 l3.8-1.7v9.1l4.7-14.6l-4.7,2V0.8z M30,29.4c0.3,0,0.6,0.3,0.6,0.6c0,0.3-0.3,0.6-0.6,0.6c-0.3,0-0.5-0.2-0.5-0.6 C29.4,29.7,29.7,29.4,30,29.4z"
							/>
						</svg>
					</span>
					<span className="c-list__name">Další vylepšení filtrování nabídek</span>
				</li>
				<li className="c-list__item">
					<span className="c-list__icon icon-svg">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" className="icon-svg__svg">
							<path
								fill="#5620ff"
								d="M9.3,0c-2.2,0-4,1.8-4,4v24c0,2.2,1.8,4,4,4h13.3c2.2,0,4-1.8,4-4V4c0-2.2-1.8-4-4-4H9.3z M9.3,2.7h13.3 C23.4,2.7,24,3.3,24,4v24c0,0.7-0.6,1.3-1.3,1.3H9.3C8.6,29.3,8,28.7,8,28V4C8,3.3,8.6,2.7,9.3,2.7z M16,25.3 c-0.7,0-1.3,0.6-1.3,1.3S15.3,28,16,28s1.3-0.6,1.3-1.3S16.7,25.3,16,25.3z"
							/>
						</svg>
					</span>
					<span className="c-list__name">Mobilní aplikace</span>
				</li>
			</ul>
		</section>

		{/* <p className="caption u-font-secondary u-mb-xs">
			Během léta spustíme <span className="u-color-secondary">placené tarify</span>,<br /> do té doby můžete Visidoo plně využívat{' '}
			<span className="u-color-secondary">zdarma</span>.
		</p> */}
	</div>
);

type Props = {
	tariff: UserRole;
};

export const PremiumSignupForm: FC<Props> = ({ tariff }) => {
	const [isSuccess, setSuccess] = useState(false);
	const [isUserHit, setUserHit] = useState(false);
	const [isError, setError] = useState(false);
	const [isLoading, setLoading] = useState(false);
	const user = useUser();
	const defaultValues: LeadPostInputs = {
		email: user.payload.email,
		firstName: user.payload.name,
		formType: LeadFormType.PREMIUM,
		interests: [`__tariff__${tariff}`],
		lastName: user.payload.surname,
		telephone: user.payload.telephone,
	};

	const context = useForm<LeadPostInputs>({ defaultValues });

	const { refresh } = useUser();

	const handleSubmit: SubmitHandler<LeadPostInputs> = async (data) => {
		setLoading(true);

		try {
			const result = await fetchLeadPost(data);
			if (result.statusCode === 200) {
				setSuccess(true);
				setError(false);
				if (result.data.lead.userId) {
					setUserHit(true);
				}
			} else {
				setSuccess(false);
				setError(true);
			}
		} catch (e) {
			console.error(e);
			setError(true);
			setSuccess(false);
		} finally {
			setLoading(false);
			refresh();
		}
	};

	return (
		<>
			<Row lg>
				{isSuccess && isUserHit ? (
					<ArticleIntro
						title={
							<>
								<span className="u-color-secondary">Zdarma</span> jsem vám{' '}
								<span className="u-color-secondary">aktivovali</span> Profi tarif
							</>
						}
						margin="xs"
					>
						<p className="caption">
							Proč zdarma? Upřímně řečeno, ještě ladíme finální podobu tarifů
							<br /> a sbíráme zpětnou vazbu – nejspíš se ozveme i vám.
						</p>
					</ArticleIntro>
				) : isSuccess ? (
					<ArticleIntro
						title={
							<>
								Vaše registrace <span className="u-color-secondary">k&nbsp;premium účtu</span> byla&nbsp;úspěšně odeslána
							</>
						}
						margin="xs"
					>
						<p className="caption">O aktivaci premium účtu vás budeme neprodleně kontaktovat.</p>
					</ArticleIntro>
				) : (
					<ArticleIntro
						title={
							<>
								<strong>Poslední krok</strong> k&nbsp;{userRoleMap[tariff]} tarifu
							</>
						}
						margin="xs"
					>
						<p className="caption">K aktivaci vyšší verze tarifu už chybí jen pár důležitých infomací.</p>
					</ArticleIntro>
				)}
			</Row>

			<Row>
				<div className="u-mb-xs f-filter">
					<div className="f-filter__bg">
						<Icon name="rays" />
					</div>
					<Form<LeadPostInputs> context={context} onSubmit={handleSubmit}>
						{isSuccess && isUserHit ? (
							<SuccessUserView />
						) : isSuccess ? (
							<SuccessView />
						) : (
							<>
								<DefaultView isLoading={isLoading} tariff={tariff} />
								{isError && (
									<ErrorMessage>
										Bohužel se něco nepodařilo, zkuste to prosím později znovu nebo nás kontaktujte.
									</ErrorMessage>
								)}
							</>
						)}
					</Form>
				</div>
			</Row>
		</>
	);
};

PremiumSignupForm.displayName = 'PremiumSignupForm';
