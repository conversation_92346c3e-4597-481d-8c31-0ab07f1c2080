import { useUser } from 'components/user/UserProvider';
import { createContext, FC, useCallback, useContext, useEffect, useState } from 'react';

declare global {
	interface Window {
		ReactNativeWebView?: {
			postMessage: (message: string) => void;
		};
	}
}

type BridgeInterface = {
	state: { ready: boolean; error: Error | null };
	postMessage: (event: string) => void;
};

const BridgeContext = createContext<BridgeInterface>({
	state: {
		ready: false,
		error: null,
	},
	postMessage: (event: string) => void event,
});

export const useBridge = () => useContext(BridgeContext);

export const BridgeProvider: FC = ({ children }) => {
	const { login } = useUser();
	const [state, setState] = useState<BridgeInterface['state']>({ ready: false, error: null });

	const postMessage = useCallback(
		(event: string) => {
			if (!state.ready) {
				if (process.env.NODE_ENV === 'development') {
					console.error(`Bridge is not ready!`);
				}
				return;
			}

			window.ReactNativeWebView?.postMessage(event);
		},
		[state.ready],
	);

	useEffect(() => {
		const handler = (e: MessageEvent<unknown>) => {
			if (typeof e.data !== 'string') {
				return;
			}

			try {
				const { type, payload } = JSON.parse(e.data) as { type?: string; payload?: Record<string, string> };
				if (type === 'SETUP' && payload?.token) {
					login(payload.token);
					setState({ ready: true, error: null });
				}
			} catch (error) {
				setState({ ready: false, error: error instanceof Error ? error : new Error(String(error)) });
			}
		};

		window.addEventListener('message', handler);
		window.ReactNativeWebView?.postMessage('READY');

		return () => window.removeEventListener('message', handler);
	}, [login]);

	return <BridgeContext.Provider value={{ state, postMessage }}>{children}</BridgeContext.Provider>;
};

BridgeProvider.displayName = 'BridgeProvider';
