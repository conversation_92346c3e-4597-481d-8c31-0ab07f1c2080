import { FC } from 'react';
import { useBridge } from './BridgeProvider';
import { BlockLoader } from 'components/ui/core/BlockLoader';

export const BridgeLoader: FC = ({ children }) => {
	const { state } = useBridge();

	return (
		<>
			{state.ready ? (
				children
			) : state.error ? null : (
				<BlockLoader loading>
					<div style={{ padding: '4em 0 8em' }}>{null}</div>
				</BlockLoader>
			)}
		</>
	);
};

BridgeLoader.displayName = 'Bridge';
