import { fetchSimilarRealEstates } from 'api/realEstate/fetchSimilarRealEstates';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Icon } from 'components/ui/core/Icon';
import { Row } from 'components/ui/core/Row';
import { FC, useCallback, useEffect, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Offer, OfferType, PossibleOfferTypeForDetailPage, StatsPayload, VisidooPriceStats } from 'types/offer';
import { normalizeQueryParam } from 'utils/normalizeQueryParam';
import { HistoryItem } from './HistoryItem';

type Props = {
	offerType: PossibleOfferTypeForDetailPage | null;
	closeHistorySection: () => void;
	isVisible: boolean;
	visidooPriceStats: StatsPayload<VisidooPriceStats>;
	offerToCompareWith: Offer;
};

export const HistorySection: FC<Props> = ({ offerType, closeHistorySection, isVisible, visidooPriceStats, offerToCompareWith }) => {
	const { formatMessage } = useIntl();
	const saleHeading = formatMessage({ description: 'detail-otherOffersSaleTitle', defaultMessage: 'Prodávané nemovitosti' });
	const rentHeading = formatMessage({ description: 'detail-otherOffersRentTitle', defaultMessage: 'Pronajímané nemovitosti' });
	const relevantStats = offerType === OfferType.SALE ? visidooPriceStats.stats.sale : visidooPriceStats.stats.rent;

	const [similarOffers, setSimilarOffers] = useState<Record<PossibleOfferTypeForDetailPage, Offer[] | null>>({
		[OfferType.SALE]: null,
		[OfferType.RENT]: null,
	});
	const [loading, setLoading] = useState(false);
	const offerId = offerToCompareWith.id;

	const onSectionOpening = useCallback(
		async (offerType: PossibleOfferTypeForDetailPage) => {
			try {
				if (offerId && isVisible && !similarOffers[offerType]) {
					setLoading(true);
					const normalizedId = normalizeQueryParam(offerId);
					const response = await fetchSimilarRealEstates(normalizedId, offerType);
					const similarOffers = response.data.realEstates;
					setSimilarOffers((prevState) => ({ ...prevState, [offerType]: similarOffers }));
				}
			} catch (error) {
				console.error(error);
			} finally {
				setLoading(false);
			}
		},
		[isVisible, offerId, similarOffers],
	);

	useEffect(() => {
		if (offerType) {
			onSectionOpening(offerType);
		}
	}, [onSectionOpening, offerType]);

	return (
		<div className={`b-detail-side b-detail-side--other ${isVisible && 'is-visible'}`}>
			<button className="b-detail-side__close btn" onClick={closeHistorySection}>
				<Icon name="close" />
				<span className="u-vhide">
					<FormattedMessage description="close" defaultMessage="Zavřít" />
				</span>
			</button>
			<Row>
				<ArticleIntro title={offerType === OfferType.SALE ? saleHeading : rentHeading} bg margin="xs">
					<p className="caption">
						<FormattedMessage
							description={'detail-otherOffersSample'}
							defaultMessage={
								'Za poslední 3 měsíce jsme nalezli {sample} {sample, plural, one {inzerát} few {inzeráty} other {inzerátů}} do vzdálenosti {range} km.'
							}
							values={{ sample: relevantStats.sample, range: relevantStats.distance }}
						/>
					</p>
				</ArticleIntro>
				<section className="c-crossroad u-mb-xl">
					{loading ? (
						<BlockLoader loading />
					) : similarOffers ? (
						<div className="c-crossroad__list grid">
							{offerType != null &&
								similarOffers[offerType]?.map((offer) => {
									return <HistoryItem key={offer.id} offer={offer} offerToCompareWith={offerToCompareWith} />;
								})}
						</div>
					) : null}
				</section>
			</Row>
		</div>
	);
};
