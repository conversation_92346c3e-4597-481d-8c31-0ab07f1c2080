import { FC, useCallback, useEffect, useState } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { Offer } from 'types/offer';
import { ExternalImage } from 'components/ui/core/ExternalImage';
import { Icon } from 'components/ui/core/Icon';
import { FormattedMessage } from 'react-intl';

type Props = {
	offer: Offer;
};

export const PhotoCarousel: FC<Props> = ({ offer, children }) => {
	const { images: offerImages, id } = offer;
	const [emblaRef, emblaApi] = useEmblaCarousel();
	const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
	const [nextBtnEnabled, setNextBtnEnabled] = useState(false);

	const onSelect = useCallback(() => {
		if (!emblaApi) return;

		setPrevBtnEnabled(emblaApi.canScrollPrev());
		setNextBtnEnabled(emblaApi.canScrollNext());
	}, [emblaApi]);

	useEffect(() => {
		if (!emblaApi) return;
		emblaApi.on('select', onSelect);
		onSelect();
	}, [emblaApi, onSelect]);

	const scrollPrev = useCallback(() => {
		if (emblaApi) emblaApi.scrollPrev();
	}, [emblaApi]);

	const scrollNext = useCallback(() => {
		if (emblaApi) emblaApi.scrollNext();
	}, [emblaApi]);

	return (
		<div className="b-offer__embla">
			<div className="embla">
				<div className="embla__viewport" ref={emblaRef}>
					<div className="embla__container">
						{offerImages.map((imageUrl) => {
							return (
								<div className="embla__slide" key={imageUrl}>
									<div className="b-offer__img">
										<ExternalImage src={imageUrl} alt="" width="260" height="200" realEstateId={id} />
									</div>
								</div>
							);
						})}
					</div>
				</div>
				{offerImages.length > 1 && (
					<>
						<button className={`embla__prev btn ${!prevBtnEnabled ? 'embla__prev__disabled' : ''}`} onClick={scrollPrev}>
							<FormattedMessage description="prev" defaultMessage="Předchozí" />
							<Icon name="chevron-left" />
						</button>
						<button className={`embla__next btn ${!nextBtnEnabled ? 'embla__next__disabled' : ''}`} onClick={scrollNext}>
							<FormattedMessage description="next" defaultMessage="Následující" />
							<Icon name="chevron-right" />
						</button>
					</>
				)}
				{children}
			</div>
		</div>
	);
};
