import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import Image from 'next/image';

export const InsertionsPageChart: FC = () => {
	return (
		<div id="charts" className="u-mb-xl u-text-center">
			<h2 className="title u-mb-sm h3">
				<FormattedMessage description={'insertionSimplePage-distribution'} defaultMessage={'Rozdělení odběratelů e-mailů'} />
			</h2>
			<div className="grid">
				<div className="grid__cell size--12-12@md">
					<h3 className="h6">
						<FormattedMessage
							description={'insertionSimplePage-primariInterest'}
							defaultMessage={'Primární zájem našich odběratelů'}
						/>
					</h3>
					<Image src="/img/illust/chart-type.jpg" alt="Poměr prodej / pronájem" width={480} height={260} lazyBoundary="1000px" />
				</div>
				<div className="grid__cell size--6-12@md">
					<h3 className="h6 u-mb-xs">
						<FormattedMessage
							description={'insertionSimplePage-saleRatio'}
							defaultMessage={'Prodej – poměr typů nemovitostí'}
						/>
					</h3>
					<Image src="/img/illust/chart-sell.jpg" alt="Poměr prodej / pronájem" width={480} height={260} lazyBoundary="1000px" />
				</div>
				<div className="grid__cell size--6-12@md">
					<h3 className="h6 u-mb-xs">
						<FormattedMessage
							description={'insertionSimplePage-rentRatio'}
							defaultMessage={'Pronájem – poměr typů nemovitostí'}
						/>
					</h3>
					<Image src="/img/illust/chart-rent.jpg" alt="Poměr prodej / pronájem" width={480} height={260} lazyBoundary="1000px" />
				</div>
			</div>
		</div>
	);
};
