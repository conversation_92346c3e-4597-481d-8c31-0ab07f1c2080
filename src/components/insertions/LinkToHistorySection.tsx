import { FC, MouseEventHandler } from 'react';
import { FormattedMessage } from 'react-intl';
import { OfferType, PossibleOfferTypeForDetailPage, StatsPayload, VisidooPriceStats } from 'types/offer';
import { formatKm } from 'utils/formats';

type Props = {
	visidooPriceStats: StatsPayload<VisidooPriceStats>;
	onClick: MouseEventHandler<HTMLAnchorElement>;
	scope: Uncapitalize<PossibleOfferTypeForDetailPage>;
};

export const LinkToHistorySection: FC<Props> = ({ visidooPriceStats, onClick, scope }) => {
	const relevantStats = visidooPriceStats.stats[scope];
	return (
		<p>
			<FormattedMessage
				description="detail-dataSample"
				defaultMessage="Data o {adType, select, Sale {prodejích} Rent {pronájmech} other {nemovitostech}} získána na základě vzorku <strong>{sample, number} {sample, plural, one {inzerátu} other {inzerátů}}</strong> do vzdálenosti <strong>{range}</strong>."
				values={{
					adType: scope === 'sale' ? OfferType.SALE : OfferType.RENT,
					sample: relevantStats.sample,
					range: formatKm(relevantStats.distance),
				}}
			/>{' '}
			<a href="#" onClick={onClick}>
				<FormattedMessage description="detail-showAds" defaultMessage="Zobrazit inzeráty" />
			</a>
		</p>
	);
};
