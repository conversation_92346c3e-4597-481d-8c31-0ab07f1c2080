import { FC } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { InsertionsContactPerson } from 'components/intl/insertions';
import { commonInltMessages } from '../../i18n/commonIntlMessages/common';

type Props = {
	usersTotal: number;
};

export const InsertionTopIntro: FC<Props> = ({ usersTotal }) => {
	const intl = useIntl();
	const { insertionIntlString } = commonInltMessages;

	return (
		<>
			<ArticleIntro title={intl.formatMessage(insertionIntlString)} bg margin="sm">
				<p className="caption">
					<FormattedMessage
						description={'insertionSimplePage-advertise'}
						defaultMessage={'Hledáte dobře cílenou reklamu na zákazníky hledající reality? Inzerujte u nás a buďte vidět.'}
					/>
				</p>
			</ArticleIntro>
			<div className="u-mb-lg">
				<p className="u-font-lg">
					<FormattedMessage
						description={'insertionSimplePage-targeted'}
						defaultMessage={
							'Visidoo je hlídač realitních inzerátů a našimi uživateli jsou pouze aktivní zájemci o prodej nebo pronájem domů, bytů či pozemků. Nabízíme možnost <strong>velmi specificky cílené e-mailové reklamy</strong> pro toto publikum.'
						}
						values={{
							strong: (...chunks: string[]) => <strong>{chunks}</strong>,
						}}
					/>
				</p>
				<ul>
					<li>
						<FormattedMessage
							description={'insertionSimplePage-emaiĺsSent'}
							defaultMessage={
								'Aktuálně posíláme e-maily <strong>{usersTotal} aktivním uživatelům</strong>, kteří právě hledají nové bydlení. {br}Jedná se o zájemce o koupi domu, bytu nebo pozemku a také o uživatele hledající pronájem bytu nebo domu (<a>více informací</a>).'
							}
							values={{
								strong: (...chunks: string[]) => <strong>{chunks}</strong>,
								usersTotal: intl.formatNumber(usersTotal),
								br: <br />,
								a: (...chunks: string[]) => <a href="#charts">{chunks}</a>,
							}}
						/>
					</li>
					{/* <li>
						Těmto uživatelům zasíláme <strong>300.000 e-mailů</strong> měsíčně.
					</li> */}
					<li>
						<FormattedMessage
							description={'insertionSimplePage-adPrice'}
							defaultMessage={'Cena reklamy se odvíjí od <strong>počtu zobrazení banneru</strong>.'}
							values={{
								strong: (...chunks: string[]) => <strong>{chunks}</strong>,
							}}
						/>
					</li>
					<li>
						<FormattedMessage
							description={'insertionSimplePage-notInSpam'}
							defaultMessage={
								'E-mailové nabídky s reklamou <strong>nekončí</strong> ve složkách pro <strong>hromadné zprávy</strong> ani <strong>ve spam</strong>.'
							}
							values={{
								strong: (...chunks: string[]) => <strong>{chunks}</strong>,
							}}
						/>
					</li>
					<li>
						<FormattedMessage
							description={'insertionSimplePage-measurable'}
							defaultMessage={
								'Oproti oborovým tištěným časopisům lze Vaši reklamu u nás <strong>měřit</strong> a vyhodnocovat (zobrazení a klikatelnost).'
							}
							values={{
								strong: (...chunks: string[]) => <strong>{chunks}</strong>,
							}}
						/>
					</li>
				</ul>
				<p>
					<InsertionsContactPerson />
				</p>
			</div>
		</>
	);
};
