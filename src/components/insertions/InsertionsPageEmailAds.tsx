import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import Image from 'next/image';

export const InsertionsPageEmailAds: FC = () => {
	return (
		<>
			<h2 className="title title--semicircle u-mb-sm h3">
				<FormattedMessage description={'insertionSimplePage-emailAds'} defaultMessage={'E-mailová reklama'} />
			</h2>
			<p className="u-font-lg">
				<FormattedMessage
					description={'insertionSimplePage-emailNotification'}
					defaultMessage={'Reklamu umisťujeme do e-mailů s upozorněním <strong>na novou nabídku reality</strong>.'}
					values={{
						strong: (...chunks: string[]) => <strong>{chunks}</strong>,
					}}
				/>
			</p>
			<p>
				<FormattedMessage
					description={'insertionSimplePage-bannerSize'}
					defaultMessage={
						'Rozměr banneru je <strong>520\u00d7250px</strong> v desktop verzi a <strong>320\u00d7150px</strong> v mobilní verzi.'
					}
					values={{
						strong: (...chunks: string[]) => <strong>{chunks}</strong>,
					}}
				/>
			</p>
			<p>
				<Image
					src="/img/illust/advertising-email.jpg"
					alt="Ukázka reklamy v e-mailu"
					width={842}
					height={616}
					lazyBoundary="1000px"
				/>
			</p>
		</>
	);
};
