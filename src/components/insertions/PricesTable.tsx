import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import { FormattedPrice } from 'components/intl/prices';
import { DiscountFormattedMessage, PricePerImpressionFormattedMessage, ReachEstimateFormattedMessage } from 'components/intl/insertions';

type Props = {
	users: number;
	usersWeek: number;
	usersMonth: number;
	priceDefault: number;
	emailShow: number;
};

export const PricesTable: FC<Props> = ({ users, usersWeek, usersMonth, priceDefault, emailShow }) => {
	return (
		<table className="u-text-left">
			<thead>
				<tr>
					<th>
						<FormattedMessage
							description={'insertionSimplePage-modelEstimate'}
							defaultMessage={'Modelové výpočety pro <strong>otevřené</strong> e-maily'}
							values={{
								strong: (...chunks: string[]) => <strong className="u-color-secondary">{chunks}</strong>,
							}}
						/>
					</th>
					<th>
						<FormattedMessage description={'insertionSimplePage-tablePrice'} defaultMessage={'Cena'} />
					</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<th>
						<FormattedMessage
							description={'insertionSimplePage-mailsOpenPerDay'}
							defaultMessage={'Všechny otevřené e-maily za den'}
						/>
						<br />{' '}
						<span className="u-color-grey u-font-sm">
							<ReachEstimateFormattedMessage usersNumber={users} emailsNumber={users * emailShow} />
						</span>
						<br />{' '}
						<span className="u-color-secondary">
							<DiscountFormattedMessage percentage={10} />
						</span>
					</th>
					<td>
						<FormattedPrice price={users * priceDefault * emailShow * 0.9} />
						<br />{' '}
						<span className="u-color-grey u-font-sm">
							<PricePerImpressionFormattedMessage priceToDisplay={priceDefault * 0.9} />
						</span>
					</td>
				</tr>
				<tr>
					<th>
						<FormattedMessage
							description={'insertionSimplePage-mailsOpenPerWeek'}
							defaultMessage={'Všechny otevřené e-maily za týden'}
						/>
						<br />{' '}
						<span className="u-color-grey u-font-sm">
							<ReachEstimateFormattedMessage usersNumber={usersWeek} emailsNumber={users * emailShow * 7} />
						</span>
						<br />{' '}
						<span className="u-color-secondary">
							<DiscountFormattedMessage percentage={30} />
						</span>
					</th>
					<td>
						<FormattedPrice price={users * priceDefault * emailShow * 7 * 0.7} />
						<br />{' '}
						<span className="u-color-grey u-font-sm">
							<PricePerImpressionFormattedMessage priceToDisplay={priceDefault * 0.7} />
						</span>
					</td>
				</tr>
				<tr>
					<th>
						<FormattedMessage
							description={'insertionSimplePage-mailsOpenPerMonth'}
							defaultMessage={'Všechny otevřené e-maily za měsíc'}
						/>
						<br />{' '}
						<span className="u-color-grey u-font-sm">
							<ReachEstimateFormattedMessage usersNumber={usersMonth} emailsNumber={users * emailShow * 31} />
						</span>
						<br />{' '}
						<span className="u-color-secondary">
							<DiscountFormattedMessage percentage={50} />
						</span>
					</th>
					<td>
						<FormattedPrice price={users * priceDefault * emailShow * 31 * 0.5} />
						<br />{' '}
						<span className="u-color-grey u-font-sm">
							<PricePerImpressionFormattedMessage priceToDisplay={priceDefault * 0.5} />
						</span>
					</td>
				</tr>
				<tr>
					<th>
						<FormattedMessage
							description={'insertionSimplePage-credit'}
							defaultMessage={'Předplacený kredit pro reklamu dle požadavků inzerenta'}
						/>
						<br />{' '}
						<span className="u-color-grey u-font-sm">
							<FormattedMessage
								description={'insertionSimplePage-prepaidCost'}
								defaultMessage={'(Cena za zobrazení klesá s výší předplaceného kreditu)'}
							/>
						</span>
					</th>
					<td>
						<FormattedMessage description={'insertionSimplePage-minimum'} defaultMessage={'Min. '} />{' '}
						<FormattedPrice price={1000} />
						<br />{' '}
						<span className="u-color-grey u-font-sm">
							<PricePerImpressionFormattedMessage priceToDisplay={priceDefault} />
						</span>
					</td>
				</tr>
			</tbody>
		</table>
	);
};
