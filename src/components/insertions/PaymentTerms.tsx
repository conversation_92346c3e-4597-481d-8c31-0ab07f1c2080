import { FC } from 'react';
import { FormattedMessage } from 'react-intl';

export const PaymentTerms: FC = () => {
	return (
		<div className="u-mb-xl">
			<h2 className="h3 title title--semicircle u-mb-sm">
				<FormattedMessage description={'insertionSimplePage-paymentTerms'} defaultMessage={'Platební podmínky'} />
			</h2>
			<ul>
				<li>
					<FormattedMessage
						description={'insertionSimplePage-invoice'}
						defaultMessage={'Faktura vydavatelství je splatná do 14 dnů ode dne jejího vystavení.'}
					/>
				</li>
				<li>
					<FormattedMessage
						description={'insertionSimplePage-deposit'}
						defaultMessage={
							'Visidoo si vyhrazuje právo na zaplacení zálohy na cenu inzerce před jejím uveřejněním, a to až do výše 100 % ceny inzerce.'
						}
					/>
				</li>
				<li>
					<FormattedMessage
						description={'insertionSimplePage-cancellation'}
						defaultMessage={'Storno zadané inzerce lze učinit pouze písemně.'}
					/>
				</li>
				<li>
					<FormattedMessage
						description={'insertionSimplePage-priceValidity'}
						defaultMessage={'Výše uvedené ceny jsou platné od 1. 1. 2022.'}
					/>
				</li>
			</ul>
		</div>
	);
};
