import { OfferBox } from 'components/ui/offer/OfferBox';
import { FC } from 'react';
import { Offer } from 'types/offer';

type Props = {
	offer: Offer;
	offerToCompareWith: Offer;
};

export const HistoryItem: FC<Props> = ({ offer, offerToCompareWith }) => {
	return (
		<div className="c-crossroad__item grid__cell">
			<OfferBox offer={offer} variant="detailed" offerToCompareWith={offerToCompareWith} />
		</div>
	);
};
