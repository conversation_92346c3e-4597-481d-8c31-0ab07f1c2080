import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useUser } from 'components/user/UserProvider';
import { useRouter } from 'next/router';
import { FC, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { TEN_SECONDS } from 'utils/constants';

export const EmailFilterEdit: FC = () => {
	const router = useRouter();
	const intl = useIntl();
	const { addNotification } = useNotifications();
	const { loggedOut } = useUser();

	const emailId = Array.isArray(router.query.emailId) ? router.query.emailId[0] : router.query.emailId;
	const filterId = Array.isArray(router.query.filterId) ? router.query.filterId[0] : router.query.filterId;

	const loginIntlString = intl.formatMessage({
		description: 'login-message',
		defaultMessage: 'Pro úpravy Va<PERSON><PERSON> filtrů se prosím přihlašte.',
	});

	useEffect(() => {
		if (!router.isReady) return;

		if (loggedOut && emailId && filterId) {
			addNotification({
				message: loginIntlString,
				timeout: TEN_SECONDS,
			});
		}
	}, [addNotification, emailId, filterId, loggedOut, loginIntlString, router.isReady]);

	return null;
};

EmailFilterEdit.displayName = 'EmailFilterEdit';
