import { useUser } from 'components/user/UserProvider';
import { createContext, FC, useCallback, useState } from 'react';
import { Filter } from 'types/filter';

type FilterDataContextValue = {
	data: Filter | null;
	filters: Filter[];
	changeData?: (newData: Filter) => void;
	changeFilters: (newFilters: Filter[]) => void;
	updateFilter?: (filter: Filter, previousId: string) => void;
	removeFilter?: (filterId: string) => void;
};

export const FilterDataContext = createContext<FilterDataContextValue>({
	data: null,
	filters: [],
	changeFilters: (newFilters) => void newFilters,
});

export const FilterDataProvider: FC = ({ children }) => {
	const { refresh } = useUser();
	const [data, setData] = useState<Filter | null>(null);
	const [filters, setFilters] = useState<Filter[]>([]);

	const changeData = useCallback(
		(newData: Filter) => {
			setData(newData);
			refresh();
		},
		[refresh],
	);

	const changeFilters = useCallback((newFilters: Filter[]) => {
		setFilters(newFilters);
	}, []);

	const updateFilter = useCallback((filter: Filter, previousId: string) => {
		setFilters((filters) => {
			return filters.map((item) => (item.id === previousId ? filter : item));
		});
	}, []);

	const removeFilter = useCallback(
		(filterId: string) => {
			setFilters((filters) => filters.filter((item) => item.id !== filterId));

			refresh();
		},
		[refresh],
	);

	return (
		<FilterDataContext.Provider value={{ data, filters, changeData, changeFilters, removeFilter, updateFilter }}>
			{children}
		</FilterDataContext.Provider>
	);
};
