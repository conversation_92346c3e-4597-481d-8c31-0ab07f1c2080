.u-text-left {
	text-align: left;
}
.u-text-right {
	text-align: right;
}
.u-text-center {
	text-align: center;
}
.u-text-justify {
	text-align: justify;
}
.u-td-dotted {
	outline: none;
	text-align: left;
	text-decoration: underline;
	text-decoration-style: dotted;
	cursor: zoom-in;
	text-underline-offset: 3px;
	text-decoration-thickness: 1px;
}
.u-text-nowrap {
	white-space: nowrap;
}
.u-text-lowercase {
	text-transform: lowercase;
}
.u-text-uppercase {
	text-transform: uppercase;
}
.u-text-capitalize {
	text-transform: capitalize;
}
.u-text-truncate {
	@include text-truncate();
}
.u-font-light {
	font-weight: 300;
}
.u-font-regular {
	font-weight: normal;
}
.u-font-bold {
	font-weight: bold;
}
.u-font-italic {
	font-style: italic;
}
.u-font-medium {
	font-weight: 500;
}
.u-font-standard {
	font-size: $fontSize;
	line-height: $lineHeight;
}
.u-font-xs {
	font-size: 13px;
	line-height: 19px;
}
.u-font-sm {
	font-size: 15px;
	line-height: 21px;
}
.u-font-md {
	font-size: $fontSize;
	line-height: $lineHeight;
}
.u-font-lg {
	font-size: 21px;
	line-height: 29px;
}
.u-font-secondary {
	font-family: $fontSecondary;
}
