.f-filter {
	position: relative;
	border-radius: $radius;
	background-color: $colorWhite;
	box-shadow: 0 0 25px rgba(0, 0, 0, 0.1);

	&__bg {
		position: absolute;
		top: -80px;
		right: -80px;
	}

	&__inner {
		padding: 20px 15px 10px;
	}

	&__holder {
		max-width: 450px;
		margin: 0 auto;
	}

	&__container {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;
	}

	&__tooltip {
		margin: 0 0 10px 10px;
	}

	&__link {
		margin: 0 0 30px;
		font-size: 16px;

		a {
			color: $colorText;
		}
	}

	&__bottom {
		padding: 20px 15px 10px;
		background-color: $colorWhite;
		box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.05);

		a {
			color: $colorText;
		}
	}

	&__bottom-inp {
		margin-bottom: 40px;
	}

	strong {
		color: $colorBlack;
		font-weight: normal;
	}

	// MEDIA QUERIEs
	@media ($lgDown) {
		&__bg {
			display: none;
		}
	}

	@media ($mdDown) {
		.h3 {
			font-size: 20px;
		}
	}

	@media ($smUp) {
		&__selects {
			display: flex;
			flex-wrap: wrap;
			align-items: flex-end;

			> .select,
			> .inp-row {
				flex: 1;
				margin-bottom: 0;
			}

			> .inp-label {
				margin: 0 15px 8px;
			}
		}
	}
	@media ($mdUp) {
		&__inner,
		&__bottom {
			padding: 25px 30px 15px;
		}
	}
	@media ($lgUp) {
		// VARIANTs
		&__inner--extend {
			padding: 35px 30px 25px;
		}
	}
}
