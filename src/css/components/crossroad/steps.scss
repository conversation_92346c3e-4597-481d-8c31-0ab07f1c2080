.c-steps {
	text-align: center;
	&__list {
		display: flex;
		flex-wrap: wrap;
		margin: 0 0 0 -40px;
	}
	&__item {
		position: relative;
		flex: 1 1 auto;
		width: 100%;
		padding: 0 0 0 40px;
	}
	&__arrow {
		position: relative;
		display: block;
		width: 50px;
		color: $colorAshGray;
		&::before {
			content: '';
			display: block;
			padding-top: percentage(22 / 80);
		}
		svg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			fill: currentcolor;
		}
	}

	@media ($lgDown) {
		&__item {
			padding-top: 80px;
			&:first-child {
				padding-top: 0;
			}
		}
		&__arrow {
			position: relative;
			top: -40px;
			margin: 0 auto;
			transform: rotate(90deg);
		}
	}

	@media ($lgUp) {
		&__item {
			width: 25%;
		}
		&__arrow {
			position: absolute;
			top: 24px;
			left: 20px;
		}
	}

	@media ($xlUp) {
		&__list {
			margin: 0 0 0 -80px;
		}
		&__item {
			padding: 0 0 0 80px;
		}
		&__arrow {
			left: 0;
			width: 80px;
		}
	}
}
