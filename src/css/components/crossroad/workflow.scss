.c-workflow {
	$s: '.c-workflow';
	&__item {
		counter-increment: counter;
		margin-bottom: 40px;
	}
	&__inner {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	& &__img-wrapper {
		position: relative;
		&::before {
			content: '0' counter(counter);
			position: absolute;
			top: 80px;
			right: -40px;
			z-index: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 80px;
			height: 80px;
			border: 10px solid $colorWhite;
			border-radius: 50px;
			background-color: $colorSecondary;
			color: $colorWhite;
			font-family: $fontSecondary;
			font-size: 20px;
		}
	}
	&__img {
		position: relative;
		display: flex;
		flex: 0 0 auto;
		justify-content: center;
		align-items: center;
		width: 200px;
		height: 250px;
		border-radius: 0 0 150px 150px;
		background: $colorBg;
		overflow: hidden;
		> div {
			vertical-align: top;
			width: 100% !important; // stylelint-disable-line declaration-no-important
			height: 100% !important; // stylelint-disable-line declaration-no-important
		}
		img {
			width: auto;
			max-width: none;
			max-height: 100%;
			object-fit: cover;
		}
	}
	&__icon {
		position: absolute;
		bottom: 20px;
		left: 50%;
		width: 50px;
		border: 15px solid $colorSecondary;
		border-radius: 100px;
		background: $colorSecondary;
		color: $colorWhite;
		outline: 2px solid $colorWhite;
		transform: translateX(-50%);
	}
	&__content {
		flex: 1 1 auto;
		max-width: 530px;
	}
	&__title {
		margin: 0 0 25px;
	}
	&__annot {
		color: $colorDarkGray;
	}

	// VARIANTs
	&__item:nth-child(2n) {
		#{$s}__img-wrapper {
			&::before {
				left: -40px;
			}
		}
		#{$s}__img {
			border-radius: 150px 150px 0 0;
		}
	}

	&__img--bd {
		border: 1px solid $colorBd;
		box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.05);
	}

	&__img--top.c-workflow__img--top.c-workflow__img--top {
		border-radius: 0 0 150px 150px;
	}

	// MEDIA QUERIEs
	@media ($mdUp) {
		&__content {
			padding: 0 0 0 50px;
		}
		&__item:nth-child(2n) {
			#{$s}__inner {
				flex-direction: row-reverse;
			}
			#{$s}__content {
				padding: 0 50px 0 0;
			}
		}
		&__img {
			width: 270px;
			height: 320px;
		}
		&__icon {
			width: 60px;
		}
	}

	@media ($lgUp) {
		&__img {
			width: 300px;
			height: 350px;
		}
	}

	@media ($mdDown) {
		text-align: center;
		&__item:nth-child(2n) {
			#{$s}__inner {
				flex-direction: column;
			}
		}
		&__inner {
			flex-direction: column;
		}
		&__img-wrapper {
			margin-bottom: 25px;
		}
	}
}
