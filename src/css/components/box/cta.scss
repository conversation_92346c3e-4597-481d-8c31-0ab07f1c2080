.b-cta {
	padding: 60px 0;
	text-align: center;
	.inp-row:not(.has-error) .inp-text {
		background: $colorWhite;
	}
	&__text {
		margin: 0 0 20px;
		color: $colorDarkGray;
	}

	// VARIANTs
	&__img--multiply {
		mix-blend-mode: multiply;
	}

	&--auto {
		padding-right: 20px;
		padding-left: 20px;
	}
	&--auto &__content {
		max-width: 690px;
		margin: 0 auto;
		padding: 0;
		text-align: center;
	}

	// MEDIA QUERIEs
	@media ($mdDown) {
		&__content {
			max-width: 450px;
			margin: 0 auto 40px;
		}
		&__img {
			max-width: 250px;
			margin: 0 auto;
		}
	}

	@media ($smDown) {
		&__img {
			max-width: 200px;
		}
	}

	@media ($mdUp) {
		.mother:not(.mother--detail) &:not(.b-cta--auto) {
			display: flex;
			align-items: center;
			min-height: 437px;
		}
		.mother:not(.mother--detail) &:not(.b-cta--auto) &__inner {
			display: flex;
			align-items: center;
		}
		.mother:not(.mother--detail) &:not(.b-cta--auto) &__content {
			flex: 1 1 auto;
			padding-right: 40px;
			text-align: left;
		}
		.mother:not(.mother--detail) &:not(.b-cta--auto) &__text {
			max-width: 510px;
		}
		.mother:not(.mother--detail) &:not(.b-cta--auto) &__img {
			flex: 0 0 auto;
			width: 300px;
		}

		// VARIANTs
		&--auto {
			min-height: auto;
			padding-right: 30px;
			padding-left: 30px;
		}
	}
}
