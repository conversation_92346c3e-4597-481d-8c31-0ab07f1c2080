.b-badges {
	&__list {
		display: grid;
		grid-template-columns: repeat(1, minmax(0, 1fr));
		grid-template-rows: auto;
		gap: 30px;
	}
	&__item {
		display: flex;
	}
	&__img {
		position: relative;
		display: flex;
		flex: 0 0 auto;
		justify-content: center;
		align-items: center;
		width: 70px;
		height: 70px;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 45px;
			height: 45px;
			border-radius: 50%;
			background: $colorLightPurple;
		}
	}
	&__content {
		flex: 1 1 auto;

		& > :last-child {
			margin-bottom: 0;
		}
	}

	// MEDIA QUERIEs
	@media ($smDown) {
		&__img {
			> span {
				transform: scale(0.8);
			}
		}
	}
	@media ($smUp) {
		&__img {
			width: 90px;
			height: 90px;
			margin-right: 0;
			&::before {
				width: 60px;
				height: 60px;
			}
		}
	}
	@media ($mdUp) {
		&__list {
			grid-template-columns: repeat(2, minmax(0, 1fr));
			gap: 40px 50px;
		}

		// VARIANTs
		&--vertical &__list {
			grid-template-columns: repeat(3, minmax(0, 1fr));
			gap: 40px 35px;
		}
		&--vertical &__item {
			flex-direction: column;
		}
	}
}
