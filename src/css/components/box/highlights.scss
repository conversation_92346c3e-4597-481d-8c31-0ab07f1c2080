.b-highlights {
	padding-top: 40px;
	text-align: center;
	&__list {
		@extend %reset-ul;
		color: $colorDarkGray;
		text-align: center;
	}
	&__item {
		@extend %reset-ul-li;
		margin-bottom: 30px;
	}
	&__icon {
		display: block;
		margin: 0 auto 15px;
		.icon-svg {
			width: 50px;
		}
	}
	&__title {
		max-width: 250px;
		margin: 0 auto 15px;
		color: $colorText;
		font-size: 19px;
		line-height: math.div(28, 20);
	}

	// MEDIA QUERIEs
	@media ($mdDown) {
		&__text {
			max-width: 400px;
			margin: 0 auto;
		}
	}

	@media ($mdUp) {
		&__list {
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
			margin: 0 0 -30px -30px;
		}
		&__item {
			flex: 0 0 auto;
			width: 50%;
			margin: 0;
			padding: 0 0 30px 30px;
		}
		&__title {
			font-size: 20px;
		}
	}

	@media ($lgUp) {
		&__item {
			width: 25%;
		}

		// VARIANTs
		&--row &__list {
			text-align: left;
		}
		&--row &__item {
			position: relative;
			width: 33.333%;
			padding-left: 110px;
		}
		&--row &__icon {
			position: absolute;
			top: 0;
			left: 30px;
		}
		&--row &__title {
			max-width: none;
		}

		&--lg {
			padding-top: 0;
		}
		&--lg &__item {
			width: 33.333%;
		}
	}
}
