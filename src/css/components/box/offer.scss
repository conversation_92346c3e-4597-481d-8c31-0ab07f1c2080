.b-offer {
	display: flex;
	flex-direction: column;
	min-height: 400px;
	padding: 20px;
	border: 1px solid $colorBd;
	border-radius: $radius;
	&__img {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 20px;
		padding-top: percentage(200 / 260);
		img {
			position: absolute;
			&:not([src*='logo-visidoo.svg']) {
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
			&[src*='logo-visidoo.svg'] {
				top: 50%;
				left: 50%;
				width: 90%;
				transform: translate(-50%, -50%);
			}
		}
	}
	&__flag1 {
		position: absolute;
		top: 5px;
		right: 10px;
		left: 10px;
	}
	&__flag2 {
		position: absolute;
		right: 10px;
		bottom: 10px;
	}
	&__flag3 {
		position: absolute;
		right: 10px;
		bottom: 30px;
	}
	&__title {
		margin: 0 0 12px;
		font-family: $fontPrimary;
		font-size: $fontSize;
	}
	&__link {
		color: $colorText;
	}
	&__address {
		color: $colorDarkGray;
	}
	&__details {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-top: auto;
		.icon-svg {
			top: -1px;
		}
	}

	&--full {
		min-height: 0;
	}
	&--full &__link {
		color: $colorLink;
	}
	&--full &__details {
		gap: 10px;
		justify-content: flex-start;
	}

	@media ($mdUp) {
		&--full &__title {
			font-size: 20px;
		}
		&:not(.b-offer--full) &__content {
			display: flex;
			flex: 1 1 auto;
			flex-direction: column;
			.link-extend__unmask {
				margin-top: auto;
			}
			.b-offer__details:not(:last-child) {
				margin-top: 0;
			}
		}
	}

	@media (min-width: 1400px) {
		&__embla {
			flex: 0 0 auto;
			width: 400px;
			margin-right: 20px;
		}

		// VARIANTs
		&--full {
			display: flex;
			flex-direction: row;
		}
		&--full &__img {
			margin: 0;
		}
		&--full &__content {
			display: flex;
			flex: 1 1 auto;
			flex-direction: column;
		}
		&--full &__title {
			font-size: 24px;
		}
		&--full &__details {
			margin-top: auto;
		}

		.b-product-detail__img &__embla {
			width: 100%;
		}
	}

	@mixin dl() {
		dl {
			column-gap: 20px;
			column-count: 2;
		}
	}

	@media ($smUp) {
		@media ($mdDown) {
			@include dl();
		}
	}

	@media ($lgUp) {
		@include dl();
	}
}
