$delay: 1s;

.b-annot {
	position: relative;
	padding: 104px 0 0;

	&:first-child {
		margin-top: -124px;
	}

	&__animation {
		position: relative;
		border-bottom: 1px solid $colorBd;

		&::before {
			content: '';
			display: block;
			padding-top: percentage(572 / 600);
		}

		video {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
		}
	}

	&__logo {
		position: absolute;

		img {
			position: absolute;
			width: 150px;
			max-width: none;
			height: auto;
			opacity: 0;
			transform: translate(-50%, -50%);
		}
	}

	&__other {
		position: absolute;
		bottom: 5%;
		left: 50%;
		padding: 5px 20px;
		border-radius: $radius;
		background: $colorBlack;
		color: $colorWhite;
		font-weight: bold;
		opacity: 0;
		transform: translateX(-50%);
	}

	&__notification {
		position: absolute;
	}

	&__notification-inner {
		position: absolute;
		top: 0;
		left: 0;
		width: 36px;
		margin: -45px 0 0 -18px;
		border-radius: 100px;
		background: $colorPrimary;
		color: $colorWhite;
		font-weight: bold;
		line-height: 36px;
		text-align: center;
		opacity: 0;
		transform: scale(0);

		&:first-child {
			margin-top: -45px;
		}
	}

	&__device {
		position: absolute;
		top: 0;
		left: 50%;
		width: 377px;
		height: 723px;
		transform: translate(-50%, 0);
	}

	&__device-wrap {
		position: relative;
		width: 100%;
		height: 100%;

		&::after {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			background: url('/img/bg/animation-device.png') 50% 50% no-repeat;
			background-size: 100% 100%;
		}
	}

	&__device-inner {
		position: absolute;
		top: 22px;
		right: 23px;
		bottom: 43px;
		left: 51px;
		padding-top: 30px;
		border-radius: 20px;
		background: #000000;
		overflow: hidden;
	}

	&__device-content,
	&__device-click {
		position: absolute;
		top: 105px;
		right: 0;
		bottom: 0;
		left: 0;
	}

	&__device-content {
		img {
			max-height: 0;
		}
	}

	&__device-click {
		opacity: 0;
		transition: opacity $t;
	}

	&__device-detail {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		transform: translateX(100%);
		transition: transform $t;
	}

	// Variants
	&__notification--visidoo,
	&__logo--visidoo {
		top: 50%;
		left: 50%;

		img {
			width: 200px;
			height: 66px;
			opacity: 1;
		}
	}

	&__notification--sreality,
	&__logo--sreality {
		top: 10%;
		left: 50%;

		img {
			width: 170px;
		}
	}

	&__logo--bezrealitky {
		top: 25%;
		left: 15%;
	}

	&__logo--remax {
		top: 25%;
		left: 85%;

		img {
			width: 110px;
		}
	}

	&__logo--mm {
		bottom: 50%;
		left: 0;

		img {
			width: 90px;
		}
	}

	&__notification--idnes,
	&__logo--idnes {
		top: 50%;
		left: 100%;

		img {
			width: 100px;
		}
	}

	&__notification--unicareal,
	&__logo--unicareal {
		top: 75%;
		left: 15%;
	}

	&__logo--patreal {
		top: 75%;
		left: 85%;

		img {
			width: 110px;
		}
	}

	&__notification--visidoo &__notification-inner {
		width: 50px;
		margin: -65px 0 0 -25px;
		font-size: 30px;
		line-height: 50px;
	}

	// MEDIA QUERIEs
	@media ($mdDown) {
		&__animation {
			display: none;
		}
		&__device {
			top: -108px;
			opacity: 0;
			transform: translate(-50%, 0) scale(0.7);
		}
	}

	@media ($smDown) {
		&__other {
			padding: 3px 10px;
			font-size: 10px;
		}
		&__notification-inner {
			width: 26px;
			margin-left: -13px;
			font-size: 14px;
			line-height: 26px;

			&:first-child {
				margin-top: -30px;
			}
		}

		// VARIANTs
		&__logo--visidoo {
			img {
				width: 100px;
				opacity: 1;
			}
		}
		&__logo--sreality {
			img {
				width: 100px;
			}
		}
		&__logo--bezrealitky {
			img {
				width: 100px;
			}
		}
		&__logo--remax {
			img {
				width: 70px;
			}
		}
		&__logo--mm {
			left: 0;

			img {
				width: 40px;
			}
		}
		&__logo--idnes {
			img {
				width: 40px;
			}
		}
		&__logo--unicareal {
			img {
				width: 70px;
			}
		}
		&__logo--patreal {
			img {
				width: 60px;
			}
		}
		&__notification--visidoo &__notification-inner {
			width: 36px;
			margin-left: -18px;
			font-size: 17px;
			line-height: 36px;

			&:first-child {
				margin-top: -45px;
			}
		}
	}

	@media ($smUp) {
		// &__bg {
		// 	&::before,
		// 	&::after {
		// 		bottom: 0;
		// 	}
		// }
		&__img {
			display: flex;
			align-items: flex-end;
			margin-bottom: 15px;
		}
		&__animation {
			margin-bottom: 15px;
		}
	}

	@media ($mdUp) {
		&__animation {
			max-width: 500px;
			margin: 40px auto 0;
		}
		&__device {
			top: 50%;
			transform: translate(-50%, -50%) scale(0);
		}
	}

	@media ($mdUp) {
		@media ($lgDown) {
			.b-intro {
				margin-bottom: 40px;
			}
			&__title {
				.title {
					font-size: 36px;
				}
			}
		}
	}

	@media ($lgUp) {
		// &__bg {
		// 	&::before,
		// 	&::after {
		// 		content: '';
		// 		position: absolute;
		// 		top: 0;
		// 		bottom: 0;
		// 		left: 0;
		// 		width: 50%;
		// 		border-radius: 0 0 800px 800px;
		// 		background: $colorLightPurple;
		// 	}
		// 	&::after {
		// 		left: 50%;
		// 		border-radius: 800px 800px 0 0;
		// 		background: $colorLightOrange;
		// 	}
		// }
		&__inner {
			position: relative;
			align-items: flex-start;
			padding-right: 50%;
		}
		&__title {
			max-width: none;
		}
		&__form {
			max-width: 510px;
			margin-bottom: 25px;
		}
		&__animation {
			position: absolute;
			top: auto;
			right: 30px;
			bottom: 0;
			width: 40%;
			margin-top: 0;
		}
		&__device {
			top: 50%;
			transform: translate(-50%, -50%) scale(0);
		}
	}

	@media ($xlUp) {
		&__inner {
			padding-right: 40%;
		}
	}
}

// $s: '.b-annot';

// .b-annot__animation.is-playing {
// 	#{$s}__logo--sreality {
// 		img {
// 			animation: opacity 0.3s $delay 1 forwards alternate;
// 		}
// 	}
// 	#{$s}__logo--bezrealitky {
// 		img {
// 			animation: opacity 0.3s $delay + 0.1s 1 forwards alternate;
// 		}
// 	}
// 	#{$s}__logo--remax {
// 		img {
// 			animation: opacity 0.3s $delay + 0.2s 1 forwards alternate;
// 		}
// 	}
// 	#{$s}__logo--mm {
// 		img {
// 			animation: opacity 0.3s $delay + 0.3s 1 forwards alternate;
// 		}
// 	}
// 	#{$s}__notification--idnes,
// 	#{$s}__logo--idnes {
// 		img {
// 			animation: opacity 0.2s $delay + 0.4s 1 forwards alternate;
// 		}
// 	}
// 	#{$s}__notification--unicareal,
// 	#{$s}__logo--unicareal {
// 		img {
// 			animation: opacity 0.2s $delay + 0.5s 1 forwards alternate;
// 		}
// 	}
// 	#{$s}__logo--patreal {
// 		img {
// 			animation: opacity 0.2s $delay + 0.6s 1 forwards alternate;
// 		}
// 	}
// 	#{$s}__other {
// 		animation: opacity 0.2s $delay + 0.7s 1 forwards alternate;
// 	}

// 	#{$s}__notification--sreality #{$s}__notification-inner {
// 		animation: scale 0.4s cubic-bezier(0.64, 0.57, 0.67, 1.53) $delay + 1.5s 1 forwards alternate;
// 	}
// 	#{$s}__notification--unicareal #{$s}__notification-inner {
// 		animation: scale 0.4s cubic-bezier(0.64, 0.57, 0.67, 1.53) $delay + 1.6s 1 forwards alternate;
// 	}
// 	#{$s}__notification--idnes #{$s}__notification-inner {
// 		animation: scale 0.4s cubic-bezier(0.64, 0.57, 0.67, 1.53) $delay + 1.7s 1 forwards alternate;
// 	}

// 	#{$s}__notification--sreality,
// 	#{$s}__notification--unicareal,
// 	#{$s}__notification--idnes {
// 		animation: group-points 0.4s $delay + 3s 1 forwards;
// 	}
// 	#{$s}__notification--visidoo #{$s}__notification-inner {
// 		animation: scale 0.4s cubic-bezier(0.64, 0.57, 0.67, 1.53) $delay + 3s 1 forwards alternate;
// 	}

// 	#{$s}__device {
// 		animation: device-show 0.4s $delay + 5s 1 forwards alternate;
// 	}

// 	#{$s}__logo:not(#{$s}__logo--visidoo),
// 	#{$s}__other-wrap,
// 	#{$s}__notification--visidoo {
// 		animation: opacity-inverse 0.2s $delay + 5.4s 1 forwards alternate;
// 	}

// 	#{$s}__device-content {
// 		img {
// 			&:nth-child(1) {
// 				animation: notification-show 0.2s $delay + 6.4s 1 forwards alternate;
// 			}
// 			&:nth-child(2) {
// 				animation: notification-show 0.2s $delay + 6.5s 1 forwards alternate;
// 			}
// 			&:nth-child(3) {
// 				animation: notification-show 0.2s $delay + 6.6s 1 forwards alternate;
// 			}
// 			&:nth-child(4) {
// 				animation: notification-show 0.2s $delay + 6.7s 1 forwards alternate;
// 			}
// 		}
// 	}

// 	#{$s}__device-click {
// 		animation: opacity 0.2s $delay + 9s 1 forwards alternate;
// 	}

// 	#{$s}__device-detail {
// 		animation: detail-show 0.2s $delay + 9.4s 1 forwards alternate;
// 	}

// 	#{$s}__device-wrap {
// 		animation: opacity-inverse 0.4s $delay + 12s 1 forwards alternate;
// 	}

// 	@media ($mdDown) {
// 		#{$s}__device {
// 			animation: device-show-sm 0.4s $delay + 5s 1 forwards alternate;
// 		}
// 	}
// }

// @keyframes opacity {
// 	to {
// 		opacity: 1;
// 	}
// }

// @keyframes opacity-inverse {
// 	to {
// 		opacity: 0;
// 	}
// }

// @keyframes scale {
// 	to {
// 		opacity: 1;
// 		transform: scale(1);
// 	}
// }

// @keyframes group-points {
// 	to {
// 		top: 50%;
// 		left: 50%;
// 		opacity: 0;
// 	}
// }

// @keyframes device-show {
// 	to {
// 		transform: translate(-50%, -50%) scale(0.7);
// 	}
// }
// @keyframes device-show-sm {
// 	to {
// 		opacity: 1;
// 	}
// }

// @keyframes notification-show {
// 	to {
// 		max-height: 100px;
// 	}
// }

// @keyframes detail-show {
// 	to {
// 		transform: translate(0);
// 	}
// }
