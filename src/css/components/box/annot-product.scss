.b-annot-product {
	$s: '.b-annot-product';
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: center;
	&__icon {
		position: relative;
		display: flex;
		flex: 0 0 60px;
		justify-content: center;
		align-items: center;
		order: 1;
		width: 60px;
		height: 60px;
		border-radius: 50%;
		background-color: $colorSecondary;
		transition: background-color $t;
		&::after {
			content: '';
			position: absolute;
			right: -4px;
			bottom: -3px;
			width: 19px;
			height: 19px;
			border: 4px solid $colorWhite;
			border-radius: 15px;
			background-color: $colorAshGray;
			transition: background-color $t;
		}
		.icon-svg {
			width: 36px;
			height: 36px;
			color: $colorWhite;
		}
	}
	&__content {
		order: 3;
	}
	&__caption {
		margin: 0;
	}
	&__title {
		margin: 0;
		font-size: 22px;
	}
	&__btn {
		order: 2;
	}

	// VARIANTs
	&--online {
		#{$s}__icon {
			&::after {
				background-color: $colorGreen;
			}
		}
	}
	&--nonactive {
		#{$s}__icon {
			&::after {
				background-color: $colorOrange;
			}
		}
	}

	// MEDIA QUERIEs
	@media ($mdDown) {
		&__icon {
			margin-bottom: 15px;
		}
	}

	@media ($smDown) {
		&__content {
			width: 100%;
		}
	}

	@media ($smUp) {
		flex-wrap: nowrap;
		justify-content: flex-start;
		align-items: flex-start;
		&__content {
			flex: 0 1 100%;
			order: 2;
		}
		&__icon + &__content {
			padding: 0 15px;
		}
		&__btn {
			flex: 0 0 auto;
			order: 3;
		}
	}

	@media ($mdUp) {
		align-items: center;
		&__icon + &__content {
			padding: 0 0 0 22px;
		}
		&__title {
			font-size: 25px;
		}
	}
}
