.b-service {
	$s: &;
	display: flex;
	flex-direction: column;
	padding: 20px;
	border: 1px solid $colorBd;
	border-radius: $radius;
	background: $colorWhite;
	dl {
		overflow: hidden;
	}
	dt {
		float: left;
		clear: both;
	}
	dd {
		float: right;
	}
	&__img {
		position: relative;
		display: grid;
		height: 0;
		min-height: 130px;
		margin-bottom: 20px;
		padding-top: 50%;
		background-color: $colorBg;
		box-sizing: border-box;

		img {
			position: absolute;
			align-self: center;
			max-width: calc(100% - 30px);
			max-height: calc(100% - 30px);
			justify-self: center;
		}
	}
	&__icon {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 80px;
		height: 80px;
		transform: translate(-50%, -50%);
	}
	&__logo {
		position: absolute;
		top: 19px;
		left: 19px;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 70px;
		height: 70px;
		border-radius: $radius;
		background: $colorWhite;
		box-shadow: 0 2px 2px rgba($colorBlack, 0.05);
	}
	&__flags {
		position: absolute;
		top: 19px;
		right: -29px;
		display: flex;
		gap: 5px 0;
		flex-direction: column;
	}
	&__title {
		margin: 0 0 20px;
		font-family: $fontSecondary;
		font-weight: 500;
	}
	&__link {
		color: $colorBlack;
	}
	&__annot {
		margin: 0 0 20px;
		color: $colorText;
	}
	&__details {
		display: flex;
		gap: 0 10px;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-top: auto;
		font-size: 16px;
	}
	&__inline-link {
		font-weight: 500;
	}

	// VARIANTs
	&--purple {
		#{$s}__img {
			background-color: $colorLightPurple;
		}
	}
}
