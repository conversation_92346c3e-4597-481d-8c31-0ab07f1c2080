[data-reach-dialog-overlay] {
	z-index: 10000;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	padding: 40px 0;
}

.b-dialog {
	position: relative;
	width: 700px;
	max-width: 90vw;
	margin: 0;
	padding: 40px 20px;
	border-radius: 4px;

	&__close {
		position: absolute;
		top: 0;
		right: 0;
		width: 40px;
		height: 40px;
		padding: 0;
		border: 0;
		background: none;
		cursor: pointer;

		.hoverevents &:hover {
			color: $colorHover;
		}
	}

	&.b-dialog--md {
		width: auto;
		max-width: 500px;
	}

	&--lg {
		width: 800px;
	}

	&--preview {
		width: 650px + 2 * $gridGutter;

		iframe {
			display: block;
			width: 650px;
			height: calc(90vh - 100px);
			margin: 0 auto;
			border: none;
		}
	}
	// MEDIA QUERIEs
	@media ($mdUp) {
		padding: 60px 50px;

		&__close {
			top: 10px;
			right: 10px;
		}
	}
}
