.b-share {
	$s: '.b-share';
	display: inline-block;
	&__list {
		@extend %reset-ul;
		display: flex;
		margin-left: -10px;
	}
	&__item {
		@extend %reset-ul-li;
		margin-left: 10px;
	}
	&__link {
		color: $colorText;
		.icon-svg {
			width: 24px;
		}
	}

	// VARIANTs
	&--color {
		#{$s}__link {
			display: inline-block;
			padding: 8px;
			border-radius: $radius;
			background-color: $colorLink;
			color: $colorWhite;
			transition: background-color $t;
			.icon-svg {
				width: 24px;
			}
		}
	}

	// HOVERs
	.hoverevents &--color {
		#{$s}__link:hover {
			background-color: $colorHover;
			color: $colorWhite;
		}
	}
}
