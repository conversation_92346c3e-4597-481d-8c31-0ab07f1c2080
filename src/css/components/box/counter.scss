.b-counter {
	&__list {
		@extend %reset-ul;
		display: flex;
		flex-wrap: wrap;
		margin: 0 0 -10px -10px;
		text-align: center;
	}
	&__item {
		@extend %reset-ul-li;
		flex: 0 1 auto;
		width: calc(50% - 10px);
		margin: 0 0 10px 10px;
		padding: 15px;
		border-radius: $radius;
		background-color: $colorLightGray;
		font-size: 14px;
		line-height: math.div(18, 14);
		a {
			cursor: pointer;
		}
	}
	&__value {
		display: block;
		margin: 0;
		font-family: $fontSecondary;
		font-size: 22px;
		transition: color $t;
	}
	&__text {
		margin: 0;
	}

	// VARIANTs
	&__item--warning {
		background: $colorLightRed;
	}

	// MEDIA QUERIEs
	@media ($mdUp) {
		&__list {
			margin: 0 0 -20px -20px;
		}
		&__item {
			width: 200px;
			margin: 0 0 20px 20px;
			padding: 20px 15px 20px 20px;
		}
		&__value {
			font-size: 25px;
		}

		// VARIANTs
		&__item--full {
			width: 100%;
		}
	}
}
