.b-article {
	$s: '.b-article';
	color: $colorDarkGray;
	&__img {
		position: relative;
		margin-bottom: 20px;
		padding-top: percentage(200 / 300);
		border-radius: $radius;
		background: $colorBg;
		img {
			border-radius: $radius;
			object-fit: cover;
		}
	}
	&__flags {
		position: absolute;
		top: 5px;
		left: 10px;
	}
	&__title {
		margin: 0 0 12px;
	}
	&__annot {
		display: -webkit-box;
		overflow: hidden;
		-webkit-line-clamp: 6;
		-webkit-box-orient: vertical;
		strong {
			display: block;
			color: $colorBlack;
		}
	}
	&__link {
		color: $colorText;
	}

	// VARIANTs
	&--featured {
		font-size: 18px;
		#{$s}__title {
			margin-bottom: 15px;
			font-family: $fontSecondary;
			font-size: 27px;
		}
		#{$s}__annot {
			margin-bottom: 15px;
		}
	}

	// MEDIA QUERIEs
	@media ($mdUp) {
		&--featured {
			font-size: 22px;
			#{$s}__title {
				margin-bottom: 20px;
				font-size: 38px;
			}
			#{$s}__annot {
				margin-bottom: 20px;
			}
			#{$s}__img {
				padding-top: percentage(400 / 970);
			}
		}
	}
}
