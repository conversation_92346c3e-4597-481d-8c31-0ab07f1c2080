.b-price {
	padding: 20px;
	border: 1px solid $colorBd;
	border-radius: $radius;
	.item-icon__icon {
		color: $colorPrimary;
	}
	&__price {
		strong {
			font-size: 24px;
		}
	}
	&__bottom {
		display: flex;
		gap: 20px;
		align-items: center;
		margin-top: 20px;
		p {
			margin: 0;
		}
	}
	&__btn {
		flex: 1 1 auto;
	}

	// VARIANTs
	&--premium {
		border-color: $colorPrimary;
		outline: 3px solid rgba($colorPrimary, 0.25);
	}
	&--row &__list {
		column-gap: 20px;
		column-count: 2;
		li {
			padding-left: 26px;
			&::before {
				display: none;
			}
		}
		span {
			position: absolute !important; // stylelint-disable-line declaration-no-important
			top: 3px;
			left: 0;
		}
	}
	&__list--tick {
		li {
			&::before {
				top: 0.3em;
				left: 2px;
				width: 6px;
				height: 10px;
				border: 2px solid $colorSecondary;
				border-width: 0 2px 2px 0;
				border-radius: 0;
				background: none;
				transform: rotate(45deg);
			}
		}
	}

	@media ($mdDown) {
		&__bottom {
			flex-direction: column;
		}
	}

	@media ($mdUp) {
		&--row &__list {
			column-count: 4;
		}
	}
}
