.b-benefits {
	&__list {
		column-gap: 50px;
		column-count: 1;
		margin-bottom: -5px;
	}
	&__item {
		display: flex;
		margin: 0;
		padding: 0 0 5px;
		break-inside: avoid;

		&::before {
			display: none;
		}
	}
	&__icon {
		flex-shrink: 0;
		padding-right: 20px;

		.icon-svg {
			width: 36px;
			border: 3px solid transparent;
		}
	}

	&__text {
		margin-top: 2px;
		color: $colorDarkGray;
		font-size: 18px;
	}

	// VARIANTs
	&--expand &__item {
		padding: 0 0 20px;
	}
	&--expand &__text {
		color: $colorBlack;
		font-size: 17px;
		strong {
			display: block;
			font-size: 19px;
		}
	}

	// MEDIA QUERIEs
	@media ($mdUp) {
		&__list {
			column-count: 2;
		}

		&__text {
			font-size: 22px;
		}
	}
}
