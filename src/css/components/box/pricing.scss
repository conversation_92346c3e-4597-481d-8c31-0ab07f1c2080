.b-pricing {
	.grid {
		margin: 0 0 -20px -20px;
		&__cell {
			border-width: 0 0 20px 20px;
		}
	}
	.item-icon__icon {
		color: $colorPrimary;
	}
	&__inner {
		position: relative;
		display: flex;
		flex-direction: column;
		padding: 20px;
		border: 1px solid $colorBd;
		border-radius: $radius;
		outline: 3px solid rgba($colorBd, 0.5);
	}
	&__flag {
		position: absolute;
		top: 0;
		right: 0;
		padding: 2px 7px;
		border-radius: 0 $radius 0 $radius;
		background: $colorPrimary;
		color: $colorWhite;
		font-weight: bold;
		font-size: 12px;
		text-transform: uppercase;
	}
	&__title {
		margin-top: 0;
	}
	&__price {
		strong {
			font-size: 24px;
		}
	}
	&__list {
		font-size: 15px;
	}
	&__btn {
		margin: auto 0 0;
		a {
			text-decoration: none;
		}
	}
	&__btn-price {
		display: block;
		font-family: $fontPrimary;
		font-weight: normal;
	}

	// VARIANTs
	&__inner--premium {
		border-color: $colorPrimary;
		outline: 3px solid rgba($colorPrimary, 0.25);
	}
	&__inner--active {
		border-color: $colorSecondary;
		outline: 3px solid rgba($colorSecondary, 0.25);
	}
	&__inner--active &__flag {
		background: $colorSecondary;
	}
	&__inner--profi2 {
		border-color: rgba($colorSecondary, 0.1);
		background: rgba($colorSecondary, 0.1);
		// color: $colorWhite;
		outline: 3px solid rgba($colorSecondary, 0.05);
		// a {
		// 	color: $colorWhite;
		// 	.hoverevents &:hover {
		// 		color: $colorPrimary;
		// 	}
		// }
		// ul li {
		// 	&::before {
		// 		border-color: $colorWhite;
		// 	}
		// }
	}
	&__inner--profi {
		border-color: rgba($colorBlack, 0.25);
		background: $colorSecondary;
		color: $colorWhite;
		outline: 3px solid rgba($colorSecondary, 0.15);
		a {
			color: $colorWhite;
			.hoverevents &:hover {
				color: $colorPrimary;
			}
		}
		ul li {
			&::before {
				border-color: $colorWhite;
			}
		}
	}
	// &--row &__list {
	// 	column-gap: 20px;
	// 	column-count: 2;
	// 	li {
	// 		padding-left: 26px;
	// 		&::before {
	// 			display: none;
	// 		}
	// 	}
	// 	span {
	// 		position: absolute !important; // stylelint-disable-line declaration-no-important
	// 		top: 3px;
	// 		left: 0;
	// 	}
	// }
	&__list--tick {
		li {
			&::before {
				top: 0.3em;
				left: 2px;
				width: 6px;
				height: 10px;
				border: 2px solid $colorSecondary;
				border-width: 0 2px 2px 0;
				border-radius: 0;
				background: none;
				transform: rotate(45deg);
			}
		}
	}

	@media ($mdDown) {
		&__bottom {
			flex-direction: column;
		}
	}

	// @media ($mdUp) {
	// 	&--row &__list {
	// 		column-count: 4;
	// 	}
	// }
}
