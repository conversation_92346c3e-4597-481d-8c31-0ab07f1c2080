.b-annot-blog {
	$s: '.b-annot-blog';
	&__img {
		position: relative;
		padding-top: percentage(600/1320);
		img {
			border-radius: $radius;
			object-fit: cover;
		}
	}
	&__img-right-icon {
		position: absolute;
		top: 10px;
		right: 10px;
		z-index: 2;
		max-width: 30%;
		max-height: 100%;
		transition: all $t ease;

		.icon-svg {
			max-width: 100%;
		}
	}
	&__flags {
		position: absolute;
		top: 5px;
		left: 10px;
	}
	&__content {
		position: relative;
		max-width: $rowMainWidth;
		margin: 0 auto;
		padding: 25px 0 20px;
		border-radius: $radius;
		background: $colorWhite;
		font-size: 18px;
	}
	&__title {
		margin: 0 0 25px;
		strong {
			color: $colorSecondary;
			font-weight: normal;
		}
	}
	&__highlight {
		color: $colorSecondary;
	}
	&__annot {
		margin: 0 0 25px;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		margin: 0 0 30px;
		font-size: $fontSize;
	}
	&__item {
		@extend %reset-ul-li;
		flex: 1 1 auto;
		margin-bottom: 7px;
		&:not(:first-child) {
			margin-left: 55px;
		}
		&:last-child {
			flex-grow: 0;
		}
	}
	&__btn {
		margin: 20px 0 0;
		a {
			color: $colorText;
		}
	}

	// VARIANTs
	&--high {
		#{$s}__img {
			&::before {
				display: none;
			}
		}
	}

	// MEDIA QUERIEs
	@media ($mdUp) {
		&__img-right-icon {
			top: 50px;
			right: 50px;
		}
		&__content {
			max-width: 970px;
			padding: 40px 0;
			font-size: 22px;
		}
		&__title {
			margin: 0 0 30px;
		}
		&__annot {
			margin: 0 0 30px;
		}
	}

	@media ($lgUp) {
		&__img {
			&::before {
				content: '';
				position: absolute;
				top: -150px;
				right: -100px;
				z-index: 1;
				width: 270px;
				height: 260px;
				border: 35px solid $colorGreen;
				border-radius: 50%;
				border-color: transparent transparent $colorGreen transparent;
				transform: translateX(-50%);
			}
		}
		&__flags {
			top: 15px;
			left: 20px;
		}
		&__list {
			margin: 0 0 25px;
		}
		&__time {
			padding-left: 30px;
		}
	}

	@media ($xlUp) {
		&__content {
			width: 1130px;
			max-width: 96%;
			margin-top: -100px;
			padding: 60px 80px;
		}

		&--high {
			#{$s}__content {
				min-height: 230px;
				margin-top: -230px;
			}
		}
	}
}
