.b-product {
	$s: '.b-product';
	position: relative;
	padding: 30px 15px;
	border-radius: $radius;
	background-color: $colorWhite;
	box-shadow: 0 0 25px rgba(0, 0, 0, 0.1);
	&__bg {
		position: absolute;
		top: -80px;
		right: -90px;
	}

	&__header {
		padding-bottom: 20px;
	}
	&__content {
		padding: 20px 0;
		border-top: 1px solid $colorBd;
		&:last-child {
			padding-bottom: 0;
		}
	}
	&__middle {
		padding-top: 20px;
		border-top: 1px solid $colorBd;
	}
	&__bottom {
		padding-top: 20px;
		border-top: 1px solid $colorBd;
		p {
			margin: 0;
		}
	}
	&__dates {
		color: $colorAshGray;
	}
	&__flags {
		display: block;
		font-size: 14px;
		line-height: 1;
	}
	&__btn {
		font-size: 16px;
	}

	// VARIANTs
	&--disabled {
		.b-annot-product__icon {
			background-color: $colorAshGray;
			&::after {
				background-color: $colorAshGray;
			}
		}
		.b-counter__value {
			color: $colorAshGray;
		}
	}

	&--online {
		.b-annot-product__icon {
			&::after {
				background-color: $colorGreen;
			}
		}
	}

	&--nonactive {
		.b-annot-product__icon {
			&::after {
				background-color: $colorOrange;
			}
		}
	}

	// MEDIA QUERIEs
	@media ($lgDown) {
		&__bg {
			display: none;
		}
	}

	@media ($mdDown) {
		&__description {
			margin-bottom: 20px;
		}
		&__bottom-left {
			margin-bottom: 15px;
		}
	}

	@media ($smUp) {
		.b-dates {
			margin: 0;
		}
		&__bottom {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			align-items: center;
		}
		&__bottom-left,
		&__bottom-right {
			display: flex;
			gap: 15px;
			align-items: center;
			margin: 0;
		}
	}

	@media ($mdUp) {
		padding: 30px;
		&__content {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			padding: 30px 0;
		}
		&__content-left {
			flex: 1 1 auto;
			padding-right: 20px;
		}
		&__content-right {
			flex: 0 1 auto;
			max-width: 420px;
		}
		&__flags {
			padding: 0 20px 0 12px;
		}
	}
}
