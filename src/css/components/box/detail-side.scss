.b-detail-side {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1100;
	background: $colorWhite;
	overflow: hidden;
	overflow-y: auto;
	box-shadow: 0 0 10px rgba($colorBlack, 0.25);
	&__close {
		position: absolute;
		top: 0;
		right: 0;
		width: 60px;
		height: 60px;
		color: $colorText;
		.icon-svg {
			width: 30px;
		}
	}
	&__iframe {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border: 0;
	}

	// VARIANTs
	&--cityperformer {
		display: block;
	}
	&--cityperformer-disabled {
		background: $colorBg;
	}
	&--other {
		padding-top: 40px;
	}

	// STATEs
	.hoverevents &__close:hover {
		color: $colorLink;
	}

	@media ($mdDown) {
		bottom: 61px;
		display: none;

		// STATEs
		&.is-visible {
			display: block;
		}
	}

	@media ($mdUp) {
		// VARIANTs
		&--other {
			transform: translateX(100%);
			transition: transform $t;
		}

		// STATEs
		&--other.is-visible {
			transform: translateX(0);
		}
	}
}
