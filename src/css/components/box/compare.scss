.b-compare {
	&__wrap {
		overflow: hidden;
		overflow-x: auto;
	}
	th {
		background: none;
		.btn {
			text-align: left;
		}
	}
	tbody tr:last-child {
		td,
		th {
			border-bottom-width: 0;
		}
	}
	td {
		font-size: 15px;
		text-align: center;
		&:not(.u-align-bottom) {
			vertical-align: middle;
		}
	}
	tbody th {
		font-weight: normal;
	}
	tbody tr:hover {
		td,
		th {
			background: $colorBg;
		}
	}
	th,
	td {
		&:nth-child(3) {
			background: $colorLightOrange;
		}
	}
	th,
	td {
		&:nth-child(5) {
			background: $colorLightPurple;
		}
	}
	&__tarif {
		text-align: center;
	}
	&__tick {
		position: relative;
		top: -2px;
		display: block;
		width: 8px;
		height: 14px;
		margin: auto;
		border: 3px solid $colorGreen;
		border-width: 0 3px 3px 0;
		border-radius: 0;
		background: none;
		text-indent: -99999px;
		overflow: hidden;
		transform: rotate(45deg);
	}
	&__no {
		position: relative;
		top: -1px;
		display: block;
		width: 10px;
		height: 3px;
		margin: auto;
		background: $colorDarkGray;
		text-indent: -99999px;
		overflow: hidden;
		opacity: 0.6;
	}

	@media ($lgDown) {
		&__wrap {
			margin: 0 -35px;
		}
	}

	@media ($mdDown) {
		&__wrap {
			margin: 0 -20px;
			padding: 0 0 0 178px;
		}
		td,
		th {
			height: 42px;
			padding-right: 5px;
			padding-left: 5px;
		}
		thead th:first-child,
		tbody th,
		tfoot td:first-child {
			position: absolute;
			top: auto;
			left: 0;
			z-index: 1;
			display: flex;
			align-items: center;
			width: 180px;
			height: 43px;
			margin-top: -1px;
			padding: 0 10px;
			background: $colorWhite;
			font-size: 13px;
			line-height: 15px;
			overflow: hidden;
			pointer-events: none;
			.btn {
				line-height: 15px;
			}
		}
		tbody tr:first-child th {
			margin-top: 0;
		}
		tbody tr:last-child th {
			height: 62px;
		}
		tfoot td:first-child {
			height: 58px;
		}
	}
}
