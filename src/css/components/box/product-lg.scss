.b-product-lg {
	display: flex;
	padding: 15px;
	border: 1px solid $colorBd;
	border-radius: $radius;
	&__details {
		display: flex;
		margin-left: -10px;
		> * {
			margin-left: 10px;
		}
	}
	&__img {
		flex: 0 0 auto;
		width: 80px;
	}
	&__content {
		flex: 1 1 auto;
		padding: 0 0 0 15px;
	}

	// VARIANTs
	&--old {
		opacity: 0.75;
		filter: grayscale(100%);
	}

	@media ($mdDown) {
		font-size: 12px;
		.row-main > &:first-child {
			margin: -20px 0 20px;
		}
		&__img {
			img {
				height: 100%;
				object-fit: cover;
			}
		}
		&__title {
			font-size: 12px;
		}
		&__details {
			margin-bottom: 0;
		}
	}
	@media ($mdUp) {
		align-items: center;
		padding: 20px;
		&__img {
			width: 350px;
		}
		&__content {
			padding: 0 0 0 20px;
		}
	}
}
