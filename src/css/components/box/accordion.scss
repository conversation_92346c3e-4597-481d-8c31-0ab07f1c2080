.b-accordion {
	&__mainTitle {
		margin: 0 0 20px;
		font-size: 25px;
	}
	&__annot {
		font-size: 18px;
	}
	&__list {
		border-top: 1px solid $colorBd;
	}
	&__item {
		border-bottom: 1px solid $colorBd;
	}
	&__title {
		margin: 0;
		font-size: 16px;
	}
	&__link {
		position: relative;
		display: block;
		width: 100%;
		margin: 0;
		padding: 20px 20px 20px 54px;
		color: $colorText;
		text-align: left;
		text-decoration: none;
	}
	&__indicator {
		position: absolute;
		top: 22px;
		left: 14px;
		display: block;
		width: 16px;
		height: 16px;
		border: 2px solid $colorPrimary;
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 5px;
			left: 2px;
			display: block;
			width: 8px;
			height: 2px;
			background: $colorPrimary;
		}
		&::after {
			transform: rotate(90deg);
			transform-origin: center;
			transition: transform $t;
		}
	}
	&__content {
		padding: 0 20px 20px 54px;
	}

	// STATEs
	.js &__content {
		position: absolute;
		top: -5000px;
		left: -5000px;
		opacity: 0;
		transition: opacity $t * 2;
	}
	.hoverevents &__link:hover {
		color: $colorSecondary;
	}
	.is-open &__content {
		position: relative;
		top: auto;
		left: auto;
		opacity: 1;
	}
	.is-open &__link {
		padding-bottom: 15px;
	}
	.is-open &__indicator {
		&::after {
			transform: rotate(180deg);
		}
	}

	// MEDIA QUERIEs
	@media ($mdUp) {
		&__annot {
			font-size: 22px;
		}
	}
}
