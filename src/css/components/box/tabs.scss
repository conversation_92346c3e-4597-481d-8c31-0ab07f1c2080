.b-tabs {
	&__menu {
		margin-bottom: 20px;
		overflow: hidden;
		overflow-x: auto;
	}
	&__container {
		display: flex;
		justify-content: space-between;
		border-bottom: 2px solid $colorLightGray;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		margin-left: -20px;
	}
	&__item {
		@extend %reset-ul-li;
		flex: 0 0 auto;
		margin-left: 20px;
	}
	&__mainLink {
		margin: 0;
	}
	&__link {
		position: relative;
		display: block;
		padding: 0 0 10px;
		color: $colorDarkGray;
		text-decoration: none;
		cursor: pointer;
		&::after {
			content: '';
			position: absolute;
			right: 50%;
			bottom: -2px;
			left: 50%;
			height: 2px;
			background: $colorBlack;
			opacity: 0;
			transition: left $t, right $t, opacity $t;
		}
	}
	&__holder {
		max-width: 450px;
		margin: 0 auto;
	}
	&__fragments {
		position: relative;
	}
	&__fragment {
		position: absolute;
		left: -5000px;
		width: 100%;
		opacity: 0;
		transition: opacity $t;
	}

	// VARIANTs
	&--transparent {
		background-color: transparent;
		box-shadow: none;
	}
	&--transparent &__menu {
		margin: 0 0 30px;
	}

	// HOVERs
	&__link:hover,
	.js-tabs__link:hover {
		color: $colorText;
	}

	// STATEs
	&__link.is-active {
		color: $colorText;
		&::after {
			right: 0;
			left: 0;
			opacity: 1;
		}
	}

	&__fragment.is-active {
		position: relative;
		left: 0;
		opacity: 1;
	}

	// MEDIA QUERIEs
	@media ($smDown) {
		&__mainLink {
			display: none;
		}
	}
}
