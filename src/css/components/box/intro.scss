.b-intro {
	max-width: 1050px;
	font-family: $fontSecondary;
	font-weight: 500;
	font-size: 20px;
	h1 {
		strong {
			color: $colorSecondary;
		}
	}
	&__title {
		margin-bottom: 10px;
	}
	&__caption {
		margin-bottom: 20px;
		font-family: $fontPrimary;
	}

	// VARIANTs
	&--bg {
		position: relative;
		max-width: 100%;
		padding-right: 70px;
	}
	&--bg &__bg {
		position: absolute;
		top: 0;
		right: 0;
	}

	&--full {
		max-width: none;
		text-align: center;
	}

	// MEDIA QUERIEs
	@media ($mdDown) {
		font-size: 16px;
		.title {
			br {
				display: none;
			}
		}

		// VARIANTs
		&--lp {
			&.u-mb-md {
				margin-bottom: 20px;
			}
		}
	}

	@media ($mdUp) {
		font-size: 30px;
		&__caption {
			margin-bottom: 40px;
		}
	}

	@media ($lgUp) {
		&--bg {
			padding-right: 200px;
		}
	}

	@media ($mdDown) {
		&--bg &__bg {
			.icon-svg {
				width: 60px;
				height: 68px;
			}
		}
	}
}
