.m-breadcrumb {
	$s: &;
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 20px;
	color: $colorAshGray;
	font-size: 16px;
	&__list {
		margin: 0;
		padding: 0;
		list-style: none;
	}
	&__item {
		position: relative;
		display: inline-block;
		margin: 0;
		padding: 0;

		&::before {
			display: none;
		}
		&:not(:last-child) {
			padding-right: 34px;
			&::after {
				content: '';
				position: absolute;
				right: 10px;
				width: 14px;
				height: 14px;
				margin-top: 3px;
				background: url($svgArrowRight) no-repeat center;
				background-size: 12px;
			}
		}
	}
	&__link {
		color: $colorBlack;
		text-decoration: none;

		.hoverevents &:hover {
			color: $colorHover;
		}
	}

	// MEDIA QUERIEs
	@media ($mdUp) {
		margin-bottom: 30px;
	}
}
