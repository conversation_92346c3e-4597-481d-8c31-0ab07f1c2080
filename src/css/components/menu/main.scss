.m-main {
	$s: '.m-main';
	.icon-svg--crown {
		width: 20px;
		margin: -1px 7px 0 0;
		color: $colorPrimary;
	}
	.icon-svg--globe {
		width: 18px;
		margin-right: 7px;
	}
	.item-icon {
		pointer-events: none;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		margin-left: -15px;
		font-size: 16px;
		line-height: 20px;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
		flex: 0 0 auto;
		margin-left: 15px;
	}
	&__link {
		position: relative;
		display: block;
		width: 100%;
		padding: 11px 0 9px;
		color: $colorText;
		outline: none;
		font-weight: 500;
		text-align: center;
		text-decoration: none;
		cursor: pointer;
	}
	&__sub-list {
		@extend %reset-ul;
		top: 0;
		right: 0;
		left: 0;
		z-index: 1;
		padding: 0 20px;
		border-radius: $radius;
		background-color: $colorWhite;
		text-align: center;
		opacity: 0;
		transition: opacity $t, background-color $t;
		pointer-events: none;
	}
	&__sub-item {
		@extend %reset-ul-li;
	}
	&__sub-link {
		display: block;
		padding: 10px;
		font-weight: normal;
		text-decoration: none;
	}
	& .inactive {
		color: $colorText;
		cursor: pointer;
	}
	& .active {
		color: $colorPrimary;
		cursor: none;
		pointer-events: none;
	}
	&__toggle {
		display: none;
	}

	// VARIANTs
	&__item--dropdown {
		#{$s}__link {
			padding-right: 20px;
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				right: 0;
				width: 12px;
				height: 12px;
				margin-top: -4px;
				background-image: url($svgSelect2);
				background-position: 0 0;
				background-repeat: no-repeat;
				background-size: 12px 12px;
			}
		}
	}
	&__item--flag {
		.item-icon {
			vertical-align: middle;
			align-items: center;
			&__text {
				position: relative;
				bottom: -2px;
			}
		}
		.icon-svg {
			width: 20px;
			box-shadow: 0 0 2px rgba($colorBlack, 0.1);
		}
	}

	// STATEs
	.dropdown-is-open &__link {
		&::after {
			transform: rotate(-180deg);
		}
	}
	.dropdown-is-open &__sub-list {
		opacity: 1;
		pointer-events: visible;
	}

	.hoverevents &__link:hover {
		color: $colorHover;
	}

	// MEDIA QUERIES
	@media ($mdDown) {
		&__toggle {
			position: absolute;
			top: 50%;
			right: 20px;
			z-index: 10;
			display: block;
			width: 20px;
			height: 26px;
			margin-top: -15px;
			outline: none;
			&::before,
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				right: 0;
				left: 0;
				height: 3px;
				margin-top: -1px;
				background: $colorText;
				transition: box-shadow $t, transform $t, background-color $t;
				box-shadow: 0 -7px 0 $colorText, 0 7px 0 $colorText;
			}
		}
		&__list {
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			z-index: 1;
			display: flex;
			flex-direction: column;
			min-height: 100vh;
			margin: 0;
			padding: 80px 0 0;
			border-bottom: 1px solid $colorBd;
			background-color: $colorWhite;
			overflow: hidden;
			overflow-y: auto;
			visibility: hidden;
			opacity: 0;
			transition: opacity $t, visibility 0s $t;
		}
		&__item {
			margin: 0;
			border-top: 1px solid $colorBd;
			&:last-child {
				border-bottom: 1px solid $colorBd;
			}
		}
		&__link {
			padding: 10px 20px;
			text-align: left;
		}
		&__sub-list {
			display: none;
			padding: 0 0 10px;
		}
		&__sub-link {
			padding: 10px 20px 10px 50px;
			text-align: left;
		}

		// VARIANTs
		&__link--alone {
			padding: 8px 0 6px;
		}

		// STATEs
		&__item--dropdown {
			#{$s}__link {
				padding-right: 40px;
				&::after {
					right: 20px;
				}
			}
		}
		&__item.is-active {
			#{$s}__link {
				color: $colorPrimary;
			}
		}
		.menu-is-open &__list {
			visibility: visible;
			opacity: 1;
			transition-delay: 0s, 0s;
		}
		.menu-is-open &__toggle {
			&::before,
			&::after {
				transition-delay: 0s, 0s;
				box-shadow: none;
			}
			&::before {
				transform: rotate(45deg);
			}
			&::after {
				transform: rotate(-45deg);
			}
		}
		.dropdown-is-open &__sub-list {
			display: block;
		}
	}

	@media ($mdUp) {
		&__list {
			margin-left: -40px;
		}
		&__item {
			margin-left: 40px;
		}
		&__sub-list {
			position: absolute;
			top: 100%;
			right: auto;
			left: 50%;
			width: 180px;
			margin-top: 10px;
			padding: 10px;
			opacity: 0;
			transform: translateX(-50%);
			box-shadow: 0 0 25px rgba(0, 0, 0, 0.1);
			&::after {
				content: '';
				position: absolute;
				bottom: 100%;
				left: 50%;
				margin-left: -7px;
				border-width: 0 7px 7px;
				border-style: solid dashed;
				border-color: $colorWhite transparent;
			}
		}
		&__sub-item {
			border-bottom: 1px solid $colorBd;
			&:last-child {
				border-bottom: none;
			}
		}

		// STATEs
		&__item.is-active {
			#{$s}__link {
				color: $colorPrimary;
				&::after {
					content: '';
					position: absolute;
					bottom: 5px;
					left: 0;
					width: 100%;
					height: 2px;
					background: $colorPrimary;
				}
			}
		}
	}
}
