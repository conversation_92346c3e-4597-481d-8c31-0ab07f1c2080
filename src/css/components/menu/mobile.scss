.m-mobile {
	position: fixed;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 2000;
	display: flex;
	justify-content: space-around;
	border-top: 1px solid $colorBd;
	background: $colorWhite;
	font-weight: normal;
	font-size: 14px;
	text-align: center;
	&__link {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		height: 60px;
		padding: 0 10px;
		color: $colorText;
		text-decoration: none;
		&::before {
			content: '';
			position: absolute;
			top: -2px;
			right: 0;
			left: 0;
			height: 3px;
			background: $colorPrimary;
			opacity: 0;
			transition: opacity $t;
		}
	}
	&__icon {
		display: block;
		margin: 0 0 2px;
		.icon-svg {
			width: 24px;
		}
	}

	&__link.is-active {
		&::before {
			opacity: 1;
		}
	}

	@media ($mdUp) {
		display: none;
	}
}
