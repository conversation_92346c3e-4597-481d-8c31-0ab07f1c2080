html {
	color: $colorText;
	font-family: $fontPrimary;
	font-size: $fontSize;
	line-height: $lineHeight;
}

// Headings
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
	margin: 1.5em 0 0.5em;
	font-family: $fontSecondary;
	font-weight: 500;
	line-height: 1.25;
}
h1,
.h1 {
	font-size: 32px;

	@media ($mdUp) {
		font-size: 46px;
	}
}
h2,
.h2 {
	font-size: 27px;

	@media ($mdUp) {
		font-size: 38px;
	}
}
h3,
.h3 {
	font-size: 22px;

	@media ($mdUp) {
		font-size: 30px;
		line-height: math.div(42, 30);
	}
}
h4,
.h4 {
	font-size: 21px;

	@media ($mdUp) {
		font-size: 25px;
		line-height: math.div(35, 25);
	}
}
h5,
.h5 {
	font-size: 20px;
	line-height: math.div(28, 20);
}
h6,
.h6 {
	font-size: 16px;
	line-height: math.div(22, 16);
}

// Paragraph
p {
	margin: 0 0 $typoSpaceVertical;
}
hr {
	height: 1px;
	margin: $typoSpaceVertical 0;
	border: solid $colorBd;
	border-width: 1px 0 0;
	overflow: hidden;
}

// Blockquote
blockquote {
	margin: 0 0 $typoSpaceVertical;
	padding: 0;
	p {
		margin-bottom: 0;
	}
}

// Links
a,
.a {
	color: $colorLink;
	text-decoration: underline;
	transition: color $t;
	appearance: none;
	-webkit-tap-highlight-color: transparent;
	.hoverevents &:hover {
		color: $colorHover;
	}
}

a[href^='tel:'] {
	color: $colorText;
	text-decoration: none;
}

// Lists
ul,
ol,
dl {
	margin: 0 0 $typoSpaceVertical;
	padding: 0;
	list-style: none;
}
li {
	margin: 0 0 math.div($typoSpaceVertical, 2);
	padding: 0 0 0 20px;
}
ul {
	li {
		position: relative;
		&::before {
			content: '';
			position: absolute;
			top: 0.4em;
			left: 0;
			width: 10px;
			height: 10px;
			border-radius: 50%;
			background-color: $colorSecondary;
		}
	}
}
ol {
	counter-reset: item;
	li {
		position: relative;
		margin: 0 0 math.div($typoSpaceVertical, 1.7);
		padding: 0 0 0 20px;
		&::before {
			content: counter(item) '.';
			counter-increment: item;
			position: absolute;
			top: 0.1em;
			left: 0;
			color: $colorSecondary;
			font-family: $fontSecondary;
			font-weight: 500;
			font-size: 15px;
		}
	}
	ol {
		li {
			&::before {
				content: counter(item, lower-alpha) '.';
			}
		}
	}
}
dl {
	/* stylelint-disable-next-line selector-no-qualifying-type */
	&.two-columns {
		column-gap: 20px;
		column-count: 2;
		div {
			break-inside: avoid-column;
		}
	}
}
dt {
	margin: 0;
}
dd {
	margin: 0 0 math.div($typoSpaceVertical, 2);
	padding: 0;
}

// Tables
table {
	clear: both;
	border-collapse: collapse;
	border-spacing: 0;
	empty-cells: show;
	width: 100%;
	margin: 0 0 $typoSpaceVertical;
	border: 1px solid $colorGray;
}
caption {
	padding: 0 0 10px;
	font-weight: bold;
	text-align: left;
	caption-side: top;
}
td,
th {
	vertical-align: top;
	padding: 8px 20px;
	border: 1px solid $colorBd;
}
th {
	font-weight: 500;
	text-align: left;
}
thead th,
tfoot th {
	background: $colorLightGray;
}

// Image
figure {
	margin-bottom: $typoSpaceVertical;
}
figcaption {
	margin-top: 0.5em;
}

img {
	@media ($xlDown) {
		max-width: 100%;
		height: auto;
	}
}
.img-right {
	display: block;
	margin: 0 0 $typoSpaceVertical;
	img {
		border-radius: $radius;
	}
}
@media ($mdUp) {
	.img-right {
		float: right;
		width: 400px;
		margin: 0 0 20px 20px;
	}
}

pre {
	margin: 0 0 $typoSpaceVertical;
	padding: 20px;
	border-radius: $radius;
	background: #282b2d;
	color: #ededed;
	font-family: 'Courier New', courier, serif;
	white-space: pre-wrap;
}
