// Colors
$colorPrimary: #f58000;
$colorSecondary: #5620ff;

$colorBlack: #160f24;
$colorDarkGray: #5b5765;
$colorAshGray: #b3b3b3;
$colorGray: #eaeaea;
$colorLightGray: #f7f7f8;
$colorWhite: #ffffff;
$colorRed: #f53f00;
$colorGreen: #3ed097;
$colorYellow: #ffdb00;
$colorOrange: $colorPrimary;

$colorLightPurple: #f7f4ff;
$colorLightOrange: #fffaf2;
$colorLightYellow: #fffce5;
$colorLightRed: #feebe5;
$colorLightGreen: #d9f6eb;

$colorText: $colorBlack;
$colorBd: $colorGray;
$colorBg: $colorLightGray;
$colorLink: $colorPrimary;
$colorHover: $colorSecondary;

$colorInpPurple: #eee8ff;
$colorInpError: #fcc9b7;

$colorFacebook: #3b5998;
$colorTwitter: #1da1f2;
$colorGoogle: #dd4b39;
$colorYoutube: #ff0000;
$colorLinkedIn: #0077b5;
$colorInstagram: #c13584;
$colorPinterest: #bd081c;

// Font
$fontSystem: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, helvetica, arial, sans-serif;
$fontPrimary: tenon, sans-serif;
$fontSecondary: stolzl, sans-serif;
$fontSize: 17px;
$lineHeight: (27 / 17);

// Typography
$typoSpaceVertical: 0.8824em;

// Grid
$gridColumns: 12;
$gridGutter: 35px;
$rowMainWidth: 1040px;
$rowMainGutter: $gridGutter;

$rowMainWidthLg: 1390px;
$rowMainWidthSm: 690px;

// Transitions
$t: 0.3s;

// Radius
$radius: 4px;

// SVGs
$svgBullet: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath d='M0 0h4v4H0z'/%3E%3C/svg%3E%0A";
$svgSelect: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 5'%3E%3Cpath d='M10 0L5 5 0 0'/%3E%3C/svg%3E%0A";
$svgSelect2: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 5'%3E%3Cpath d='M1.3.2L.9.7l3.9 3.9.2.2.2-.2L9.1.7 8.7.2 5 3.9 1.3.2z'/%3E%3C/svg%3E";
$svgAsterisc: "data:image/svg+xml,%3C%3Fxml version='1.0' encoding='utf-8'%3F%3E%3Csvg width='12px' height='12px' viewBox='0 0 12 12' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns='http://www.w3.org/2000/svg'%3E%3Cg id='' fill='%23F58000'%3E%3Cpath d='M6.63281 6.07617L8.96484 9.22852L8.0625 9.82617L6 6.56836L3.9375 9.82617L3.07031 9.22852L5.40234 6.07617L1.91016 5.17383L2.25 4.20117L5.66016 5.39648L5.4375 1.57617L6.5625 1.57617L6.375 5.39648L9.75 4.20117L10.0547 5.20898L6.63281 6.07617Z' /%3E%3C/g%3E%3C/svg%3E";
$svgArrowRight: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMiAxMiIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMTIgMTIiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxwYXRoIHN0eWxlPSJmaWxsOiMxNjBmMjQiIGQ9Im0zLjYuNS0uNi42IDQuNSA0LjVMMyAxMC4xbC42LjcgNC44LTQuOS4zLS4zLS4zLS4zeiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLjA0NSAuNjIzKSIvPjwvc3ZnPgo=';
