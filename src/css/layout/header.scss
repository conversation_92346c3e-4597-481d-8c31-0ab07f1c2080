.header {
	position: relative;
	z-index: 1000;
	transition: background-color $t;
	&__inner {
		display: flex;
		justify-content: space-between;
		padding: 20px 0;
	}
	&__left {
		flex: 0 0 auto;
	}
	&__logo {
		position: relative;
		z-index: 10;
		display: block;
		margin: 0;
		padding: 0;
		font-family: $fontPrimary;
		font-size: 100%;
		line-height: 1;
	}

	// VARIANTs
	&__inner--center {
		justify-content: center;
	}

	// STATEs
	.menu-is-open & {
		background-color: $colorWhite;
	}
}
