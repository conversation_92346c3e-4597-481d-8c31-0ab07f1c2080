.footer {
	position: relative;
	&__separator {
		border-top: 1px solid $colorBd;
	}
	&__btn {
		display: flex;
		justify-content: center;
		margin: 60px auto 0;
		font-weight: 500;
		font-size: $fontSize;
		text-align: center;
		a {
			position: relative;
			color: $colorText;
			text-decoration: none;
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				right: -25px;
				z-index: -1;
				width: 60px;
				height: 60px;
				border-radius: 50%;
				background: $colorLightOrange;
				transform: translateY(-50%);
			}
		}
		.icon-svg {
			margin-top: -2px;
		}
	}
	&__inner {
		padding: 50px 0;
		font-size: 16px;
	}
	&__contacts {
		color: $colorDarkGray;
		line-height: math.div(26, 16);
	}
	&__title {
		margin: 0 0 10px;
		color: $colorText;
		font-weight: 500;
		font-size: $fontSize;
	}
	&__link {
		color: $colorDarkGray;
	}
	&__company {
		margin-bottom: 10px;
		font-weight: 500;
	}
	&__address {
		margin-bottom: 10px;
	}
	&__bottom {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 50px;
		text-align: center;
	}
	&__contact-item {
		display: inline-block;
	}
	&__copyrights {
		display: flex;
		flex-direction: column;
		margin: 0;
		padding-top: 10px;
	}
	&__nvidia {
		span {
			vertical-align: top;
		}
	}

	// VARIANTs
	&--bg {
		background: $colorBg;
	}

	// HOVERs
	.hoverevents &__link:hover {
		color: $colorText;
	}

	// MEDIA QUERIEs
	@media ($smUp) {
		&__bottom {
			flex-direction: row;
		}
		&__copyrights {
			flex-direction: row;
			padding: 0 0 0 10px;
		}
		&__nvidia {
			margin-left: auto;
		}
	}

	@media ($mdUp) {
		&__title {
			margin-bottom: 20px;
		}
	}

	@media ($lgUp) {
		&__btn {
			position: absolute;
			top: 80px;
			right: $rowMainGutter;
			margin: 0;
		}
		&__inner {
			padding: 80px 0;
		}
	}
}
