@import 'base/normalize';
@import 'base/variables';
@import 'base/mixins';
@import 'base/extends';
@import 'utilities/index';
@import 'print/index';

body {
	width: 100%;
	background: none;
	color: $colorBlack;
	font-family: $fontSystem;
	font-size: 8pt;
	line-height: 12pt;
}

fieldset {
	margin: 0;
	padding: 0;
	border: 0;
}

p,
ul,
table {
	margin: 0 0 0.4cm;
}

a {
	color: $colorBlack;
	text-decoration: none;
}

img {
	max-width: 100%;
	height: auto;
	border: 0;
}

header,
footer,
nav,
form,
iframe,
.menu,
.hero,
.adslot,
.b-cookie,
.b-bnr,
.b-social {
	display: none;
}

// Fonts
h1,
.h1 {
	font-size: 20px;
}
h2,
.h2 {
	font-size: 18px;
}
h3,
.h3 {
	font-size: 17px;
}
h4,
.h4 {
	font-size: 15px;
}

// Spacing
.u-mb-xl,
.u-mb-lg,
.u-mb-md {
	margin-bottom: 40px;
}

// Core components
.flag {
	font-size: 10px;
}
.blockquote {
	border: 1px solid $colorBd;
	page-break-inside: avoid;
}
.title {
	margin-bottom: 20px;
}

// Boxes
.b-intro {
	.caption {
		font-size: 15px;
	}
	.title {
		font-size: 24px;
	}
}
.b-article {
	&__flags {
		top: 1px;
		left: 3px;
	}
	&__title {
		margin-bottom: 5px;
		font-size: 13px;
	}

	// VARIANTs
	&--featured {
		font-size: 14px;
		.b-article__title {
			font-size: 18px;
		}
		.b-article__annot {
			font-size: 14px;
			line-height: 1.3;
		}
	}
}
.b-offer {
	min-height: 280px;
	&__flag1 {
		top: 1px;
		left: 3px;
	}
	&__flag2 {
		right: 3px;
		bottom: 3px;
	}
	&__title {
		font-size: 13px;
	}
}
.b-highlights {
	&__list {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-template-rows: auto;
		gap: 20px;
	}
	&__title {
		margin-bottom: 5px;
		font-size: 13px;
	}
}
.b-cta {
	display: flex;
	flex-direction: row;
	padding: 10px 0;
	&__img {
		position: relative;
		img {
			width: 50%;
			height: auto;
		}
	}
}
.b-annot-blog {
	&__title {
		margin-bottom: 12px;
	}
	&__content {
		padding-bottom: 10px;
		font-size: 14px;
		line-height: 1.3;
	}
	&__annot {
		margin-bottom: 10px;
	}
	&__list {
		margin-bottom: 10px;
		font-size: 13px;
	}
	&__item {
		margin-bottom: 0;
	}
}
.b-accordion {
	&__item.is-open .js &__content {
		position: relative;
		top: auto;
		left: auto;
		opacity: 1;
	}
}

// Crossroads
.c-workflow {
	max-width: 80%;
	margin: 0 auto;
	page-break-inside: avoid;
	&__item {
		margin-bottom: 15px;
	}
	&__inner {
		flex-direction: row;
	}
	&__img-wrapper {
		&::before {
			top: 40px;
			right: -20px;
			width: 40px;
			height: 40px;
			border: 2px solid $colorBd;
			font-size: 14px;
		}
	}
	&__img {
		width: 100px;
		height: 130px;
	}
	&__content {
		max-width: 400px;
	}
	&__title {
		margin-bottom: 10px;
		font-size: 14px;
	}

	// STATEs
	&__item:nth-child(2n) {
		.c-workflow__inner {
			flex-direction: row-reverse;
		}
		.c-workflow__img-wrapper {
			&::before {
				left: -20px;
			}
		}
	}
}

.c-crossroad {
	page-break-inside: avoid;
	.grid {
		display: flex;
		justify-content: center;
		page-break-inside: avoid;
	}
	.grid__cell {
		flex: 0 0 220px;
	}
}
