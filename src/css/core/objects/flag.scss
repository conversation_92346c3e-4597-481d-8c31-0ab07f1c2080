.flag {
	$s: '.flag';
	display: inline-block;
	border-radius: $radius;
	background-color: $colorWhite;
	color: $colorText;
	font-size: 14px;
	line-height: math.div(17, 14);
	&__inner {
		display: flex;
		vertical-align: top;
		flex-wrap: nowrap;
		padding: 3px 6px;
	}
	&__icon {
		flex: 0 0 auto;
		margin-right: 6px;
		font-size: 0;
		.icon-svg {
			top: 2px;
		}
	}
	&__text {
		position: relative;
		top: 1px;
		flex: 1 1 auto;
	}

	// VARIANTs
	&--link {
		color: $colorText;
	}
	&--purple,
	&--green,
	&--semidark,
	&--dark {
		#{$s}__inner {
			color: $colorWhite;
		}
	}
	&--purple {
		background-color: $colorSecondary;
	}
	&--green {
		background-color: $colorGreen;
	}
	&--semidark {
		background-color: $colorAshGray;
	}
	&--dark {
		background-color: $colorDarkGray;
	}
	&--grey {
		background-color: $colorGray;
	}
	&--yellow {
		background-color: $colorYellow;
		font-weight: 500;
	}
	&--tarif,
	&--premium {
		vertical-align: middle;
		margin-left: 0;
		padding: 2px 3px;
		background-color: $colorYellow;
		font-weight: bold;
		font-size: 12px;
		text-transform: uppercase;
		text-decoration: none;
		transition: $t;
		.icon-svg.icon-svg {
			width: 14px;
			margin: 0;
		}
		.flag__text {
			margin-left: 3px;
		}
		.tooltip & {
			margin-left: 5px;
		}
		.b-cta & {
			// width: 56px;
			// height: 56px;
			margin: 0 0 20px;
			padding: 6px 10px 0;
			.icon-svg {
				width: 40px;
			}
			.flag__text {
				display: block;
				margin: 0;
				padding: 5px 0;
			}
		}
		.m-main__link & {
			margin-top: -2px;
		}
	}
	&--tarif {
		padding: 3px 5px 1px;
	}

	// STATEs
	.hoverevents a &--premium:hover {
		background: $colorSecondary;
		color: $colorWhite;
	}
}
