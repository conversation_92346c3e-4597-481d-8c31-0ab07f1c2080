.row-main,
.row-main-lg {
	@include clearfix();
	position: relative;
	width: 100%;
	max-width: $rowMainWidth;
	margin: 0 auto;
	padding: 0 20px;
	@media ($mdUp) {
		.mother:not(.mother--detail) & {
			padding: 0 $rowMainGutter;
		}
	}
	@media ($lgUp) {
		&--detail {
			padding-right: 40px;
		}
	}
}

.row-main-lg {
	max-width: $rowMainWidthLg;
}

.grid {
	@extend %reset-ol;
	@extend %grid;
	margin-bottom: -($gridGutter);
	margin-left: -($gridGutter);
	&__cell {
		@extend %reset-ol-li;
		@extend %grid__cell;
		position: relative;
		border: $gridGutter solid transparent;
		border-width: 0 0 $gridGutter $gridGutter;

		// hide the border in MS high contrast mode
		border-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3C/svg%3E");
		&--top {
			align-self: flex-start;
		}
		&--middle {
			align-self: center;
		}
		&--bottom {
			align-self: flex-end;
		}
		&--eq {
			display: flex;
			> * {
				flex: 1 1 auto;
			}
		}
	}

	// VARIANTs
	&--scroll {
		@extend %grid--scroll;
	}
	&--nowrap {
		flex-wrap: nowrap;
	}
	&--middle {
		align-items: center;
	}
	&--bottom {
		align-items: flex-end;
	}
	&--center {
		justify-content: center;
	}
	&--right {
		justify-content: flex-end;
	}
	&--space-between {
		justify-content: space-between;
	}

	&--y-0 {
		margin-bottom: 0;
	}
	&--y-0 > &__cell {
		border-bottom-width: 0;
	}
	&--y-xs {
		margin-bottom: -10px;
	}
	&--y-xs > &__cell {
		border-bottom-width: 10px;
	}
	&--x-xs {
		margin-left: -10px;
	}
	&--x-xs > &__cell {
		border-left-width: 10px;
	}
	&--x-lg {
		margin-left: -60px;
	}
	&--x-lg > &__cell {
		border-left-width: 60px;
	}
	&--y-lg {
		margin-bottom: -60px;
	}
	&--y-lg > &__cell {
		border-bottom-width: 60px;
	}

	@media ($mdDown) {
		margin-bottom: -20px;
		&__cell {
			border-bottom-width: 20px;
		}
		&--y-lg {
			margin-bottom: -40px;
		}
		&--y-lg > &__cell {
			border-bottom-width: 40px;
		}
	}
}
.size {
	@include generateGridSize();
	&--auto {
		max-width: 100%;
	}
	@media ($lgUp) {
		&--3-5-12 {
			@include suffix('lg') {
				width: 100 / 12 * 3.5%;
			}
		}
	}
}

// .push {
// 	@include generateGridPush();
// }
// .pull {
// 	@include generateGridPull();
// }
.order {
	@include generateGridOrder();
}
