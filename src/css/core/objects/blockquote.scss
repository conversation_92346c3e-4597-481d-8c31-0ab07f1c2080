.blockquote {
	$s: '.blockquote';
	position: relative;
	padding: 30px;
	border-radius: $radius;
	background: $colorLightGray;
	&__text {
		max-width: 630px;
	}
	&__title {
		display: block;
		margin: 0 40px 12px 0;
		font-family: $fontSecondary;
		font-weight: 500;
		font-size: 16px;
	}
	&__quotes {
		position: absolute;
		top: 29px;
		right: 30px;
	}
	a {
		color: $colorText;
	}

	// VARIANTs
	&--lg {
		padding: 40px;
		font-size: 20px;
		line-height: math.div(28, 20);
		#{$s}__title {
			margin: 0 0 10px;
			font-size: 20px;
		}
		#{$s}__quotes {
			top: 36px;
			.icon-svg {
				width: 45px;
				height: 30px;
			}
		}
	}
	&--bg {
		z-index: 0;
		&::after {
			content: '';
			position: absolute;
			top: -130px;
			left: 20px;
			z-index: -1;
			width: 300px;
			height: 260px;
			border: 40px solid $colorWhite;
			border-radius: 50%;
			border-color: transparent transparent $colorWhite transparent;
		}
	}
	&--color {
		background: $colorLightPurple;
		#{$s}__title,
		#{$s}__quotes {
			color: $colorSecondary;
		}
	}
	&--lp {
		margin-bottom: 20px;
	}

	// MEDIA QUERIEs
	@media ($mdDown) {
		padding: 15px 20px;
		&__quotes {
			top: 18px;
			right: 20px;
		}
		&--lg {
			padding: 20px;
			font-size: 17px;
			#{$s}__title {
				max-width: 75%;
				font-size: 18px;
			}
			#{$s}__quotes {
				top: 24px;
				.icon-svg {
					width: 40px;
					height: 25px;
				}
			}
		}
		&--bg {
			&::after {
				top: -50px;
				width: 220px;
				height: 160px;
			}
		}
	}

	@media ($lgUp) {
		&--lp#{&}--lp {
			margin-top: 40px;
		}
	}
}
