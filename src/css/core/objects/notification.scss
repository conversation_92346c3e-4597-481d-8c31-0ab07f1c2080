.notification {
	$s: '.notification';
	position: relative;
	padding: 20px 55px 20px 70px;
	border-radius: $radius;
	background-color: $colorWhite;
	font-size: 16px;
	overflow: hidden;
	box-shadow: 0 0 25px rgba(0, 0, 0, 0.15);

	& + & {
		margin-top: 15px;
	}
	&__icon {
		position: absolute;
		top: 15px;
		left: 15px;
		.icon-svg {
			vertical-align: top;
		}
	}
	&__close {
		position: absolute;
		top: 15px;
		right: 15px;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 24px;
		height: 24px;
		border-radius: $radius;
		transition: background-color $t;
		cursor: pointer;
	}
	&__timer {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 0;
		width: 100%;
		height: 5px;
		background-color: $colorLightGray;
	}
	&__timer-inner {
		display: block;
		width: 0;
		height: 100%;
		background: #9d83f1;
		transition: width 5s linear;
	}

	// VARIANTs
	&--offer {
		padding: 15px 45px 20px 15px;
		#{$s}__inner {
			display: flex;
		}
		#{$s}__content {
			padding-left: 20px;
		}
		#{$s}__img {
			position: relative;
			flex: 0 0 auto;
			#{$s}__flags {
				position: absolute;
				top: 0;
				right: 0;
				left: 0;
				display: flex;
				justify-content: center;
				height: 24px;
				border-radius: $radius $radius 0 0;
				background: $colorSecondary;
				color: $colorWhite;
				font-size: 14px;
				text-align: center;
			}
			img {
				width: 100px;
				height: 105px;
				border-radius: $radius;
				object-fit: cover;
			}
		}
		#{$s}__title {
			margin: 0 0 8px;
			font-weight: 500;
		}
		#{$s}__link {
			color: $colorText;
		}
		#{$s}__address {
			margin: 0 0 8px;
			color: $colorDarkGray;
		}
		#{$s}__details {
			display: flex;
			flex-wrap: wrap;
			margin: 0 0 0 -25px;
		}
		#{$s}__detail {
			margin: 0 0 0 25px;
		}
	}

	// HOVERs
	.hoverevents &__close:hover {
		background-color: $colorLightGray;
	}

	// MEDIA QUERIEs
	@media ($smUp) {
		&--offer {
			padding: 15px 55px 20px 15px;
		}
	}
}
