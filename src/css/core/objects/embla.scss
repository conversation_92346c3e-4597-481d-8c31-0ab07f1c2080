.embla {
	position: relative;
	overflow: hidden;
	&__container {
		display: flex;
	}
	&__slide {
		flex: 0 0 100%;
	}
	&__prev,
	&__next {
		position: absolute;
		top: 50%;
		width: 50px;
		height: 50px;
		margin-top: -25px;
		border-radius: 25px;
		background: rgba($colorWhite, 0.5);
		color: $colorBlack;
		text-indent: -5000px;
		overflow: hidden;
		transition: background-color $t;
		.icon-svg {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
	}
	&__prev {
		left: 20px;
		.icon-svg {
			margin-left: -1px;
		}
	}
	&__next {
		right: 20px;
		.icon-svg {
			margin-right: -1px;
		}
	}

	&__prev__disabled,
	&__next__disabled {
		opacity: 0.3;
	}

	// STATEs
	.hoverevents &__prev:hover,
	.hoverevents &__next:hover {
		background: $colorWhite;
	}

	.hoverevents &__prev__disabled:hover,
	.hoverevents &__next__disabled:hover {
		background: rgba($colorWhite, 0.5);
	}
}
