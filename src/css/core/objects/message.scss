.message {
	$s: '.message';
	position: relative;
	padding: 13px 60px 12px 45px;
	border-radius: $radius;
	background: $colorLightPurple;
	text-align: left;
	> :last-child {
		margin-bottom: 0;
	}
	&__icon {
		position: absolute;
		top: 15px;
		left: 15px;
		color: $colorSecondary;
		.icon-svg {
			vertical-align: top;
			width: 20px;
		}
	}
	&__close {
		position: absolute;
		top: 13px;
		right: 16px;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 24px;
		height: 24px;
		border-radius: $radius;
		transition: background-color $t;
		cursor: pointer;
	}

	// VARIANTs
	&--noclose {
		padding-right: 20px;
	}
	&--icon-middle &__icon {
		top: 50%;
		line-height: 1;
		transform: translateY(-50%);
	}
	&--error,
	&--ok,
	&--warning {
		color: $colorText;
		li {
			&::before {
				background-color: $colorWhite;
			}
		}
		a {
			color: $colorSecondary;
		}
	}
	&--error {
		background: $colorLightRed;
		#{$s}__icon {
			color: $colorRed;
		}
	}
	&--ok {
		background: $colorLightGreen;
		#{$s}__icon {
			color: $colorGreen;
		}
	}
	&--warning {
		background: $colorLightYellow;
		#{$s}__icon {
			color: $colorOrange;
		}
	}

	&--float {
		max-width: 810px;
	}

	// HOVERs
	.hoverevents &__close:hover {
		background-color: rgba($colorWhite, 0.5);
	}
}
