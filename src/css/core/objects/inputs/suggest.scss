.suggest {
	position: relative;

	&__wrapper {
		position: absolute;
		top: 100%;
		right: 0;
		left: 0;
		z-index: 10;
		width: 100%;
		border: 1px solid $colorSecondary;
		border-radius: 0 0 $radius $radius;
		background: $colorWhite;
		outline: 0;
		visibility: hidden;
		opacity: 0;
		transition: all $t;
		box-shadow: 0 0 0 5px $colorInpPurple;

		&::before {
			content: '';
			position: absolute;
			top: -7px;
			right: -1px;
			left: -1px;
			height: 6px;
			border-right: 1px solid $colorSecondary;
			border-left: 1px solid $colorSecondary;
			background-color: $colorWhite;
		}
	}

	&__label {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	&__tool {
		margin-top: 5px;
		font-size: 14px;
		text-align: right;
		a {
			color: $colorText;
			text-decoration: none;
		}
		.item-icon {
			align-items: flex-start;
		}
		.icon-svg {
			width: 14px;
			margin-top: 2px;
			margin-right: 5px;
		}
	}

	&__label &__tool {
		margin-top: 0;
		margin-bottom: 5px;
	}

	&__group-container {
		display: flex;
		height: 100%;
	}

	&__field-container {
		position: relative;
		display: flex;
		flex: 1 1 100%;
		align-items: center;
		max-width: 100%;
		padding: 1px;
	}

	&__field-wrapper {
		display: flex;
		flex: 1 1 100%;
	}

	&__field {
		position: relative;
		z-index: 2;
		display: block;
		width: 100%;
		padding: 10px 10px 8px 0;
		border: 0;
		background: transparent;
		outline: none;
		font-size: 16px;
		box-shadow: none;
	}

	&__visual-field {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border: 1px solid $colorBd;
		border-radius: $radius;
		background-color: $colorLightGray;
		overflow: hidden;
		transition: border-color $t, background-color $t;
	}

	&__visual-field .btn {
		position: absolute;
		top: 50%;
		right: 0;
		z-index: 20;
		transform: translateY(-50%);
	}

	&__visual-field .btn__text {
		background: $colorLightGray;
		color: currentcolor;
	}

	&__prepend {
		position: relative;
		z-index: 1;
		flex: 0 1 25px;
		order: -1;
		padding: 0 8px 0 10px;

		.icon-svg {
			width: 24px;
		}
	}

	&__append {
		position: relative;
		z-index: 1;
		flex: 0 0 100px;
		background-image: url($svgSelect2);
		background-position: top 50% right 14px;
		background-repeat: no-repeat;
		background-size: 10px 5px;
		line-height: 20px;
	}

	&__note {
		margin-bottom: -2px;
		font-size: 16px;

		a {
			color: $colorText;
			text-decoration: none;
		}
	}

	&__list {
		@extend %reset-ul;
		position: relative;
		max-height: 67px * 5;
		border-bottom: 1px solid $colorBd;
		overflow-y: auto;
	}

	&__item {
		@extend %reset-ul-li;
		border-bottom: 1px solid $colorBd;
	}

	&__item:last-child {
		border-bottom: 0;
	}

	&__link,
	.no-touchevents &__link:hover {
		display: flex;
		align-items: center;
		padding: 9px 20px 7px 10px;
		color: $colorText;
		text-decoration: none;
		transition: color $t, background $t;
		cursor: pointer;

		.icon-svg {
			top: -2px;
			width: 24px;
			min-width: 24px;
			margin-right: 5px;
		}
	}

	&__btn {
		position: relative;
		margin: 0;
		padding: 10px 15px;
		text-align: right;
	}

	// STATEs
	&__wrapper.is-visible {
		visibility: visible;
		opacity: 1;
	}

	.has-error &__field {
		border-color: initial;
		background: initial;
	}

	.has-error &__visual-field {
		border-color: $colorInpError;
		background: $colorLightRed;
	}

	&__field:focus {
		box-shadow: none;
	}

	&__field:focus + &__visual-field {
		border-color: $colorSecondary;
		background-color: $colorWhite;
		box-shadow: 0 0 0 5px $colorInpPurple;
	}

	&__item.is-selected &__link {
		background: $colorBg;
		color: $colorText;
	}

	&__item.is-highlighted &__link {
		color: $colorHover;
	}

	&__btn.is-selected .btn__text {
		background: $colorHover;
	}

	.hoverevents &__visual-field .btn__text:hover {
		background: $colorGray;
		color: currentcolor;
	}

	.has-error &__field:focus {
		border-color: initial;
		background: initial;
		outline: none;
		box-shadow: none;
	}

	.has-error &__field:focus + &__visual-field {
		border-color: $colorRed;
		background: $colorWhite;
		outline: 2px solid transparent;
		box-shadow: 0 0 0 5px $colorLightRed;
	}
}
