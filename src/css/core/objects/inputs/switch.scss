.switch {
	display: inline-block;
	margin-bottom: 10px;
	&__btn {
		position: relative;
		width: 40px;
		height: 12px;
		border-radius: $radius;
		background-color: $colorGray;
		outline: none;
		transition: background-color $t;
		appearance: none;
		&::before {
			content: '';
			position: absolute;
			top: -50%;
			left: 0;
			width: 24px;
			height: 24px;
			border-radius: 15px;
			background-color: #b3b3b3;
			transition: all $t;
			cursor: pointer;
			box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
		}
	}
	&__label {
		padding-left: 10px;
	}

	// STATEs
	&__btn:checked {
		background-color: #aa8fff;
		&::before {
			background-color: $colorSecondary;
			transform: translateX(17px);
		}
	}

	// HOVERs
	.hoverevents &__btn:hover::before {
		box-shadow: 1px 1px 4px rgba(0, 0, 0, 0), 0 0 0 4px rgba(#b3b3b3, 0.35);
	}
	.hoverevents &__btn:checked:hover::before {
		box-shadow: 1px 1px 4px rgba(0, 0, 0, 0), 0 0 0 4px rgba(#aa8fff, 0.35);
	}
	&__btn:not(:checked) + label::after {
		content: attr(data-unchecked);
	}
	&__btn:checked + label::after {
		content: attr(data-checked);
	}
}
