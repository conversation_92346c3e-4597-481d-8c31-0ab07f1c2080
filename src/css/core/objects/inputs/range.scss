.range {
	display: flex;
	justify-content: space-between;
	align-items: center;
	&__input {
		position: relative;
		flex-grow: 1;
		max-width: 50%;
		&:nth-of-type(1) {
			margin-right: 50px;
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				right: -40px;
				width: 30px;
				height: 1px;
				padding: 0;
				background-color: $colorAshGray;
			}
		}
	}
	.inp-text {
		color: $colorDarkGray;
	}
	p {
		margin: 0;
	}

	// MEDIA QUERIEs
	@media ($smDown) {
		&__input {
			&:nth-of-type(1) {
				margin-right: 30px;
				&::after {
					right: -25px;
					width: 20px;
				}
			}
		}
	}
}
