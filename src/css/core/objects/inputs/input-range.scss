@import '~react-input-range/lib/css/index.css';

.input-range {
	$s: '.input-range';
	width: calc(100% - 1rem);
	margin: 10px 0.5rem 50px;
	&__slider {
		border-color: $colorSecondary;
		background-color: $colorSecondary;
		transition: none;
	}
	&__track {
		transition: none;
		&--active {
			background-color: $colorSecondary;
		}

		#{$s}__label-container {
			display: none;
		}
	}
	&__label {
		color: $colorText;
		font-family: $fontPrimary;
	}
	&__label--min {
		left: -0.5rem;
		#{$s}__label-container {
			left: 0;
		}
	}
	&__label--max {
		right: -0.5rem;
		#{$s}__label-container {
			left: 0;
		}
	}
	&__slider-container {
		transition: none;
	}
}
