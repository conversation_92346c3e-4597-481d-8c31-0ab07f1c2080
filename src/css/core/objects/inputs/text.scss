.inp-text {
	@extend %inp;

	&::placeholder {
		color: rgba($colorDarkGray, 0.5);
		opacity: 1;
	}

	// VARIANTs
	&--dark {
		&::placeholder {
			color: rgba($colorText, 0.5);
		}
	}

	// STATEs
	&:focus {
		&::placeholder {
			color: rgba($colorDarkGray, 0.25);
		}
	}

	@at-root textarea#{&} {
		height: auto;
	}
}

.suggest__field-wrapper {
	input:-webkit-autofill,
	input:-webkit-autofill:hover,
	input:-webkit-autofill:focus,
	textarea:-webkit-autofill,
	textarea:-webkit-autofill:hover,
	textarea:-webkit-autofill:focus,
	select:-webkit-autofill,
	select:-webkit-autofill:hover,
	select:-webkit-autofill:focus {
		border: 1px solid transparent;
		background: transparent;
		transition: background-color 5000s ease-in-out 0s;
		-webkit-text-fill-color: $colorText;
		-webkit-box-shadow: none;
	}
}
