.inp-items {
	&__list {
		margin: 0 0 -10px -15px;
	}
	&__item {
		border: solid transparent;
		border-width: 0 0 10px 15px;
	}
	& .inp-row {
		margin: 0;
	}
	&__btn {
		padding: 8px 15px;
		border-radius: 4px;
		background: $colorBg;
		color: $colorText;
		font-size: 16px;
		transition: background-color $t;
		.icon-svg {
			width: 24px;
			height: 24px;
		}
	}

	* + & {
		margin-top: 5px;
	}

	// VARIANTs
	&--filter &__btn {
		padding-right: 20px;
		padding-left: 20px;
	}

	// STATEs
	.hoverevents &__btn:hover,
	&__btn.is-selected {
		background: $colorInpPurple;
	}

	@media ($smDown) {
		&__list {
			margin: 0 0 -10px -10px;
		}
		&__item {
			border-width: 0 0 10px 10px;
		}
		&__btn {
			padding-right: 10px;
			padding-left: 10px;
			font-size: 15px;
			.item-icon__icon {
				margin-right: 5px;
			}
		}
	}
}
