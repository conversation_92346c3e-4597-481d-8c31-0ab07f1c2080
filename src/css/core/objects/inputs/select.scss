.select {
	position: relative;

	&__inp {
		position: relative;
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		padding: 10px 44px 8px 10px;
		border: 1px solid $colorBd;
		border-radius: $radius;
		background-color: $colorLightGray;
		background-image: url($svgSelect2);
		background-position: top 50% right 15px;
		background-repeat: no-repeat;
		background-size: 10px 5px;
		outline: 0;
		font-size: 16px;
		text-align: left;
		transition: border-color $t;
		appearance: none;

		&::-ms-expand {
			display: none;
		}

		.icon-svg {
			width: 20px;
			height: 20px;
		}

		&::after {
			content: '';
			position: absolute;
			right: -1px;
			bottom: 0;
			left: -1px;
			z-index: 10;
			height: 6px;
			border-right: 1px solid $colorSecondary;
			border-left: 1px solid $colorSecondary;
			background-color: $colorWhite;
			opacity: 0;
			transition: border-color $t, opacity $t;
		}
	}

	&__options {
		position: absolute;
		top: 100%;
		right: 0;
		left: 0;
		display: block;
		max-height: 300px;
		margin-top: -1px;
		border: 1px solid $colorSecondary;
		border-radius: 0 0 $radius $radius;
		background-color: $colorWhite;
		overflow: hidden;
		overflow-y: scroll;
		visibility: hidden;
		opacity: 0;
		transition: all $t;
		box-shadow: 0 0 0 5px $colorInpPurple;
	}

	&__option-wrapper {
		border-bottom: 1px solid $colorBd;

		&:last-of-type {
			border: none;
		}
	}

	&__option {
		display: block;
		width: 100%;
		padding: 8px 44px 6px 10px;
		border: none;
		background-color: $colorWhite;
		font-size: 16px;
		text-align: left;
		transition: background-color $t;
		appearance: none;

		&::-ms-expand {
			display: none;
		}

		&.is-selected {
			background-color: $colorLightGreen;
		}

		.icon-svg {
			top: -1px;
			width: 20px;
			height: 20px;
		}
	}

	// HOVERs
	&__inp:hover {
		border-color: $colorSecondary;
		cursor: pointer;
	}

	&__option:hover {
		background: $colorLightGray;
		cursor: pointer;
	}

	// STATEs
	&.is-open {
		z-index: 5;
		border-radius: $radius;
		outline: 5px solid transparent;
		box-shadow: 0 0 0 5px $colorInpPurple;
	}

	&.is-open &__options {
		visibility: visible;
		opacity: 1;
	}

	&.is-open &__inp {
		border: 1px solid $colorSecondary;
		background-color: $colorWhite;

		&::after {
			opacity: 1;
		}
	}

	.has-error &__inp,
	.has-error &__inp::after {
		border-color: $colorInpError;
		background-color: $colorLightRed;
	}

	.has-error &.is-open &__inp,
	.has-error &.is-open &__options {
		border-color: $colorRed;
		background-color: $colorWhite;
		outline: 2px solid transparent;
		box-shadow: 0 0 0 5px $colorLightRed;
	}

	.has-error &.is-open &__inp::after {
		border-color: $colorRed;
	}
}
