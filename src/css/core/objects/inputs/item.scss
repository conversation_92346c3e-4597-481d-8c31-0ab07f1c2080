// stylelint-disable no-descending-specificity
.inp-item {
	position: relative;
	display: inline-block;
	vertical-align: top;
	min-height: 20px;
	padding-left: 26px;
	font-size: 16px;
	line-height: 20px;
	cursor: pointer;
	&__inp {
		position: absolute;
		left: 0;
		opacity: 0;
	}
	&__text {
		display: block;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 18px;
			height: 18px;
			border: 2px solid $colorDarkGray;
			border-radius: 3px;
			background: $colorWhite;
			transition: border-color $t, background-color $t, box-shadow $t;
		}
		&::after {
			content: '';
			position: absolute;
			top: 6px;
			left: 6px;
			width: 8px;
			height: 8px;
			background: $colorSecondary;
			opacity: 0;
			transition: opacity $t;
		}
	}
	&__title {
		display: block;
	}

	// VARIANTS
	&--radio &__text {
		&::before {
			border-radius: 10px;
		}
		&::after {
			top: 5px;
			left: 5px;
			width: 8px;
			height: 8px;
			border-radius: 5px;
			background: $colorSecondary;
		}
	}
	&--checkbox &__text {
		&::after {
			top: 3px;
			left: 7px;
			width: 5px;
			height: 10px;
			border: 2px solid $colorSecondary;
			border-width: 0 2px 2px 0;
			background: none;
			transform: rotate(45deg);
		}
	}

	// HOVERs
	.hoverevents &__inp:hover + &__text {
		&::before {
			outline: 2px solid transparent;
			box-shadow: 0 0 0 3px #eeeeef;
		}
	}
	.hoverevents &__inp:checked:hover + &__text {
		&::before {
			outline: 2px solid transparent;
			box-shadow: 0 0 0 3px $colorInpPurple;
		}
	}

	.hoverevents &__inp:disabled:hover + &__text {
		&::before {
			outline: 0;
			box-shadow: none;
		}
	}

	// STATES
	&__inp:not(.is-reversed):checked + &__text,
	&__inp.is-reversed:not(:checked) + &__text {
		&::before {
			border-color: $colorSecondary;
			background: $colorWhite;
		}
		&::after {
			opacity: 1;
		}
	}
	&__inp:disabled + &__text {
		&::before {
			border-color: $colorAshGray;
			background: $colorGray;
		}
	}
	&--checkbox &__inp:disabled:checked + &__text {
		&::before {
			border-color: $colorAshGray;
		}
		&::after {
			border-width: 0 2px 2px 0;
			border-color: $colorAshGray;
			opacity: 1;
		}
	}

	&--radio &__inp:disabled:checked + &__text {
		&::before {
			border-color: $colorAshGray;
		}
		&::after {
			background: $colorAshGray;
			opacity: 1;
		}
	}
}

// stylelint-enable
