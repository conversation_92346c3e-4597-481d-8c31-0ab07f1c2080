label {
	.hoverevents &:hover {
		cursor: pointer;
	}
}

.inp-label {
	position: relative;
	display: inline-block;
	vertical-align: top;
	margin-bottom: 0.25em;
	font-weight: 500;
	font-size: 16px;
	line-height: math.div(19, 16);

	// VARIANTs
	&--required {
		position: relative;
		padding-right: 15px;
		&::after {
			content: url($svgAsterisc);
			position: absolute;
			top: -4px;
			right: 0;
			color: $colorPrimary;
		}
	}
}
