%inp {
	display: block;
	width: 100%;
	padding: 10px 15px 8px;
	border: 1px solid $colorBd;
	border-radius: $radius;
	background: $colorWhite;
	color: $colorText;
	font-size: 16px;
	line-height: 20px;
	transition: border-color $t, box-shadow $t, background-color $t;
	appearance: none;

	// STATEs
	&:disabled {
		background-color: $colorBg;
	}
	&:focus {
		border-color: $colorSecondary;
		background-color: $colorWhite;
		outline: 2px solid transparent;
		box-shadow: 0 0 0 5px $colorInpPurple;
	}
	.has-error & {
		border-color: $colorInpError;
		background: $colorLightRed;
		&:focus {
			border-color: $colorRed;
			background: $colorWhite;
			outline: 2px solid transparent;
			box-shadow: 0 0 0 5px $colorLightRed;
		}
	}
}
