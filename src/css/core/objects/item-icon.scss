.item-icon {
	$s: '.item-icon';
	display: inline-flex;
	vertical-align: top;
	align-items: center;
	&__icon {
		flex: 0 0 auto;
		margin-right: 10px;
		font-size: 0;
	}
	&__text {
		flex: 1 1 auto;
	}

	// VARIANTs
	&--after {
		flex-direction: row-reverse;
		#{$s}__icon {
			margin: 0 0 0 5px;
		}
	}
	&--low #{$s}__icon {
		color: $colorSecondary;
	}
	&--medium #{$s}__icon {
		color: $colorOrange;
	}
	&--high #{$s}__icon {
		color: $colorRed;
	}
	&--suggest #{$s}__icon {
		margin-right: 5px;
		.icon-svg {
			top: -1px;
		}
	}
}
