.btn {
	$s: '.btn';
	display: inline-block;
	vertical-align: middle;
	margin: 0;
	padding: 0;
	border: 0;
	background: none;
	font-size: inherit;
	text-decoration: none;
	transition: color $t;
	cursor: pointer;
	&.item-icon {
		display: inline-flex;
	}
	&.a:not(.item-icon) {
		display: inline;
		vertical-align: baseline;
	}
	&__text {
		position: relative;
		display: block;
		padding: 12px 20px 11px;
		border-radius: $radius;
		background-color: $colorLink;
		color: $colorWhite;
		font-family: $fontSecondary;
		font-size: 14px;
		line-height: 17px;
		text-align: center;
		text-decoration: none;
		transition: background-color $t, border-color $t, color $t;
	}

	// VARIANTs
	&--full {
		display: block;
		width: 100%;
	}
	&--disabled {
		pointer-events: none;
		#{$s}__text {
			background-color: $colorGray;
			color: $colorAshGray;
		}
	}

	&--secondary &__text {
		background: $colorSecondary;
	}
	&--outline &__text {
		border: 2px solid;
		background: transparent;
		color: $colorLink;
	}
	&--facebook &__text {
		background: $colorFacebook;
	}
	&--twitter &__text {
		background: $colorTwitter;
	}
	&--linkedin &__text {
		background: $colorLinkedIn;
	}
	&--grey &__text {
		background: $colorBd;
		color: $colorText;
	}

	&--miw-100 {
		min-width: 100px;
	}

	&--sm &__text {
		padding: 7px 20px 6px;
	}
	&--xs &__text {
		padding: 5px 15px;
		font-size: 12px;
		line-height: 14px;
	}
	&--md &__text {
		padding: 11px 30px 10px;
		font-size: 16px;
		line-height: 22px;
	}
	&--lg &__text {
		padding: 17px 30px 16px;
		font-size: 18px;
		line-height: 24px;
	}

	&--icon &__text {
		padding: 8px;
	}
	&--cookie &__text {
		padding: 7px 20px 6px;
		border: 1px solid $colorWhite;
		background-color: transparent;
		color: $colorWhite;
	}

	// HOVERs
	.hoverevents &:hover &__text {
		background-color: $colorHover;
		color: $colorWhite;
	}
	.hoverevents &--secondary:hover &__text {
		background-color: $colorPrimary;
	}
	.hoverevents &--cookie:hover &__text {
		background-color: $colorWhite;
		color: $colorText;
	}
	.hoverevents &--outline:hover &__text {
		border-color: $colorHover;
	}

	// STATEs
	&:disabled,
	&.is-disabled {
		pointer-events: none;
		#{$s}__text {
			background-color: $colorGray;
			color: $colorAshGray;
		}
	}
}
