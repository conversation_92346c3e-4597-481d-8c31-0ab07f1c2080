.title {
	margin: 0;
	strong {
		color: $colorSecondary;
		font-weight: normal;
	}
	&__caption {
		font-size: 80%;
	}

	// VARIANTs
	&--semicircle {
		position: relative;
		margin: 0 auto;
		padding-bottom: 40px;
		text-align: center;

		&::before {
			content: '';
			position: absolute;
			bottom: 0;
			left: 50%;
			width: 100px;
			height: 100px;
			border: 15px solid $colorGreen;
			border-radius: 50%;
			border-color: transparent transparent $colorGreen transparent;
			transform: translateX(-50%);
		}
	}

	// MEDIA QUERIEs
	@media ($mdUp) {
		&--semicircle {
			padding-bottom: 55px;
			&::before {
				width: 165px;
				height: 180px;
				border: 20px solid $colorGreen;
				border-color: transparent transparent $colorGreen transparent;
			}
		}
	}

	@media ($smDown) {
		font-size: 26px;
		&:not(.b-intro__title) {
			font-size: 22px;
		}
	}
}
