@use 'sass:math';

$pointerSize: 8px;
$pointerGap: 3px;
$animationSize: 10px;

.tooltip {
	display: inline-flex;
	vertical-align: top;
	align-items: center;

	&__icon {
		flex: 0 0 auto;
		margin-left: 5px;
		color: $colorDarkGray;
		font-size: 0;

		.icon-svg {
			top: -1px;
		}
	}

	&__link {
		color: inherit;
	}

	&__text {
		flex: 1 1 auto;
	}

	&__content {
		position: absolute;
		top: 0;
		z-index: 100000;
		display: block;
		max-width: 320px;
		padding: 5px 10px;
		border-radius: 4px;
		background-color: $colorDarkGray;
		color: $colorWhite;
		font-size: 12px;
		text-align: center;
		opacity: 0;
		transform: translate3d(-50%, -$animationSize - $pointerGap, 0);
		animation: text-animation-top $t $t forwards;

		&::after {
			content: '';
			position: absolute;
			top: 100%;
			left: 0;
			display: block;
			width: 100%;
			height: 10px;
		}
	}

	&__pointer {
		position: absolute;
		bottom: math.div(-$pointerSize, 2);
		left: 50%;
		display: block;
		width: $pointerSize;
		height: $pointerSize;
		background-color: $colorDarkGray;
		transform: translateX(-50%) rotate(45deg);
	}

	// VARIANTS
	&--input {
		flex: 0 0 auto;
		margin: 0 0 10px;
		font-weight: 500;
		font-size: 14px;
		line-height: math.div(18, 14);
	}

	&__content--right {
		transform: translate3d($pointerGap + $animationSize, -50%, 0);
		animation: text-animation-right $t $t forwards;

		&::after {
			top: 0;
			left: -10px;
			width: 10px;
			height: 100%;
		}
	}

	&__content--right &__pointer {
		top: 50%;
		left: math.div(-$pointerSize, 2);
		transform: translateY(-50%) rotate(45deg);
	}
}

@keyframes text-animation-top {
	0% {
		opacity: 0;
		transform: translate3d(-50%, -$animationSize - $pointerGap, 0);
	}
	100% {
		opacity: 1;
		transform: translate3d(-50%, -$pointerGap, 0);
	}
}

@keyframes text-animation-right {
	0% {
		opacity: 0;
		transform: translate3d($pointerGap + $animationSize, -50%, 0);
	}
	100% {
		opacity: 1;
		transform: translate3d($pointerGap, -50%, 0);
	}
}
