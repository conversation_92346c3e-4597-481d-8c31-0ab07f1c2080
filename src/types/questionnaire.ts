import { AddNotification } from './common';
import { LandType, RealEstateType } from './filter';
import { ConditionType, HouseType, OfferType, TerraceType } from './offer';

export type Step = 1 | 2 | 3;

export type QuestionProps = {
	onInterestExpression: () => void;
	onInterestRefusal: () => void;
	questionStep: Step;
	skipMoreDetails?: boolean;
	skipQuestion: () => void;
} & Partial<QuestionnaireFilter>;

export type UserDetailsHandling = {
	hasConfirmedUserDetails: boolean;
	confirmUserDetails: () => void;
};

export type SpecificQuestionProps = AddNotification &
	UserDetailsHandling & {
		moveToNextStep: () => void;
		userDetails: QuestionnaireUserData;
		questionnaire: Questionnaire;
	};

export type QuestionnaireCreationInputs = {
	email: string;
};

export type QuestionnairePatch = {
	interest?: QuestionnaireInterest;
	estimateData?: QuestionnaireEstimateData;
	userData?: QuestionnaireUserInputs;
	userHasInteracted?: boolean;
};

export type QuestionType = {
	heading: string;
	subHeading?: string;
};

export type QuestionnaireUserInputs = {
	email: string;
	firstName: string;
	lastName: string;
	telephone: string;
};

export type QuestionnaireUserData = {
	userData: QuestionnaireUserInputs;
};

export type QuestionnaireEstimateData = {
	address: string;
	area: number;
	condition: ConditionType | null;
	disposition: string | null;
	houseType: HouseType | null;
	landArea: number | null;
	landType: LandType | null;
	realEstateType: RealEstateType;
	terraceType: TerraceType | null;
};

export type QuestionnaireEstimateFormInputs = {
	estimateData: QuestionnaireEstimateData;
} & (QuestionnaireUserData | undefined);

export enum QuestionnaireInterest {
	ESTIMATE = 'ESTIMATE',
	INSURANCE = 'INSURANCE',
	MORTGAGE = 'MORTGAGE',
	RENTING_OWN_HOUSE = 'RENTING_OWN_HOUSE',
}

export type Questionnaire = QuestionnaireUserInputs & {
	id: string;
	interests: QuestionnaireInterest[];
	userHasInteracted: boolean;
};

export type QuestionnaireFilterType = OfferType.RENT | OfferType.SALE;

export type QuestionnaireFilter = {
	filterType: QuestionnaireFilterType;
};

export type QuestionnaireResponse = {
	questionnaire: Questionnaire;
};
