import { ContactType } from 'types/admin';
import { OfferType, OwnershipType, TerraceType } from 'types/offer';
import { DateRange } from 'types/time';
import { UserState } from 'types/user';
import { Geometry, PolygonCoords } from './map';
import { Polygon } from '@turf/turf';

export const PRICE_HIGH_SALE = 100_000_000;
export const PRICE_HIGH_RENT = 50_000;
export const PRICE_HIGH_ROOMMATES = 10_000;
export const AREA_HIGH_DEFAULT = 1_000;
export const LAND_AREA_HIGH_DEFAULT = 10_000;

export enum RealEstateType {
	APARTMENT = 'Apartment',
	HOUSE = 'House',
	LAND = 'Land',
	COMMERCIAL = 'Commercial',
	OTHER = 'Other',
}

export enum LandType {
	BUILD_PLOT = 'BuildPlot',
	COMMERCIAL = 'Commercial',
	OTHER = 'Other',
}

export enum HouseType {
	DETACHED = 'Detached',
	TERRACED = 'Terraced',
	COTTAGE = 'Cottage',
	HUT = 'Hut',
}

export enum FilterState {
	ACTIVE = 'Active',
	DELETED = 'Deleted', // Not used at the moment
	DISABLED = 'Disabled',
	PENDING = 'Pending',
}

export enum FilterRelation {
	NONE = 'None',
	EXACT_MATCH = 'ExactMatch',
	WITHIN = 'Within',
	CONTAINS = 'Contains',
}

export type FilterRange = { gte: number; lte: number | null };

export type Location = {
	id: string;
	level: number;
	name: string;
	fullName: string;
	part: string | null;
	city: string | null;
	district: string | null;
	region: string | null;
	cohesion: string | null;
	country: string | null;
};

export type EstimateRequest = Omit<FilterRequest, 'email' | 'state'>;

export type FilterRequest = {
	adType: OfferType;
	agent: ContactType[];
	area: FilterRange;
	createWebhook: string | null;
	deleteWebhook: string | null;
	disposition: string[];
	elevator: boolean;
	email: string;
	recipientEmail: string;
	exactMatch: boolean;
	excludedOrigins: string[];
	houseType: HouseType[];
	landArea: FilterRange;
	landType: LandType[];
	locationId?: string | null;
	ownershipType: OwnershipType[];
	customLocationCoords?: Polygon | null;
	price: FilterRange;
	radius: number;
	realEstateType: RealEstateType;
	state: FilterState;
	terraceType: TerraceType[];
	updateWebhook: string | null;
};

export type EstimateData = {
	estimate: Estimate;
};

export type Estimate = {
	range: DateRange;
	value: number;
};

export type Filter = {
	adType: OfferType;
	agent: ContactType[];
	area: FilterRange;
	createWebhook: string | null;
	createdAt: number;
	deleteWebhook: string | null;
	disposition: string[];
	elevator: boolean;
	email: string;
	recipientEmail: string;
	estimate: Estimate;
	exactMatch: boolean;
	excludedOrigins?: string[];
	houseType: HouseType[];
	id: string;
	landArea: FilterRange;
	landType: LandType[];
	location: Location;
	locationId?: string;
	customLocationCoords?: PolygonCoords | null;
	notificationsSent: number;
	ownershipType?: OwnershipType[];
	price: FilterRange;
	radius: number;
	realEstateType: RealEstateType;
	state: FilterState;
	terraceType?: TerraceType[];
	updateWebhook: string | null;
	updatedAt: number;
	withTerraceOnly: boolean;
};

export type FilterResponse = {
	filter: Filter;
	relation?: FilterRelation;
	originalFilterState?: FilterState;
	user?: {
		id?: string;
		state?: UserState;
		activeFilters?: number;
		totalFilters?: number;
		isSubscribed?: boolean;
		newSubscriber?: boolean;
	};
};

export type FiltersResponse = {
	filters: Filter[];
};

export type FilterDeleteResponse = {
	user: {
		activeFilters: number;
	};
};

export type ApiResponse<T> = {
	statusCode: number;
	message: string;
	data: T;
};

export type PlainApiResponse = ApiResponse<undefined>;

export type AutocompleteData = {
	suggestions: Location[];
};

export type GeoData = {
	geo: Location;
};

type Lon = number;
type Lat = number;

export type GeoPoint = [Lon, Lat];
