import { BlogAuthor } from 'types/author';

export type BlogArticle = {
	metadata: BlogArticleMeta;
	content: string;
};

export type BlogArticleTag = {
	name: string;
	icon: string;
};

export type BlogArticleContentHighlight = {
	title: string;
	content: string;
};

export type BlogArticleSource = {
	title: string;
	href: string;
};

export type BlogArticles = BlogArticle[];

export type BlogArticleMeta = {
	path: string;
	heading: string;
	perex: string;
	contentHighlight: BlogArticleContentHighlight | null;
	keywords: string[];
	tags: string[];
	previewImage?: string;
	socialImage?: string;
	publish: number;
	author?: BlogAuthor | null;
	relatedArticles: BlogArticle[];
	sources: BlogArticleSource[];
};
