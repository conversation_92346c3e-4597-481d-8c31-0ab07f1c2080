export enum AuthState {
	LOADING = 'Loading',
	LOGGED_IN = 'Logged in',
	LOGGED_OUT = 'Logged out',
}

export enum UserRole {
	NONE = 0,
	USER = 1,
	MINI = 3,
	STANDARD = 5,
	PREMIUM = 6,
	PROFI = 7,
	ENTERPRISE = 9,
	ADVANCED = 10,
	ADMIN = 10000,
}

export const userRoleMap: Record<UserRole, string> = {
	[UserRole.NONE]: 'Žádný',
	[UserRole.USER]: 'Uživatel',
	[UserRole.MINI]: 'Mini',
	[UserRole.STANDARD]: 'Standard',
	[UserRole.PREMIUM]: 'Premium',
	[UserRole.PROFI]: 'Profi',
	[UserRole.ENTERPRISE]: 'Enterprise',
	[UserRole.ADVANCED]: 'Pokročilý',
	[UserRole.ADMIN]: 'Administrátor',
};

export enum UserState {
	ACTIVE = 'Active',
	BLOCKED = 'Blocked',
	CREATED = 'Created',
	DELETED = 'Deleted',
	NEW = 'New',
}

export type ApiKeyResponse = {
	apiKey: string;
};

export enum TelephoneSource {
	CTA_BOX = 'CTA_BOX',
	LANDING_PAGE = 'LANDING_PAGE',
	REGISTRATION = 'REGISTRATION',
}
