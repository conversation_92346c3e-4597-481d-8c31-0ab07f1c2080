import { ContactPayload, ContactType } from 'types/admin';
import { LandType, RealEstateType } from './filter';
import { Location } from 'types/filter';
import { MessageDescriptor } from 'react-intl';
import { commonInltMessages } from '../i18n/commonIntlMessages/common';

const { municipalOwnershipIntlString, directOwnershipIntlString, sharedOwnershipIntlString, cooperativeOwnershipIntlString } =
	commonInltMessages;

export enum OfferType {
	SALE = 'Sale',
	RENT = 'Rent',
	AUCTION = 'Auction',
	OTHER = 'Other',
	ROOMMATES = 'Roommates',
}

export enum RoommatesDisposition {
	ONE_BED = '1bed',
	TWO_BED = '2bed',
	THREE_BED = '3bed',
	FOUR_BED = '4bed',
	INDEPENDENT_ROOM = 'independentRoom',
}

export type OfferResponse = {
	realEstates: Offer[];
};

export type RealEstateResponse = {
	realEstate: Offer;
};

type Lon = number;
type Lat = number;

export type GeoPoint = [Lon, Lat] | null;

export type Offer = {
	adType: OfferType;
	area: number;
	createdAt: number;
	disposition: string;
	gps: GeoPoint;
	id: string;
	imageUrl: string;
	images: string[];
	landArea: number;
	landType: LandType;
	location?: Location;
	origin: string;
	price: number;
	priceNote: string;
	priceText: string;
	realEstateType: RealEstateType;
	stats?: StatsPayload[];
	url: string;
	deletedAt?: number | null;
	contact?: ContactPayload;
	extended?: ExtendedPayload;
};

export type ExtendedPayload = {
	basement: boolean | null;
	buildingType: BuildingType | null;
	conditionType: ConditionType | null;
	description: string | null;
	elevator: boolean | null;
	floor: string | number | null;
	houseType: HouseType | null;
	landWidth: number | null;
	ownershipType: OwnershipType | null;
	parkingType: ParkingType | null;
	terraceType: TerraceType | null;
	title: string | null;
	updatedAt: string;
};

export enum BuildingType {
	BRICK = 'Brick',
	OTHER = 'Other',
	PANEL = 'Panel',
	STONE = 'Stone',
	WOOD = 'Wood',
}
export enum ConditionType {
	BAD = 'Bad',
	GOOD = 'Good',
	IN_PROGRESS = 'In Progress',
	NEW = 'New',
}
export enum HouseType {
	DETACHED = 'Detached',
	TERRACED = 'Terraced',
	COTTAGE = 'Cottage',
	HUT = 'Hut',
}

export enum OwnershipType {
	COOPERATIVE = 'Cooperative',
	DIRECT = 'Direct',
	MUNICIPAL = 'Municipal',
	SHARED = 'Shared',
}

export enum ParkingType {
	GARAGE = 'Garage',
	PLACE = 'Place',
}
export enum TerraceType {
	BALCONY = 'Balcony',
	LOGGIA = 'Loggia',
	TERRACE = 'Terrace',
}

type OwnerShipTypeUnion = `${OwnershipType}`;
export const ownershipTypeMessageMap: Record<OwnerShipTypeUnion, MessageDescriptor> = {
	Direct: directOwnershipIntlString,
	Cooperative: cooperativeOwnershipIntlString,
	Municipal: municipalOwnershipIntlString,
	Shared: sharedOwnershipIntlString,
};

export type StatsPayload<T = StatsVariants> = {
	realEstateId: string;
	source: StatsSource;
	stats: T;
};

export enum StatsSource {
	CityPerformer = 'CityPerformer',
	PriceHubble = 'PriceHubble',
	Valuo = 'Valuo',
	VisidooPrice = 'VisidooPrice',
}

export type CityPerformerStats = {
	indices: {
		overall: number | null;
		mobility: number | null;
		wellbeing: number | null;
		relax: number | null;
		safety: number | null;
		services: number | null;
		environment: number | null;
	};
	shareUrl: string;
	rentPrice: number | null;
	salePrice: number | null;
	pricePercentile: number | null;
	daysOnMarket: number | null;
};

export type ValuoStats = {
	rent: {
		average: number | null;
		median: null; // Not supported;
		range: [number, number] | null;
		sample: 0; // Not supported
		distance: number;
	};
	sale: {
		average: number | null;
		median: null; // Not supported;
		range: [number, number] | null;
		sample: 0; // Not supported;
		distance: number;
	};
};

export type PriceHubbleStats = {
	id: string;
};

export type VisidooPriceStats = {
	rent: {
		average: number | null;
		median: number | null;
		range: [number, number] | null;
		sample: number;
		distance: number;
	};
	sale: {
		average: number | null;
		median: number | null;
		range: [number, number] | null;
		sample: number;
		distance: number;
	};
};

export type StatsVariants = CityPerformerStats | ValuoStats | PriceHubbleStats | VisidooPriceStats | null;

export type NonPublicOfferSchema = {
	realEstate: {
		adType: OfferType;
		agent?: ContactType | null;
		area: number;
		basement: boolean | null;
		buildingType: BuildingType | null;
		conditionType: ConditionType | null;
		description: string | null;
		disposition: string | null;
		elevator: boolean | null;
		floor: string | number | null;
		gps: GeoPoint;
		houseType: HouseType | null;
		imageUrl: string[] | null;
		landArea: number | null;
		landType: LandType | null;
		landWidth: number | null;
		location: string | null;
		origin: string | null;
		ownershipType: OwnershipType | null;
		parkingType: ParkingType | null;
		price: number | null;
		priceNote: string | null;
		priceText: string | null;
		realEstateType?: RealEstateType;
		terraceType: TerraceType | null;
		title: string | null;
		url: string | null;
	};

	contact: {
		email: string;
		telephone: string | null;
		name: string | null;
		agencyName: string | null;
		contactType: ContactType;
	};
};

export type PossibleOfferTypeForDetailPage = OfferType.SALE | OfferType.RENT;
