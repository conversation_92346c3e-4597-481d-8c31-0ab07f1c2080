import { ReactElement, ReactNode } from 'react';
import { ApiResponse, RealEstateType } from 'types/filter';
import { Offer, OfferType } from 'types/offer';
import { TelephoneSource, UserRole, UserState } from 'types/user';
import { ConsentOption } from 'types/userConsents';

export interface EmailPayload {
	bouncedAt: number | null;
	complainedAt: number | null;
	createdAt: number;
	deliveredAt: number | null;
	id: string;
	interactedAt: number | null;
	interactions: string[];
	openedAt: number | null;
	recipientId: string;
	sentAt: number | null;
	template: string;
}

export interface EmailPreviewPayload {
	from: string;
	html: string;
	subject: string;
	text: string;
	to: string;
}

export interface EmailsResponse {
	emails: EmailPayload[];
}

export interface EmailResponse {
	email: EmailPreviewPayload;
}

export interface NewsletterResponse {
	email: EmailPreviewPayload;
	users: number;
}

export interface DuplicatesResponse {
	realEstates: Offer[][];
}

export interface EmailStatsPayload {
	sent: number;
	delivered: number;
	rejected: number;
	opened: number;
	interacted: number;
}

export type UserUpdateRequest = {
	adminNote?: string;
	apiAccess: string[];
	name?: string;
	surname?: string;
	email: string;
	role: UserRole;
	roleExpiresAt: number | null;
	state: UserState;
	locale: string;
	timezone: string;
	telephone: string;
};

export interface UserBasePayload {
	apiAccess: string[];
	email: string;
	emailPreferences: ConsentOption[];
	filters: number;
	id: string;
	locale: string;
	name?: string;
	role: UserRole;
	roleExpiresAt: string | null;
	state: UserState;
	surname?: string;
	telephone?: string | null;
	timezone: string;
}

export interface UserAdminPayload {
	adminInfo: {
		adminNote?: string | null;
		telephoneSource: TelephoneSource;
		createdAt: number;
	};
}

export interface UserStripeInfo {
	stripeInfo: {
		stripeSubscriptionId: string | null;
		stripeCustomerId: string | null;
	};
}

export type UserPayload = UserBasePayload & Partial<UserAdminPayload> & Partial<UserStripeInfo>;

export interface UsersResponse {
	users: UserPayload[];
	count: {
		active: number;
		blocked: number;
		inactive: number;
		total: number;
	};
}

export interface UserResponse {
	user: UserPayload;
	emails: EmailStatsPayload;
}

export enum FetchType {
	STATIC = 'STATIC',
	DYNAMIC = 'DYNAMIC',
}

export enum ScraperState {
	ENABLED = 'Enabled',
	DISABLED = 'Disabled',
}

export type ScraperRequest = {
	adType: OfferType;
	fetchType: FetchType;
	pageMultiplier: number;
	realEstateType: RealEstateType;
	schedule: { gte: string; lte: string }[];
	startFromZero: boolean;
	state: ScraperState;
	url: string;
};

export interface ScraperResponse {
	scraper: ScraperPayload;
}

export interface ScrapersResponse {
	scrapers: ScraperPayload[];
	origins: {
		origin: string;
		friendlyName: string;
	}[];
}

export interface ScraperDataResponse {
	scrapers: ScraperData[];
	count: number;
}

export type ScraperLocale = 'cs' | 'sk';

export type ScraperData = {
	name: string;
	url: string;
	locale: ScraperLocale;
};

export interface ScraperPayload {
	adType: OfferType;
	checkUpdates: boolean;
	createdAt: number;
	fetchType: FetchType;
	friendlyName: string | null;
	id: string;
	origin: string | null;
	pageMultiplier: number;
	realEstates: number;
	realEstateType: RealEstateType;
	schedule: { gte: string; lte: string }[];
	startFromZero: boolean;
	state: ScraperState;
	url: string;
}

export interface ScraperHydrateRequest {
	from: number;
	to: number;
}

export type EmailTestCopyRequest = {
	email: string;
};

export type EmailTestNewRequest = {
	email: string;
	subject: string;
	html: string;
};

export type NewsletterPostRequest = {
	campaign: string;
};

export type NotificationPostRequest = {
	userId: string;
	realEstateId: string;
};

export enum ContactType {
	AGENT = 'Agent',
	DEVELOPER = 'Developer',
	IN_REVIEW = 'InReview',
	MUNICIPALITY = 'Municipality',
	PRIVATE = 'Private',
	UNKNOWN = 'Unknown',
}

export type ContactPayload = {
	agencyName: string | null;
	contactType: ContactType;
	createdAt: number;
	email: string;
	id: string;
	name: string | null;
	telephone: string | null;
};

export type ContactsResponse = {
	contacts: ContactPayload[];
};

export type ContactResponse = {
	contact: ContactPayload;
};

export type ContactPatchRequest = {
	contactType: ContactType;
};

export type AdminNoteRequest = {
	note: string;
};

export type ManualUploadRequest = {
	stringifiedSchema: string;
};

export type JsonSchemaIssue = {
	code: string;
	expected: string;
	message: string;
	path: string[];
	received: string;
};

export type NonPublicRealEstateValidateResponse = ApiResponse<{
	issues?: JsonSchemaIssue[];
	isValid: boolean;
	users: string[] | null;
}>;

export type Service = {
	name: string;
	query: Record<string, any>;
	validFrom: string;
	validTo: string;
};

export type ServiceListResponse = ApiResponse<{ services: Service[] }>;

export const serviceProviders = ['Fingo', 'Direct', 'DirectSale', 'AGP', 'KontrolaSmluv', 'KontrolaPozemku', 'Ownest'] as const;

export type ServiceProvider = typeof serviceProviders[number];

export type ServiceComponentProps = {
	img?: string;
	icon?: string;
	flag?: string;
	flagColor?: 'purple' | 'green' | 'semidark' | 'dark' | 'yellow';
	logo?: string;
	logoAlt?: string;
	logoWidth?: number;
	logoHeight?: number;
	title: string;
	url: string;
	desc?: string | ReactNode;
};

export type ServiceComponent = ReactElement<ServiceComponentProps>;
