import { GeoPoint } from './filter';

export type PolygonCoords = GeoPoint[][];

export type Geometry = {
	coordinates: PolygonCoords;
	type: 'Polygon';
};

export type Feature = {
	geometry: Geometry;
	id: string;
};

export type DrawData = {
	features: Feature[];
};

export const areValidPolygonCoords = (data: unknown): data is PolygonCoords => {
	return (
		Array.isArray(data) &&
		data.every((item) => Array.isArray(item) && item.every((point) => Array.isArray(point) && point.length === 2))
	);
};
