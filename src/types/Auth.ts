import { ConsentOption } from 'types/userConsents';

export type RegisterInputs = {
	email: string;
	locale: string;
	timezone: string;
};

export type RegisterCtaInputs = RegisterInputs & {
	telephone: string;
};

export type ResetInputs = {
	email: string;
};

export type LoginInputs = {
	email: string;
	password: string;
};

export type EditUserInputs = {
	name: string;
	surname: string;
	emailPreferences: ConsentOption[];
	locale: string;
	timezone: string;
	telephone: string;
	role: number;
};

export type CompleteRegistrationInputs = {
	magic: string;
	name: string;
	surname: string;
	password: string;
	telephone: string;
};

export type CompleteResetInputs = {
	magic: string;
	email: string;
	password: string;
};

export type LoginResponse = {
	statusCode: number;
	message: string;
	data?: {
		token: string;
	};
};

export type RegisterResponse = {
	statusCode: number;
	message: string;
};

export type ResetResponse = RegisterResponse;
