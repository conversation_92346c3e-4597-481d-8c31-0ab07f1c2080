import { ContactList } from 'components/admin/ContactList';
import { EmailsList } from 'components/admin/EmailsList';
import { ManualRealEstateUploadForm } from 'components/admin/manualOffersUpload/ManualRealEstateUploadForm';
import { NewsletterAdministration } from 'components/admin/NewsletterAdministration';
import { RealEstateDuplicatesList } from 'components/admin/RealEstateDuplicatesList';
import { ScraperList } from 'components/admin/ScraperList';
import { ServicesUploadForm } from 'components/admin/servicesUpload/ServicesUploadForm';
import { UsersList } from 'components/admin/UsersList';
import { PageSeo } from 'components/meta/PageSeo';
import { Row } from 'components/ui/core/Row';
import { Tabs } from 'components/ui/core/Tabs';
import { useAdminPage } from 'hooks/useAdminPage';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const AdministrationIndex: NextPage = () => {
	useAdminPage();

	return (
		<>
			<PageSeo title="Administrace" />
			<Row>
				<Tabs>
					<div title="Uživatelé">
						<UsersList />
					</div>
					<div title="Scrapery">
						<ScraperList />
					</div>
					<div title="E-maily">
						<EmailsList />
					</div>
					<div title="Newsletter">
						<NewsletterAdministration />
					</div>
					<div title="Duplicity realit">
						<RealEstateDuplicatesList />
					</div>
					<div title="Kontakty">
						<ContactList />
					</div>
					<div title="Manual offers upload">
						<ManualRealEstateUploadForm />
					</div>
					<div title="Services upload">
						<ServicesUploadForm />
					</div>
				</Tabs>
			</Row>
		</>
	);
};

AdministrationIndex.displayName = 'AdministrationIndex';

export default AdministrationIndex;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/administrace',
	[Locale.en]: '/administration',
});
