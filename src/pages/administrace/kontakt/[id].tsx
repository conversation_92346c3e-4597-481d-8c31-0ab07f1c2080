import { ContactDetail } from 'components/admin/ContactDetail';
import { PageSeo } from 'components/meta/PageSeo';
import { Row } from 'components/ui/core/Row';
import { useAdminPage } from 'hooks/useAdminPage';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const AdministrationContact: NextPage = () => {
	const id = useRouter().query.id;

	useAdminPage();

	return (
		<>
			<PageSeo title="Detail kontaktu | Administrace" />
			<Row>{id && <ContactDetail id={Array.isArray(id) ? id[0] : id} />}</Row>
		</>
	);
};

AdministrationContact.displayName = 'AdministrationContact';

export default AdministrationContact;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/administrace/kontakt/[id]',
	[Locale.en]: '/administration/contact/[id]',
});
