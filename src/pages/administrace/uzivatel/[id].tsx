import { UserDetail } from 'components/admin/UserDetail';
import { PageSeo } from 'components/meta/PageSeo';
import { Row } from 'components/ui/core/Row';
import { useAdminPage } from 'hooks/useAdminPage';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const AdministrationUser: NextPage = () => {
	const id = useRouter().query.id;

	useAdminPage();

	return (
		<>
			<PageSeo title="Detail uživatele | Administrace" />
			<Row>{id && <UserDetail id={Array.isArray(id) ? id[0] : id} />}</Row>
		</>
	);
};

AdministrationUser.displayName = 'AdministrationUser';

export default AdministrationUser;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/administrace/uzivatel/[id]',
	[Locale.en]: '/administration/user/[id]',
});
