import { fetchTelephonePatch } from 'api/telephone/fetchTelephonePatch';
import clsx from 'clsx';
import { PageSeo } from 'components/meta/PageSeo';
import { Button } from 'components/ui/core/Button';
import { Form } from 'components/ui/core/Form';
import { Row } from 'components/ui/core/Row';
import { TelephoneInput } from 'components/ui/core/TelephoneInput';
import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FormattedMessage, useIntl } from 'react-intl';
import { AddTelephoneInputs } from 'types/telephone';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { TEN_SECONDS } from 'utils/constants';
import { normalizeQueryParam } from 'utils/normalizeQueryParam';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Cta } from 'components/ui/boxes/Cta';
import { Label } from 'components/ui/core/Label';

const LpTelephone: NextPage = () => {
	const { formatMessage } = useIntl();
	const { addNotification } = useNotifications();
	const { likeServiceIntlString, shareWithFamilyIntlString, commonTelephoneIntlString } = commonInltMessages;
	const router = useRouter();
	const context = useForm<AddTelephoneInputs>({ defaultValues: { telephone: '' } });
	const {
		formState: { isSubmitting },
		watch,
	} = context;
	const telephone = watch('telephone');
	const { emailId, userId } = router.query ?? {};

	// STATUS MESSAGES
	const statusMessagesMap = {
		400: formatMessage({ description: 'telephoneFetch-400', defaultMessage: 'Formulář se nepodařilo odeslat' }),
		401: formatMessage({ description: 'telephoneFetch-401', defaultMessage: 'Nelze spárovat telefon s vaším e-mailem' }),
		403: formatMessage({ description: 'telephoneFetch-403', defaultMessage: 'U profilu již bylo zadáno telefonní číslo' }),
		200: formatMessage({ description: 'telephoneFetch-200', defaultMessage: 'Děkujeme, ozveme se vám co nejdříve to půjde' }),
	};
	const fallbackMessage = formatMessage({ description: 'telephoneFetch-genericError', defaultMessage: 'Nepodařilo se odeslat formulář' });
	const invalidUrlMessage = formatMessage({
		description: 'telephoneFetch-invalidCredentials',
		defaultMessage: 'Nepodařilo se odeslat formulář',
	});

	const onSubmit: SubmitHandler<AddTelephoneInputs> = async (data) => {
		if (!emailId || !userId || !data.telephone.length) {
			addNotification({ message: invalidUrlMessage, timeout: TEN_SECONDS });
			return;
		}
		const normalizedEmailId = normalizeQueryParam(emailId);
		const normalizedUserId = normalizeQueryParam(userId);

		const response = await fetchTelephonePatch(data, normalizedEmailId, normalizedUserId);
		const { statusCode } = response;

		showToastNotification(statusCode);
	};

	const showToastNotification = (statusCode: number) => {
		const message = statusCode in statusMessagesMap ? statusMessagesMap[statusCode as keyof typeof statusMessagesMap] : fallbackMessage;

		addNotification({ message, timeout: TEN_SECONDS });
	};

	return (
		<>
			<PageSeo
				title={formatMessage({
					description: 'telephoneBox-title',
					defaultMessage: 'Potřebujete poradit{br} s hledáním bydlení?',
				})}
			/>
			<Row>
				<ArticleIntro
					title={formatMessage(
						{
							description: 'telephoneBox-title',
							defaultMessage: 'Potřebujete poradit{br} s hledáním bydlení?',
						},
						{ br: <br /> },
					)}
					bg
					margin="sm"
				>
					<p className="caption">
						<FormattedMessage
							description={'telephoneBox-desc'}
							defaultMessage={
								'Nechte nám váš kontakt, ozveme se vám{br} a poradíme s címkoliv co se týká hledání a koupě bydlení.'
							}
							values={{
								br: <br />,
							}}
						/>
					</p>
				</ArticleIntro>
				<Form<AddTelephoneInputs> onSubmit={onSubmit} context={context}>
					<div className="u-mb-lg">
						<div className="grid grid--y-0 grid--x-xs">
							<div className="grid__cell size--6-12@md">
								<div className={clsx('inp-row')}>
									<Label id="telephone" required>
										<FormattedMessage {...commonTelephoneIntlString} />
									</Label>
									<span className="inp-fix">
										<TelephoneInput />
									</span>
								</div>
							</div>
							<div className="grid__cell size--6-12@md">
								<p>
									<Label id="telephone">&nbsp;</Label>
									<br />
									<Button
										type="submit"
										disabled={isSubmitting || telephone.length < 1}
										className={clsx('btn-loader', isSubmitting && 'is-loading')}
										iconAfter="arrow-right"
										text={formatMessage({
											description: 'telephoneBox-register',
											defaultMessage: 'Ozvěte se mi',
										})}
									/>
								</p>
							</div>
						</div>
					</div>
				</Form>
			</Row>

			<Cta title={formatMessage(likeServiceIntlString)} text={formatMessage(shareWithFamilyIntlString)} share />
		</>
	);
};
LpTelephone.displayName = 'LpTelephone';

export default LpTelephone;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/lp-telephone',
	[Locale.en]: '/lp-telephone',
});
