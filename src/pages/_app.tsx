import '@reach/dialog/styles.css';
import { <PERSON>ie } from 'components/meta/Cookie';
import { GTM, GTMIframe } from 'components/meta/GTM';
import { UserAgentScript } from 'components/meta/UserAgentScript';
import { EmailFilterEdit } from 'components/search/EmailFilterEdit';
import { FilterDataProvider } from 'components/search/FilterDataProvider';
import { CookiePopup } from 'components/ui/layout/CookiePopup';
import { Layout } from 'components/ui/layout/Layout';
import { NotificationsProvider } from 'components/ui/notification/NotificationsProvider';
import { AuthProvider } from 'components/user/AuthProvider';
import { UserProvider } from 'components/user/UserProvider';
import 'css/style.scss';
import { usePageview } from 'hooks/usePageview';
import { getLocaleMessages } from 'i18n/supportedLocales';
import { useShortLocale } from 'i18n/useShortLocale';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { ReactElement, useEffect, useState } from 'react';
import { IntlProvider } from 'react-intl';
import { AppPropsWithLayout } from 'types/next';
import { SITE_NAME } from 'utils/constants';
import { setFormatLocale } from 'utils/formats';
import 'mapbox-gl/dist/mapbox-gl.css';
import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css';

const App = ({ Component, pageProps }: AppPropsWithLayout) => {
	const shortLocale = useShortLocale();
	const { defaultLocale } = useRouter();
	const [messages, setMessages] = useState({});
	const getLayout =
		Component.getLayout ??
		((page: ReactElement) => (
			<Layout>
				{page}
				<CookiePopup />
			</Layout>
		));

	useEffect(() => {
		setFormatLocale(shortLocale);
		(async () => {
			const messages = await getLocaleMessages(shortLocale);
			setMessages(messages);
		})();
	}, [shortLocale]);

	usePageview();

	return (
		<IntlProvider
			locale={shortLocale}
			defaultLocale={defaultLocale}
			defaultRichTextElements={{ br: () => <br />, strong: (...chunks) => <strong>{chunks}</strong> }}
			messages={messages}
			onError={() => null}
		>
			<Head>
				<title key="title">{SITE_NAME}</title>
				<meta key="charset" charSet="utf-8" />
				<meta key="viewport" name="viewport" content="width=device-width, initial-scale=1" />
				<link rel="apple-touch-icon" sizes="180x180" href="/img/favicons/apple-touch-icon.png" />
				<link rel="icon" type="image/png" sizes="32x32" href="/img/favicons/favicon-32x32.png" />
				<link rel="icon" type="image/png" sizes="16x16" href="/img/favicons/favicon-16x16.png" />
				<link rel="manifest" href="/img/favicons/site.webmanifest" />
				<link rel="mask-icon" href="/img/favicons/safari-pinned-tab.svg" color="#5bbad5" />
				<meta name="msapplication-TileColor" content="#da532c" />
				<meta name="theme-color" content="#ffffff" />
				<Cookie />
				<UserAgentScript />
				<GTM />
			</Head>

			<NotificationsProvider>
				<UserProvider>
					<AuthProvider>
						<FilterDataProvider>
							<GTMIframe />
							{getLayout(<Component {...pageProps} />)}
						</FilterDataProvider>
						<EmailFilterEdit />
					</AuthProvider>
				</UserProvider>
			</NotificationsProvider>
		</IntlProvider>
	);
};

App.displayName = 'App';

export default App;
