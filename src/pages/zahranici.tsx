import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { FormattedMessage, useIntl } from 'react-intl';
import { PageSeo } from 'components/meta/PageSeo';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import Image from 'next/image';
import { Icon } from 'components/ui/core/Icon';

const Abroad: NextPage = () => {
	const intl = useIntl();
	const AbroadIntlString = intl.formatMessage({ description: 'Abroad-title', defaultMessage: 'Zahraniční nemovitosti' });

	return (
		<>
			<PageSeo title={AbroadIntlString} />
			<Row>
				<ArticleIntro title={AbroadIntlString} bg margin="sm">
					<p className="caption">
						<FormattedMessage
							description={'Abroad-mainText'}
							defaultMessage={
								'Zajímají vás inzeráty nemovitostí ze zahraničí? Tak právě pro Vás jsme připravili toto rozšíření.'
							}
						/>
					</p>
					<p className="u-mt-xs">
						<a href="#" className="btn">
							<span className="btn__text">To musím vyzkoušet</span>
						</a>
					</p>
				</ArticleIntro>

				<section className="b-highlights b-highlights--lg u-mb-md">
					<h2 className="u-mb-md">Vyhody balíčku Zahraničí</h2>
					<div className="b-highlights__list u-mb-last-0">
						<div className="b-highlights__item">
							<div className="b-highlights__inner u-mb-last-0">
								<span className="b-highlights__icon">
									<Icon name="highlight-1" />
								</span>
								<h3 className="b-highlights__title">
									<FormattedMessage defaultMessage={'Hlídáme zahraniční realitní servery'} description={'abroad-h3'} />
								</h3>
								<span className="b-highlights__text">
									<FormattedMessage
										description={'abroad-dontWaste'}
										defaultMessage={
											'Netravte hodiny hledáním nabídek na nepřehledných zahraničních serverech. My to uděláme za vás.'
										}
									/>
								</span>
							</div>
						</div>
						<div className="b-highlights__item">
							<div className="b-highlights__inner u-mb-last-0">
								<span className="b-highlights__icon">
									<Icon name="highlight-2" />
								</span>
								<h3 className="b-highlights__title">
									<FormattedMessage
										description={'abroad-speed'}
										defaultMessage={'Okamžite upozorníme na nejnovější nabídku'}
									/>
								</h3>
								<span className="b-highlights__text">
									<FormattedMessage
										description={'abroad-newOffers'}
										defaultMessage={'Buďte rychlejší než ostatní. Nová nabídka za minutu ve vaší e-mailové schránce.'}
									/>
								</span>
							</div>
						</div>
						<div className="b-highlights__item">
							<div className="b-highlights__inner u-mb-last-0">
								<span className="b-highlights__icon">
									<Icon name="highlight-4" />
								</span>
								<h3 className="b-highlights__title">
									<FormattedMessage description={'abroad-help'} defaultMessage={'Pomůžeme vám s nákupem či pronájmem'} />
								</h3>
								<span className="b-highlights__text">
									<FormattedMessage
										description={'abroad-helpDesc'}
										defaultMessage={
											'Díky partnerské síti v zahraničí vám pomůžeme i koupit či pronajmout nemovitost. Vše optimalizovano pro vaše maximální pohodlí.'
										}
									/>
								</span>
							</div>
						</div>
					</div>
				</section>

				<h2 className="h4 u-mb-xs">V jakých zemích lze vyhledávat?</h2>
				<div className="b-price b-price--row u-mb-md">
					<ul className="b-price__list u-mb-0">
						<li>
							<Image src="/img/flags/croatia.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Chorvatsko
						</li>
						<li>
							<Image src="/img/flags/slovakia.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Slovensko
						</li>
						<li>
							<Image src="/img/flags/germany.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Německo
						</li>
						<li>
							<Image src="/img/flags/austria.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Rakousko
						</li>
						<li>
							<Image src="/img/flags/italy.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Itálie
						</li>
						<li>
							<Image src="/img/flags/spain.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Španělsko
						</li>
						<li>
							<Image src="/img/flags/bulgaria.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Bulharsko
						</li>
						<li>
							<Image src="/img/flags/hungary.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Maďarsko
						</li>
						<li>
							<Image src="/img/flags/poland.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Polsko
						</li>
						<li>
							<Image src="/img/flags/france.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Francie
						</li>
						<li>
							<Image src="/img/flags/great-britain.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Velká Británie
						</li>
						<li>
							<Image src="/img/flags/netherlands.png" alt="" width={16} height={16} loading="lazy" layout="fixed" />
							Nizozemsko
						</li>
					</ul>
				</div>

				<h2 className="h3 u-text-center u-mb-md">
					Rozšíření pro zahraniční nemovitosti <span className="u-color-secondary">lze kombinovat</span>
					<br /> s tarify Premium i Standard.
				</h2>

				<div className="b-cta b-cta--auto u-bg-gray u-mb-lg">
					<div className="b-cta__content u-mb-last-0">
						<p className="h4 u-mt-0 u-mb-xs">
							<strong>990 Kč</strong> za měsíc
						</p>
						<p>
							<a href="#" className="btn btn--lg">
								<span className="btn__text">Mám zájem o zahraniční nemovitosti</span>
							</a>
						</p>
						<p className="u-mt-sm">Garance vrácení peněz do 14 dní od platby. Tarif můžete kdykoli změnit, žádné závazky.</p>
					</div>
				</div>
			</Row>
		</>
	);
};
Abroad.displayName = 'Abroad';

export default Abroad;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/zahranici',
	[Locale.en]: '/abroad',
});
