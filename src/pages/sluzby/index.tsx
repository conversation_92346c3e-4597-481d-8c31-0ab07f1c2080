import { useIntl } from 'react-intl';
import { PageSeo } from 'components/meta/PageSeo';
import { Row } from 'components/ui/core/Row';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { Services } from 'components/services/Services';
import { serviceProviders } from 'types/admin';

const ServicesPage: NextPage = () => {
	const intl = useIntl();
	const howItWorksIntlString = intl.formatMessage({ description: 'howItWorks-title', defaultMessage: 'Služby pro bydlení' });

	return (
		<>
			<PageSeo title={howItWorksIntlString} />
			<Row lg>
				<div className="b-intro u-mb-md">
					<h1 className="title u-mb-xs">
						<strong>Služby</strong> šité na míru
					</h1>
					<p className="caption">
						Naše partnery pe<PERSON>liv<PERSON> v<PERSON>í<PERSON>, abychom Vám mohli nabídnout nejlepší možnou pomoc s&nbsp;bydlením.
					</p>
				</div>

				{/* <nav className="b-tabs__menu u-mb-sm">
					<div className="b-tabs__container">
						<ul className="b-tabs__list">
							<li className="b-tabs__item">
								<a className="b-tabs__link is-active">Vše</a>
							</li>
							<li className="b-tabs__item">
								<a className="b-tabs__link">Kupuji nemovitost</a>
							</li>
							<li className="b-tabs__item">
								<a className="b-tabs__link">Hledám pronájem</a>
							</li>
							<li className="b-tabs__item">
								<a className="b-tabs__link">Prodávám nemovitost</a>
							</li>
							<li className="b-tabs__item">
								<a className="b-tabs__link">Nabízím pronájem</a>
							</li>
						</ul>
					</div>
				</nav>

				<div className="inp-items inp-items--filter u-mb-sm">
					<ul className="inp-items__list grid grid--x-sm">
						<li className="inp-items__item grid__cell size--auto">
							<button type="button" className="inp-items__btn btn is-active">
								Vše <span className="inp-items__count">(21)</span>
							</button>
						</li>
						<li className="inp-items__item grid__cell size--auto">
							<button type="button" className="inp-items__btn btn">
								Financování <span className="inp-items__count">(1)</span>
							</button>
						</li>
						<li className="inp-items__item grid__cell size--auto">
							<button type="button" className="inp-items__btn btn">
								Pojištění <span className="inp-items__count">(1)</span>
							</button>
						</li>
						<li className="inp-items__item grid__cell size--auto">
							<button type="button" className="inp-items__btn btn">
								Právní služby <span className="inp-items__count">(2)</span>
							</button>
						</li>
						<li className="inp-items__item grid__cell size--auto">
							<button type="button" className="inp-items__btn btn">
								Nemovitost <span className="inp-items__count">(6)</span>
							</button>
						</li>
						<li className="inp-items__item grid__cell size--auto">
							<button type="button" className="inp-items__btn btn">
								Realitní služby<span className="inp-items__count">(8)</span>
							</button>
						</li>
						<li className="inp-items__item grid__cell size--auto">
							<button type="button" className="inp-items__btn btn">
								Energie<span className="inp-items__count">(15)</span>
							</button>
						</li>
					</ul>
				</div> */}

				<Services servicesToShow={serviceProviders} />
			</Row>
			<CtaCommon />
		</>
	);
};
ServicesPage.displayName = 'Services';

export default ServicesPage;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/sluzby',
	[Locale.en]: '/services',
});
