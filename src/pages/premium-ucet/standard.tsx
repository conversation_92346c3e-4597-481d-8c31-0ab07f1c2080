import { ActivatedFilterContents } from 'components/dekujeme/ActivatedFilterContents';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { AuthForm, useAuth } from 'components/user/AuthProvider';
import { useUser } from 'components/user/UserProvider';
import type { NextPage } from 'next';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { Filter, FilterResponse } from 'types/filter';
import { AuthState } from 'types/user';
import { trackEvent } from 'utils/analytics';
import { ONE_SECOND } from 'utils/constants';

const PremiumAccountStandard: NextPage = () => {
	// const intl = useIntl();
	const { loggedIn, userState } = useUser();
	const { openAuthForm } = useAuth();
	const openLogin = (event: MouseEvent) => {
		if (loggedIn) return;
		event.preventDefault();
		openAuthForm(AuthForm.LOGIN);
	};

	const [data, setData] = useState<Filter>();
	const intl = useIntl();
	const titleIntlString = intl.formatMessage({
		description: 'PremiumAccountMini-title',
		defaultMessage: 'Máte aktivní tarif <strong>Standard</strong>',
	});

	useEffect(() => {
		const { data } = JSON.parse(localStorage.getItem('filledFilter') ?? '{"data": false}');
		setData(data);

		const userInfo: NonNullable<FilterResponse['user']> = JSON.parse(localStorage.getItem('visidoo:userInfo') ?? '{}');
		if (Object.keys(userInfo).length > 0) {
			setTimeout(() => {
				trackEvent({
					category: 'filter_form_finished',
					action: userInfo?.newSubscriber ? 'first' : 'repeate',
					label: data?.id ?? '',
					value: 89 * 4 * 2 * 0.78, // 712 * 0.78
				});
				localStorage.removeItem('visidoo:userInfo');
			}, ONE_SECOND);
		}
	}, []);

	if (typeof data == null || userState === AuthState.LOADING) return null;

	return (
		<>
			<PageSeo title="Máte aktivní tarif Standard" />

			<Row lg>
				<ArticleIntro title={titleIntlString} bg margin="sm">
					<p>
						V e&#8209;mailu naleznete potvrzení platby.{' '}
						<Link href={'/moje-filtry'} onClick={openLogin} legacyBehavior={false}>
							Spravovat svoje předplatné a&nbsp;měnit filtry
						</Link>{' '}
						můžete {loggedIn ? '' : 'po přihlášení'} ve svém profilu.
					</p>
				</ArticleIntro>
			</Row>
			<ActivatedFilterContents data={data} lg={false} />
		</>
	);
};

export default PremiumAccountStandard;
