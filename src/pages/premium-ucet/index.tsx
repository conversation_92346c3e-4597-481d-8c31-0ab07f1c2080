import { fetchScraperData } from 'api/realEstate/fetchScraperData';
import { PremiumDialogsProvider } from 'components/dialog/PremiumDialogsProvider';
import { PageSeo } from 'components/meta/PageSeo';
import { PremiumTariffsCompare } from 'components/premium/PremiumTariffsCompare';
import { PremiumTariffsDetails } from 'components/premium/PremiumTariffsDetails';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticProps, NextPage } from 'next';
import { FormattedMessage, useIntl } from 'react-intl';
import { ScraperData } from 'types/admin';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { ONE_DAY } from 'utils/constants';

type Props = {
	scrapers: ScraperData[];
	count: number;
};

const PremiumAccount: NextPage<Props> = ({ scrapers, count }) => {
	const intl = useIntl();
	const PremiumAccountIntlString = intl.formatMessage({
		description: 'PremiumAccount-title',
		defaultMessage: 'Vyberte si <strong>optimální tarif</strong>',
	});

	return (
		<>
			<PageSeo title="Vyberte si optimální tarif" />

			<Row>
				<ArticleIntro title={PremiumAccountIntlString} bg margin="sm">
					<p className="caption">
						<FormattedMessage
							description={'PremiumAccount-mainText'}
							defaultMessage={'Špičkové funkce, díky kterým získáte nejlepší nemovitost odpovídající přesně vašim požadavkům'}
						/>
					</p>
				</ArticleIntro>
			</Row>

			<PremiumDialogsProvider scrapers={scrapers}>
				<PremiumTariffsCompare count={count} />
				<PremiumTariffsDetails count={count} />

				<div className="u-text-center u-mb-lg">
					<h2 className="h4">💳 Platba probíhá přes zabezpečenou platební bránu Stripe.</h2>
					<p>
						V případě, že preferujete platbu na základě faktury, kontaktujte nás na e-mailu{' '}
						<a href="mailto:<EMAIL>"><EMAIL></a>.
					</p>
				</div>
			</PremiumDialogsProvider>

			<CtaCommon />
		</>
	);
};
PremiumAccount.displayName = 'PremiumAccount';

export default PremiumAccount;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/premium-ucet',
	[Locale.en]: '/premium-account',
});

export const getStaticProps: GetStaticProps<Props> = async () => {
	const { data } = await fetchScraperData();

	return {
		revalidate: ONE_DAY / 1000,
		props: { scrapers: data.scrapers, count: data.count },
	};
};
