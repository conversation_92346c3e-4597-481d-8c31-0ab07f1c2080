import { PremiumSignupForm } from 'components/premium/PremiumSignupForm';
import { PageSeo } from 'components/meta/PageSeo';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { UserRole } from 'types/user';

const PremiumAccountSignup: NextPage = () => {
	const router = useRouter();
	const tariff: UserRole | null = router.query.tariff
		? Number(Array.isArray(router.query.tariff) ? router.query.tariff[0] : router.query.tariff)
		: null;

	useEffect(() => {
		if (tariff === null && router.isReady) {
			router.push('/premium-ucet');
		}
	}, [router, tariff]);

	if (!tariff) return null;

	return (
		<>
			<PageSeo title="Aktivace Premium účtu" />

			<PremiumSignupForm tariff={tariff} />
		</>
	);
};
PremiumAccountSignup.displayName = 'PremiumAccountSignup';

export default PremiumAccountSignup;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/premium-ucet-potvrzeni',
	[Locale.en]: '/premium-account-signup',
});
