import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const DocsRealEstate: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace - RealEstate" description="" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							<strong>RealEstate</strong>
						</>
					}
					margin="sm"
				/>
				<h2 className="h4">Žádost a přidělení API klíče</h2>
				<p>API klíč je vázaný na registrovaného uživatele Visidoo.</p>
				<p>
					Pokud jste registraci ještě neprovedli, tak ji naleznete pod odkazem <strong>Přihlášení</strong> v horním menu a
					následně odkazem <strong>Chci se registrovat</strong> v zobrazeném modalu. Dále postupujte podle instrukcí.
				</p>
				<p>
					Po registraci zašlete žádost o přidělení API klíče pro registrovaný e-mail na adresu{' '}
					<a href="mailto:<EMAIL>"><EMAIL></a>. API klíč vám bude co nejdříve vygenerován.
				</p>

				<h2 className="h4">Základní informace</h2>
				<ul>
					<li>
						<strong>HTTP metoda</strong>: <code>GET</code>
					</li>
					<li>
						<strong>Endpoint</strong>: <code>https://api.visidoo.com/v1/real-estate</code>
					</li>
					<li>
						<strong>MIME typ</strong>: <code>application/json</code>
					</li>
					<li>
						<strong>Autorizace</strong>:{' '}
						<code>
							Authorization: Bearer{' '}
							<var>
								[
								<Link href="/muj-profil" target="_blank">
									váš přidělený api klíč
								</Link>
								]
							</var>
						</code>
					</li>
					<li>
						<strong>Query parametry</strong>:
						<ul>
							<li>
								<strong>url</strong> - Url hledaného inzerátu (dejte pozor, aby byla správně zakódovaná pro přenos v url,
								např. v js pomocí funkce encodeURIComponent)
							</li>
						</ul>
					</li>
				</ul>

				<h2 className="h4">Příklad odpovědi</h2>
				<samp>
					<pre
						dangerouslySetInnerHTML={{
							__html: `{
	"realEstate": {
		"adType": "Sale",
		"area": 51,
		"createdAt": "2022-10-19T16:13:50.230Z",
		"deletedAt": null,
		"disposition": "2+kk",
		"gps": [
			14.46683999,
			50.0888403344
		],
		"id": "8f1146f5ac14898baad0d908b5d9a1a6b6b35000",
		"imageUrl": "https://d18-a.sdn.cz/d_18/c_img_QJ_Jf/kjRpYE.jpeg?fl=res,749,562,3|shr,,20|jpg,90",
		"landArea": null,
		"landType": null,
		"origin": "Sreality",
		"price": 6200000,
		"priceNote": "včetně provize",
		"priceText": "6 200 000",
		"realEstateType": "Apartment",
		"url": "https://www.sreality.cz/detail/prodej/byt/2+kk/praha-zizkov-zerotinova/2145072972",
		"extended": {
			"basement": true,
			"buildingType": "Brick",
			"conditionType": "Good",
			"description": "Nabízíme na prodej pěkný, světlý byt 2+kk v Žerotínově ulici na Žižkově. Byt se nachází ve 2. patře rohového zrekonstruovaného činžovního domu s výtahem. Tvoří ho předsíň, obývací pokoj s kuchyňskou linkou, ložnice, koupelna s vanou, samostatné WC a komora. Byt má nová dřevěná špaletová okna, v ložnici jsou kvalitní parkety, v obývacím pokoji plovoucí podlaha, nový elektrický bojler. K bytu náleží sklep, který není započtený v podlahové ploše bytu, dále podíl na společném balkoně v mezipatře a také podíl na nebytových jednotkách v domě, ze kterých má vlastník bytu pravidelný příjem. Dále je v domě kolárna. 2 minuty pěšky na stanici tram a busu Biskupcova, v okolí mnoho obchodů a restaurací, parky Parukářka a Vítkov, sportovní areál Pražačka.",
			"elevator": true,
			"floor": 3,
			"houseType": null,
			"landWidth": null,
			"ownershipType": "Direct",
			"parkingType": null,
			"terraceType": "Balcony",
			"title": "Prodej bytu 2+kk 51 m²",
			"updatedAt": "2022-10-21T09:01:52.855Z"
		},
		"location": {
			"country": "Česko",
			"level": 10,
			"city": "Praha",
			"part": "Žižkov",
			"district": "okres Hlavní město Praha",
			"name": "Žižkov",
			"id": "-428824",
			"cohesion": "Praha",
			"region": "Hlavní město Praha"
		},
		"stats": [
			{
				"realEstateId": "8f1146f5ac14898baad0d908b5d9a1a6b6b35000",
				"source": "VisidooPrice",
				"stats": {
					"sale": {
						"average": 6776244,
						"median": 6494500,
						"range": [
							4300000,
							16865000
						],
						"sample": 178,
						"distance": 8
					},
					"rent": {
						"average": 25023,
						"median": 17834,
						"range": [
							10000,
							1120000
						],
						"sample": 171,
						"distance": 8
					}
				}
			},
			{
				"realEstateId": "8f1146f5ac14898baad0d908b5d9a1a6b6b35000",
				"source": "CityPerformer",
				"stats": {
					"indices": {
						"overall": 8.59,
						"mobility": 9.3,
						"wellbeing": 9.36,
						"relax": 7.48,
						"safety": 9.48,
						"services": 9.15,
						"environment": 7.46
					},
					"shareUrl": "https://app.cityperformer.com/share/453897777873684455/analysis/overview?share=cSnIlfrFhEgZzuFTEigbYolobBssJTkOzaitgrWP",
					"pricePercentile": null,
					"rentPrice": null,
					"salePrice": null,
					"daysOnMarket": null
				}
			},
			{
				"realEstateId": "8f1146f5ac14898baad0d908b5d9a1a6b6b35000",
				"source": "Valuo",
				"stats": {
					"sale": {
						"average": 6186759,
						"median": null,
						"range": [
							5798445,
							6575073
						],
						"sample": 0,
						"distance": 2
					},
					"rent": {
						"average": 16320,
						"median": null,
						"range": [
							14178,
							18462
						],
						"sample": 0,
						"distance": 2
					}
				}
			}
		]
	}
}`,
						}}
					/>
				</samp>

				<h2 className="h4">Popis výstupních parametrů</h2>
				<ul>
					<li>
						<strong>realEstate</strong>:{' '}
						<var>
							<Link href="/dosc/types/real-estate">RealEstate</Link>
						</var>{' '}
						- Nalezená nemovitost, obsahuje rozšíření <var>EXTENDED</var>, <var>LOCATION</var> a <var>STATS</var>
					</li>
				</ul>
			</Row>
		</>
	);
};

DocsRealEstate.displayName = 'DocsRealEstate';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs/real-estate',
	[Locale.en]: '/docs/real-estate',
});

export default DocsRealEstate;
