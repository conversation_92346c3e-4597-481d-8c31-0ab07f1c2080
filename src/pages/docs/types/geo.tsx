import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const GeoType: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace - Typy - Geo" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							Popis parametrů lokality
							<br /> <strong>Geo</strong>
						</>
					}
					margin="sm"
				/>
			</Row>

			<Row>
				<ul>
					<li>
						<strong>city</strong>: <var>string</var> - Obec
					</li>
					<li>
						<strong>cohesion</strong>: <var>string</var> - Region soudržnosti
					</li>
					<li>
						<strong>country</strong>: <var>string</var> - Země
					</li>
					<li>
						<strong>district</strong>: <var>string</var> - <PERSON><PERSON>
					</li>
					<li>
						<strong>fullName</strong>: <var>string</var> - <PERSON>lý název lokality
					</li>
					<li>
						<strong>id</strong>: <var>string</var> - Id oblasti v OpenStreetMap
					</li>
					<li>
						<strong>level</strong>: <var>number</var> - Administrativní úroveň dle OpenStreetMap
					</li>
					<li>
						<strong>name</strong>: <var>string</var> - Místní název
					</li>
					<li>
						<strong>part</strong>: <var>string</var> - Část obce (katastrální území)
					</li>
					<li>
						<strong>region</strong>: <var>string</var> - Kraj
					</li>
				</ul>
			</Row>
		</>
	);
};

GeoType.displayName = 'GeoType';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs/types/geo',
	[Locale.en]: '/docs/types/geo',
});

export default GeoType;
