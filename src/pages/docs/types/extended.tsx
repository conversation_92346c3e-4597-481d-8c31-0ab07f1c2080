import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const ExtendedType: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace - Typy - Extended" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							Rozšířené parametry nemovitosti
							<br /> <strong>Extended</strong>
						</>
					}
					margin="sm"
				/>
			</Row>

			<Row>
				<ul>
					<li>
						<strong>basement</strong>: <var>boolean</var> - Jestli má/nemá sklep
					</li>
					<li>
						<strong>buildingType</strong>: <var>string</var> - Typ stavby, nabývá hodnot <var>Brick</var> (cihlová),{' '}
						<var>Other</var> (ostatní), <var>Panel</var> (panelová), <var>Stone</var> (kamenná), <var>Wood</var> (dřevěná)
					</li>
					<li>
						<strong>conditionType</strong>: <var>string</var> - Stav nemovitosti, nabývá hodnot <var>Bad</var> (špatný),{' '}
						<var>Good</var> (dobrý), <var>In Progress</var> (stále ve výstavbě), <var>New</var> (novostabva)
					</li>
					<li>
						<strong>description</strong>: <var>string</var> - Celý popis inzerátu
					</li>
					<li>
						<strong>elevator</strong>: <var>boolean</var> - Jestli má/nemá výtah
					</li>
					<li>
						<strong>floor</strong>: <var>string | number</var> - Podlaží bytu (velmi nepřesné), v případě domu znamená, jestli
						je jedno nebo vícepatrový.
					</li>
					<li>
						<strong>houseType</strong>: <var>string</var> - Typ domu, nabývá hodnot <var>Cottage</var> (chata),{' '}
						<var>Detached</var> (samostatný), <var>Hut</var> (chalupa), <var>Terraced</var> (řadový). Platné pouze pro{' '}
						<code>realEstateType: House</code>
					</li>
					<li>
						<strong>landWidth</strong>: <var>number</var> - Uliční šíře pozemku (velmi nepřesné)
					</li>
					<li>
						<strong>ownershipType</strong>: <var>string</var> - Typ vlastnictví, nabývá hodnot <var>Cooperative</var>{' '}
						(družstevní), <var>Direct</var> (osobní), <var>Municipal</var> (obecní), <var>Shared</var> (společnost)
					</li>
					<li>
						<strong>parkingType</strong>: <var>string</var> - Typ parkování, nabývá hodnot <var>Garage</var> (garáž, garážové
						stání), <var>Place</var> (parkovací místo)
					</li>
					<li>
						<strong>state</strong>: <var>string</var> - Interní stav inzerátu, nabývá hodnot <var>New</var> (nový,
						nezpracovaný), <var>Processed</var> (zpracovaný), <var>Stale</var> (pravděpodobně smazaný, zařazen k prověření),{' '}
						<var>Deleted</var> (smazaný)
					</li>
					<li>
						<strong>terraceType</strong>: <var>string</var> - Typ terasy, nabývá hodnot <var>Balcony</var> (balkón),{' '}
						<var>Loggia</var> (lodžie), <var>Terrace</var> (terasa)
					</li>
					<li>
						<strong>title</strong>: <var>string</var> - Titulek inzerátu
					</li>
					<li>
						<strong>updatedAt</strong>: <var>string</var> - Čas v ISO8601, kdy byla provedena poslední změna záznamu
					</li>
				</ul>
			</Row>
		</>
	);
};

ExtendedType.displayName = 'ExtendedType';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs/types/extended',
	[Locale.en]: '/docs/types/extended',
});

export default ExtendedType;
