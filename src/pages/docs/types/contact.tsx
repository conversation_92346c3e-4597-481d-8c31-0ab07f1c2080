import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const ContactType: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace - Typy - Contact" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							Popis parametrů kontaktů
							<br /> <strong>Contact</strong>
						</>
					}
					margin="sm"
				/>
			</Row>

			<Row>
				<ul>
					<li>
						<strong>agencyName</strong>: <var>string</var> - <PERSON>méno realitní kancel<PERSON>e (pokud se jedná o realitní kancelář)
					</li>
					<li>
						<strong>contactType</strong>: <var>string</var> - Typ kontaktu, nabýv<PERSON> hodnot <var>Agent</var> (realitn<PERSON> ka<PERSON>),{' '}
						<var>Developer</var> (developer), <var>Municipality</var> (obecní), <var>Private</var> (soukromý),{' '}
						<var>Unknown</var> (neznámý)
					</li>
					<li>
						<strong>createdAt</strong>: <var>string</var> - Čas v ISO8601, kdy byl kontakt vytvořen
					</li>
					<li>
						<strong>email</strong>: <var>string</var> - Email kontaktu
					</li>
					<li>
						<strong>id</strong>: <var>string</var> - Naše interní id kontaktu
					</li>
					<li>
						<strong>name</strong>: <var>string</var> - Jméno/název kontaktu
					</li>
					<li>
						<strong>telephone</strong>: <var>string</var> - Telefon kontaktu (neformátovaný, nevalidovaný)
					</li>
				</ul>
			</Row>
		</>
	);
};

ContactType.displayName = 'ContactType';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs/types/contact',
	[Locale.en]: '/docs/types/contact',
});

export default ContactType;
