import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const StatsType: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace - Typy - Stats" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							Popis parametrů statistik a analýz
							<br /> <strong>Stats</strong>
						</>
					}
					margin="sm"
				/>
			</Row>

			<Row>
				<h2 className="h4">CityPerformer</h2>
				<ul>
					<li>
						<strong>indices</strong> - Socioekonomické indexy lokality
						<ul>
							<li>
								<strong>overall</strong>: <var>number</var> - Celkový
							</li>
							<li>
								<strong>mobility</strong>: <var>number</var> - <PERSON><PERSON>ita
							</li>
							<li>
								<strong>wellbeing</strong>: <var>number</var> - <PERSON><PERSON><PERSON> bydlení
							</li>
							<li>
								<strong>relax</strong>: <var>number</var> - Volný čas
							</li>
							<li>
								<strong>safety</strong>: <var>number</var> - Bezpečnost
							</li>
							<li>
								<strong>services</strong>: <var>number</var> - Služby
							</li>
							<li>
								<strong>environment</strong>: <var>number</var> - Životní prostředí
							</li>
						</ul>
					</li>
					<li>
						<strong>shareUrl</strong>: <var>string</var> - Url pro embedování do iframu
					</li>
					<li>
						<strong>daysOnMarket</strong>: <var>string</var> - Průměrná doba podobných nemovitostí na trhu
					</li>
				</ul>
			</Row>

			<Row>
				<h2 className="h4">Valuo</h2>
				<ul>
					<li>
						<strong>rent</strong> - Statistiky pro pronájmy
						<ul>
							<li>
								<strong>average</strong>: <var>number</var> - Průměrná cena
							</li>
							<li>
								<strong>median</strong>: <var>number</var> - Medián cen
							</li>
							<li>
								<strong>range</strong>: <var>Array&lt;number&gt;</var> - Minimum a maximum cen
							</li>
							<li>
								<strong>sample</strong>: <var>number</var> - Přibližný počet srovnávaných inzerátů
							</li>
							<li>
								<strong>distance</strong>: <var>number</var> - Přibližná maximální vzdálenost porovnávaných inzerátů od
								výchozí nemovitosti
							</li>
						</ul>
					</li>
					<li>
						<strong>sale</strong> - Statistiky pro prodeje
						<ul>
							<li>
								<strong>average</strong>: <var>number</var> - Průměrná cena
							</li>
							<li>
								<strong>median</strong>: <var>number</var> - Medián cen
							</li>
							<li>
								<strong>range</strong>: <var>Array&lt;number&gt;</var> - Minimum a maximum cen
							</li>
							<li>
								<strong>sample</strong>: <var>number</var> - Přibližný počet srovnávaných inzerátů
							</li>
							<li>
								<strong>distance</strong>: <var>number</var> - Přibližná maximální vzdálenost porovnávaných inzerátů od
								výchozí nemovitosti
							</li>
						</ul>
					</li>
				</ul>
			</Row>

			<Row>
				<h2 className="h4">VisidooPrice</h2>
				<ul>
					<li>
						<strong>rent</strong> - Statistiky pro pronájmy
						<ul>
							<li>
								<strong>average</strong>: <var>number</var> - Průměrná cena
							</li>
							<li>
								<strong>median</strong>: <var>number</var> - Medián cen
							</li>
							<li>
								<strong>range</strong>: <var>Array&lt;number&gt;</var> - Minimum a maximum cen
							</li>
							<li>
								<strong>sample</strong>: <var>number</var> - Přibližný počet srovnávaných inzerátů
							</li>
							<li>
								<strong>distance</strong>: <var>number</var> - Přibližná maximální vzdálenost porovnávaných inzerátů od
								výchozí nemovitosti
							</li>
						</ul>
					</li>
					<li>
						<strong>sale</strong> - Statistiky pro prodeje
						<ul>
							<li>
								<strong>average</strong>: <var>number</var> - Průměrná cena
							</li>
							<li>
								<strong>median</strong>: <var>number</var> - Medián cen
							</li>
							<li>
								<strong>range</strong>: <var>Array&lt;number&gt;</var> - Minimum a maximum cen
							</li>
							<li>
								<strong>sample</strong>: <var>number</var> - Přibližný počet srovnávaných inzerátů
							</li>
							<li>
								<strong>distance</strong>: <var>number</var> - Přibližná maximální vzdálenost porovnávaných inzerátů od
								výchozí nemovitosti
							</li>
						</ul>
					</li>
				</ul>
			</Row>
		</>
	);
};

StatsType.displayName = 'StatsType';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs/types/stats',
	[Locale.en]: '/docs/types/stats',
});

export default StatsType;
