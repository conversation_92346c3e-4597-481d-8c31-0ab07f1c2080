import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const RealEstateType: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace - Typy - RealEstate" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							Popis parametrů nemovitosti
							<br /> <strong>RealEstate</strong>
						</>
					}
					margin="sm"
				/>
			</Row>

			<Row>
				<ul>
					<li>
						<strong>adType</strong>: <var>string</var> - Typ inzerátu, nabývá hodnot <var>Sale</var> (prodej), <var>Rent</var>{' '}
						(pronájem), <var>Roommates</var> (spolubydlení).
					</li>
					<li>
						<strong>area</strong>: <var>number | null</var> - <PERSON>loc<PERSON> nemovitosti ve čtverečních metrech, <var>null</var> pro{' '}
						<code>realEstateType: Land</code>.
					</li>
					<li>
						<strong>createdAt</strong>: <var>string</var> - Čas v ISO8601, kdy byl inzerát zpracován.
					</li>
					<li>
						<strong>deletedAt</strong>: <var>string | null</var> - Čas v ISO8601, kdy byl inzerát smazán, <var>null</var> v
						případě, že je stále aktivní.
					</li>
					<li>
						<strong>disposition</strong>: <var>string | null</var> - Vyjádření dispozice v českém systému (1+kk, 1+1, 2+kk...),{' '}
						<var>null</var> pro <code>realEstateType: Land</code> a <code>realEstateType: House</code>
					</li>
					<li>
						<strong>gps</strong>: <var>Array&lt;number&gt;</var> - Souřadnice ve formátu DD [Longitude, Latitude].
					</li>
					<li>
						<strong>id</strong>: <var>string</var> - Naše interní id nemovitosti
					</li>
					<li>
						<strong>imageUrl</strong>: <var>string</var> - Odkaz na první náhledový obrázek
					</li>
					<li>
						<strong>landArea</strong>: <var>number</var> - Plocha pozemku ve čtverečních metrech, <var>null</var> pro{' '}
						<code>realEstateType: Apartment</code>.
					</li>
					<li>
						<strong>landType</strong>: <var>string</var> - Druh pozemku, nabývá hodnot <var>BuildPLot</var> (stavební),{' '}
						<var>Commercial</var> (komerční), <var>Other</var> (ostatní). <var>Null</var> pro <code>realEstateType: House</code>{' '}
						a <code>realEstateType: Apartment</code>.
					</li>
					<li>
						<strong>origin</strong>: <var>string</var> - Url origin původce inzerátu.
					</li>
					<li>
						<strong>price</strong>: <var>number | null</var> - Cena nemovitosti v Kč, <var>null</var>, když je neznámá nebo
						neuvedena.
					</li>
					<li>
						<strong>priceNote</strong>: <var>string</var> - Poznámka k ceně.
					</li>
					<li>
						<strong>priceText</strong>: <var>string</var> - Původní textová reprezentace ceny před zpracováním.
					</li>
					<li>
						<strong>realEstateType</strong>: <var>string</var> - Typ nemovitosti, nabývá hodnot <var>Apartment</var> (byt),{' '}
						<var>House</var> (dům), <var>Land</var> (pozemek).
					</li>
					<li>
						<strong>url</strong>: <var>string</var> - Odkaz na zdroj inzerátu.
					</li>
				</ul>

				<h3 className="h5">Volitelná rozšíření objektu nemovitosti</h3>
				<ul>
					<li>
						<strong>contact</strong>:{' '}
						<var>
							<Link href="/docs/types/contact">Contact</Link>
						</var>{' '}
						- Parametry kontaktu
					</li>
					<li>
						<strong>extended</strong>:{' '}
						<var>
							<Link href="/docs/types/extended">Extended</Link>
						</var>{' '}
						- Rozšířené parametry nemovitosti
					</li>
					<li>
						<strong>history</strong>: <var>Array&lt;&gt;</var> - Pole zjištěných sad změn u inzerátu. Formát{' '}
						<a href="https://github.com/flitbit/diff/" target="_blank" rel="noreferrer noopener">
							dle knihovny
						</a>
						. Lze se vrátit k jakémukoliv minulému stavu.
					</li>
					<li>
						<strong>images</strong>: <var>Array&lt;string&gt;</var> - Pole všech obrázků nemovitosti
					</li>
					<li>
						<strong>location</strong>:{' '}
						<var>
							<Link href="/docs/types/geo">Geo</Link>
						</var>{' '}
						- Objekt s lokalitou nemovitosti.
					</li>
					<li>
						<strong>source</strong>: <var>string</var> - Odkaz na zdrojový kód nemovitosti
					</li>
					<li>
						<strong>stats</strong>:{' '}
						<var>
							Array&lt;<Link href="/docs/types/stats">Stats</Link>&gt;
						</var>{' '}
						- Parametry analýz a statistik
					</li>
					<li>
						<strong>storedImages</strong>: <var>Array&lt;string&gt;</var> - Url všech archivovaných obrázků nemovitosti
					</li>
				</ul>
			</Row>
		</>
	);
};

RealEstateType.displayName = 'RealEstateType';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs/types/real-estate',
	[Locale.en]: '/docs/types/real-estate',
});

export default RealEstateType;
