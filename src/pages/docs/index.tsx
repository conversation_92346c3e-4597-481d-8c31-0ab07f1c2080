import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const DocsIndex: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace API" description="" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							Dokumentace <strong>Visidoo</strong> API
						</>
					}
					margin="sm"
				></ArticleIntro>
			</Row>

			<Row>
				<ul>
					<li>
						<Link href="/docs/autocomplete">GET /v1/autocomplete</Link>
					</li>
					<li>
						<Link href="/docs/real-estate">GET /v1/real-estate</Link>
					</li>
					<li>
						<Link href="/docs/report">
							POST /v1/report/{'{'}requestId{'}'}
						</Link>
					</li>
					<li>
						<Link href="/docs/history/list">GET /v1/history</Link>
					</li>
					<li>
						<Link href="/docs/history/detail">
							GET /v1/history/{'{'}realEstateId{'}'}
						</Link>
					</li>
				</ul>
			</Row>

			<Row>
				<h2 className="h4">Přehled jednotlivých typů</h2>
				<ul>
					<li>
						<Link href="/docs/types/contact">Contact</Link>
					</li>
					<li>
						<Link href="/docs/types/extended">Extended</Link>
					</li>
					<li>
						<Link href="/docs/types/geo">Geo</Link>
					</li>
					<li>
						<Link href="/docs/types/real-estate">RealEstate</Link>
					</li>
					<li>
						<Link href="/docs/types/stats">Stats</Link>
					</li>
				</ul>
			</Row>
		</>
	);
};

DocsIndex.displayName = 'DocsIndex';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs',
	[Locale.en]: '/docs',
});

export default DocsIndex;
