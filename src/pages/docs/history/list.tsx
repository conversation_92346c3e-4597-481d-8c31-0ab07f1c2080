import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const DocsHistoryList: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace - History - List" description="" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							Historie nabídek
							<br /> <strong>získání seznamu nabídek</strong>
						</>
					}
					margin="sm"
				/>
				<h2 className="h4">Žádost a přidělení API klíče</h2>
				<p>API klíč je vázaný na registrovaného uživatele Visidoo.</p>
				<p>
					Pokud jste registraci ještě neprovedli, tak ji naleznete pod odkazem <strong>Přihlášení</strong> v horním menu a
					následně odkazem <strong>Chci se registrovat</strong> v zobrazeném modalu. Dále postupujte podle instrukcí.
				</p>
				<p>
					Po registraci zašlete žádost o přidělení API klíče pro registrovaný e-mail na adresu{' '}
					<a href="mailto:<EMAIL>"><EMAIL></a>. API klíč vám bude co nejdříve vygenerován.
				</p>

				<h2 className="h4">Základní informace</h2>
				<ul>
					<li>
						<strong>HTTP metoda</strong>: <code>GET</code>
					</li>
					<li>
						<strong>Endpoint</strong>: <code>https://api.visidoo.com/v1/history</code>
					</li>
					<li>
						<strong>MIME typ</strong>: <code>application/json</code>
					</li>
					<li>
						<strong>Autorizace</strong>:{' '}
						<code>
							Authorization: Bearer{' '}
							<var>
								[
								<Link href="/muj-profil" target="_blank">
									váš přidělený api klíč
								</Link>
								]
							</var>
						</code>
					</li>
					<li>
						<strong>Query parametry</strong>:
						<ul>
							<li>
								<strong>from</strong> - Čas v milisekundách nebo ISO8601 od kdy (včetně) hledat nemovitosti
							</li>
							<li>
								<strong>to</strong> - Čas v milisekundách nebo ISO8601 do kdy hledat nemovitosti
							</li>
						</ul>
					</li>
					<li>
						<strong>Podmínky vstupu</strong>:
						<ul>
							<li>
								Rozdíl mezi <var>from</var> a <var>to</var> nesmí být větší než 24 hodin.
							</li>
							<li>
								<var>from</var> může být maximálně 7 dní do minulosti.
							</li>
						</ul>
					</li>
				</ul>

				<h2 className="h4">Příklad odpovědi</h2>
				<samp>
					<pre
						dangerouslySetInnerHTML={{
							__html: `{
    "updatedAt": 0,
    "realEstates": [
        "e34a1d12ad18a7eb71767c052dce5038d7100c94",
        "018e6ec74dbf0aeca0ebcf5694ea70a3c1e124a8"
    ]
}`,
						}}
					/>
				</samp>

				<h2 className="h4">Popis výstupních parametrů</h2>
				<ul>
					<li>
						<strong>updatedAt</strong>: <var>string</var> - Čas v ISO8601, ke kterému jsou data platná.
					</li>
					<li>
						<strong>realEstates</strong>: <var>string[]</var> - Pole id nalezených nemovitostí.
					</li>
				</ul>
			</Row>
		</>
	);
};

DocsHistoryList.displayName = 'DocsHistoryList';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs/history/list',
	[Locale.en]: '/docs/history/list',
});

export default DocsHistoryList;
