import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const DocsHistoryDetail: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace - History - Detail" description="" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							His<PERSON><PERSON> nabídek
							<br /> <strong>získání detailů</strong>
						</>
					}
					margin="sm"
				/>

				<h2 className="h4">Základní informace</h2>
				<ul>
					<li>
						<strong>HTTP metoda</strong>: <code>GET</code>
					</li>
					<li>
						<strong>Endpoint</strong>:{' '}
						<code>
							https://api.visidoo.com/v1/history/<var>[id]</var>
						</code>
					</li>
					<li>
						<strong>MIME typ</strong>: <code>application/json</code>
					</li>
					<li>
						<strong>Autorizace</strong>:{' '}
						<code>
							Authorization: Bearer{' '}
							<var>
								[
								<Link href="/muj-profil" target="_blank">
									váš přidělený api klíč
								</Link>
								]
							</var>
						</code>
					</li>
					<li>
						<strong>Id parametr v url</strong>: Id nemovitosti získané z dotazu na seznam
					</li>
				</ul>
				<h2 className="h4">Příklad odpovědi</h2>
				<samp>
					<pre
						dangerouslySetInnerHTML={{
							__html: `{
    "updatedAt": 0,
    "realEstate": {
        "adType": "Sale",
        "area": 120,
        "createdAt": 1648539645820,
        "deletedAt": null,
        "disposition": null,
        "gps": [
            16.9312530556,
            50.0715169444
        ],
        "history": []
        "storedImages": [
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/000.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/001.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/002.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/003.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/004.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/005.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/006.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/008.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/010.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/011.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/013.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/014.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/015.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/016.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/017.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/018.jpg,
            https://1eea010b-5d6b-4a38-95aa-e1f65ec60754.s3.eu-central-1.amazonaws.com/dbeb7e4b685b1e0be04d22696fe1503a28d3b7b8/019.jpg
        ],
        "landArea": 2047,
        "landType": "BuildPlot",
        "location": {
            "area": 36.630051,
            "country": "Česko",
            "city": "Jince",
            "part": null,
            "district": "okres Příbram",
            "name": "Jince",
            "fullName": "Jince, okres Příbram, Středočeský kraj, Střední Čechy, Česko",
            "id": "-441426",
            "cohesion": "Střední Čechy",
            "region": "Středočeský kraj"
        },
        "origin": "https://reality.idnes.cz",
        "price": 3012953,
        "priceNote": "Tržní odhad:4.519.430 Kč",
        "priceText": "3 012 953 Kč",
        "realEstateType": "House",
        "url": "https://reality.idnes.cz/detail/prodej/dum/jince/6242b7927209127cb0497205/"
    }
}`,
						}}
					/>
				</samp>

				<h2 className="h4">Popis výstupních parametrů</h2>
				<ul>
					<li>
						<strong>updatedAt</strong>: <var>string</var> - Čas v ISO8601, ke kterému jsou data platná.
					</li>
					<li>
						<strong>realEstate</strong>:{' '}
						<var>
							<Link href="/docs/types/real-estate">RealEstate</Link>
						</var>{' '}
						- Parametry nemovitosti
					</li>
				</ul>
			</Row>
		</>
	);
};

DocsHistoryDetail.displayName = 'DocsHistoryDetail';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs/history/detail',
	[Locale.en]: '/docs/history/detail',
});

export default DocsHistoryDetail;
