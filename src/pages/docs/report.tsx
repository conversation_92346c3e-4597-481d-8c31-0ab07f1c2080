import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const DocsReport: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace - Report" description="" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							<strong>Report</strong> aktu<PERSON>ln<PERSON> nabídky
						</>
					}
					margin="sm"
				/>
				<h2 className="h4">Žádost a přidělení API klíče</h2>
				<p>API klíč je vázaný na registrovaného uživatele Visidoo.</p>
				<p>
					Pokud jste registraci ještě neprovedli, tak ji naleznete pod odkazem <strong>Přihlášení</strong> v horním menu a
					následně odkazem <strong>Chci se registrovat</strong> v zobrazeném modalu. Dále postupujte podle instrukcí.
				</p>
				<p>
					Po registraci zašlete žádost o přidělení API klíče pro registrovaný e-mail na adresu{' '}
					<a href="mailto:<EMAIL>"><EMAIL></a>. API klíč vám bude co nejdříve vygenerován.
				</p>

				<h2 className="h4">Základní informace</h2>
				<ul>
					<li>
						<strong>HTTP metoda</strong>: <code>POST</code>
					</li>
					<li>
						<strong>Endpoint</strong>:{' '}
						<code>
							https://api.visidoo.com/v1/report/<var>[id]</var>
						</code>
					</li>
					<li>
						<strong>MIME typ</strong>: <code>application/json</code>
					</li>
					<li>
						<strong>Autorizace</strong>:{' '}
						<code>
							Authorization: Bearer{' '}
							<var>
								[
								<Link href="/muj-profil" target="_blank">
									váš přidělený api klíč
								</Link>
								]
							</var>
						</code>
					</li>
					<li>
						<strong>Id parametr v url</strong>: Vaše unikátní id pro dotaz
					</li>
				</ul>

				<h2 className="h4">Příklad dotazu</h2>
				<samp>
					<pre
						dangerouslySetInnerHTML={{
							__html: `{
    "filter": {
        "adType": "Sale",
        "area": {
            "gte": 0,
            "lte": null,
        },
        "disposition": ["2+1", "3+kk", "3+1"],
        "exactMatch": false,
        "houseType": [],
        "landArea": {
            "gte": 0,
            "lte": null,
        },
        "landType": [],
        "locationId": "",
        "ownershipType": [],
        "price": {
            "gte": 0,
            "lte": 10000000,
        },
        "radius": 20,
        "realEstateType": "Apartment",
        "terraceType": []
    }
}`,
						}}
					/>
				</samp>

				<h2 className="h4">Popis vstupních parametrů</h2>
				<p>
					Všechna následující pole jsou v dotazu <strong>povinná</strong>.
				</p>
				<ul>
					<li>
						<strong>adType</strong>: <var>string</var> - Typ inzerátu, musí být jedno z: <var>Sale</var> (prodej),{' '}
						<var>Rent</var> (pronájem), <var>Roommates</var> (spolubydlení).
					</li>
					<li>
						<strong>area</strong>: Rozsah plochy ve čtverečních metrech, platné pouze pro <code>realEstateType: Apartment</code>{' '}
						nebo <code>realEstateType: House</code>.
						<ul>
							<li>
								<strong>gte</strong>: <var>number</var> - Musí být větší rovno nule.
							</li>
							<li>
								<strong>lte</strong>: <var>number</var> - Musí být větší než <var>gte</var> nebo <code>null</code>.
							</li>
						</ul>
					</li>
					<li>
						<strong>disposition</strong>: <var>Array&lt;string&gt;</var> - Textová reprezentace českého stylu dispozice, 1+kk,
						1+1, 2+kk, 2+1, 3+kk, 3+1... Platné pouze pro <code>realEstateType: Apartment</code>. V případě prázdného pole se
						berou všechny varianty.
					</li>
					<li>
						<strong>exactMatch</strong>: <var>boolean</var> - Pokud je <code>true</code>, pak ignoruje inzeráty, které úplně
						nesplňují zadané parametry, typicky např. inzeráty bez ceny.
					</li>
					<li>
						<strong>houseType</strong>: <var>Array&lt;string&gt;</var> Typ domu, platné hodnoty jsou: <var>Detached</var>{' '}
						(samostatný), <var>Terraced</var> (řadový), <var>Cottage</var> (chalupa), <var>Hut</var> (chata). Platné pouze pro{' '}
						<code>realEstateType: House</code>. V případě prázdného pole se berou všechny varianty.
					</li>
					<li>
						<strong>landArea</strong>: Rozsah plochy pozemku ve čtverečních metrech, platné pouze pro{' '}
						<code>realEstateType: Land</code> nebo <code>realEstateType: House</code>.
						<ul>
							<li>
								<strong>gte</strong>: <var>number</var> - Musí být větší rovno nule.
							</li>
							<li>
								<strong>lte</strong>: <var>number</var> - Musí být větší než <var>gte</var> nebo <code>null</code>.
							</li>
						</ul>
					</li>
					<li>
						<strong>landType</strong>: <var>Array&lt;string&gt;</var> Typ pozemku, platné hodnoty jsou: <var>BuildPlot</var>{' '}
						(stavební), <var>Commercial</var> (komerční), <var>Other</var> (ostatní). Platné pouze pro{' '}
						<code>realEstateType: Land</code>. V případě prázdného pole se berou všechny varianty.
					</li>
					<li>
						<strong>locationId</strong>: <var>string</var> ID lokality z OpenStreetMap s prefixem &ldquo;-&rdquo;, např. pro{' '}
						<a href="https://www.openstreetmap.org/relation/438171" target="_blank" rel="noreferrer noopener">
							Brno
						</a>{' '}
						je platná hodnota <code>-438171</code>. Podporované jsou oblasti, které mají <code>boundary</code>:{' '}
						<var>administrative</var> a <code>admin_level</code> do úrovně <var>10</var>.
					</li>
					<li>
						<strong>ownershipType</strong>: <var>Array&lt;string&gt;</var> Typ vlastnictví, platné hodnoty jsou:{' '}
						<var>Cooperative</var> (Družstevní), <var>Direct</var> (Osobní), <var>Municipal</var> (Obecní), <var>Shared</var>{' '}
						(Společnosti). Platné pouze pro <code>adType: Sale</code>. V případě prázdného pole se berou všechny varianty.
					</li>
					<li>
						<strong>price</strong>:
						<ul>
							<li>
								<strong>gte</strong>: <var>number</var> - Musí být větší rovno nule.
							</li>
							<li>
								<strong>lte</strong>: <var>number</var> - Musí být větší než <var>gte</var> nebo <code>null</code>.
							</li>
						</ul>
					</li>
					<li>
						<strong>radius</strong>: <var>number</var> - Okruh v kilometrech, který se přidá k zadané lokalitě, musí být větší
						rovno nule, stupně po 5, maximálně 50 (5, 10, 15...).
					</li>
					<li>
						<strong>realEstateType</strong>: Typ nemovitosti, musí být jedno z: <var>Apartment</var> (byt), <var>House</var>{' '}
						(dům), <var>Land</var> (pozemek).
					</li>
					<li>
						<strong>terraceType</strong>: <var>Array&lt;string&gt;</var> Typ terasy, platné hodnoty jsou: <var>Terrace</var>{' '}
						(terasa), <var>Balcony</var> (balkón), <var>Loggia</var> (lodžie). Platné pouze pro{' '}
						<code>realEstateType: House</code> nebo <code>realEstateType: Apartment</code>. V případě prázdného pole se berou
						všechny varianty.
					</li>
				</ul>

				<h2 className="h4">Příklad odpovědi</h2>
				<samp>
					<pre
						dangerouslySetInnerHTML={{
							__html: `{
    "id": "d60fc7b7-96e4-48d0-bae0-975deb81239d",
    "updatedAt": 0,
    "realEstates": [
        {
            "adType": "Sale",
            "area": 33,
            "createdAt": 1661420462222,
            "disposition": null,
            "gps": [
                13.321192,
                49.691599
            ],
            "id": "9ec097db7c6b07857d5799b74f6633868a44161a",
            "imageUrl": "https://img4.realitymat.cz/image/3130532/50805399.jpg",
            "landArea": 624,
            "landType": null,
            "location": {
                "country": "Česko",
                "level": 10,
                "city": "Plzeň",
                "part": "Lhota u Dobřan",
                "district": "okres Plzeň-město",
                "name": "Lhota u Dobřan",
                "id": "-430611",
                "cohesion": "Jihozápad",
                "region": "Plzeňský kraj"
            },
            "origin": "Realitymat",
            "price": 4200000,
            "priceNote": "Cena konečná vč. všech poplatků. Pozemek stavební - nový dům či dostavba, sítě na pozemku.",
            "priceText": "4 200 000 Kč",
            "realEstateType": "House",
            "url": "https://www.realitymat.cz/detail/1015775/prodej-chaty-plzen-k-sinoru-33m2"
        },
        {
            "adType": "Sale",
            "area": 87,
            "createdAt": 1661420568362,
            "disposition": "3+1",
            "gps": [
                16.9312530556,
                50.0715169444
            ],
            "id": "3258d4a9dc0d5fd88d785244c6bfc85b8e7fee0c",
            "imageUrl": "https://reality.idnes.cz/file/thumbnail/630743e4a380692ab026d097?profile=front_detail_article_big_fit",
            "landArea": null,
            "landType": null,
            "location": {
                "country": "Česko",
                "level": 8,
                "city": "Hanušovice",
                "part": null,
                "district": "okres Šumperk",
                "name": "Hanušovice",
                "id": "-438323",
                "cohesion": "Střední Morava",
                "region": "Olomoucký kraj"
            },
            "origin": "iDnes reality",
            "price": 1990000,
            "priceNote": "včetně provize RK",
            "priceText": "1 990 000 Kč",
            "realEstateType": "Apartment",
            "url": "https://reality.idnes.cz/detail/prodej/byt/hanusovice-nadrazni/630743e2a380692ab026d094/"
        },
        {
            "adType": "Sale",
            "area": null,
            "createdAt": 1661420350122,
            "disposition": null,
            "gps": [
                18.4801783333,
                49.8587316667
            ],
            "id": "18f980d52f023addd78bc6554b2a781b37eaeb4a",
            "imageUrl": "https://reality.idnes.cz/file/thumbnail/630742dedf6a0c7abf43d518?profile=front_detail_article_big_fit",
            "landArea": 10165,
            "landType": "BuildPlot",
            "location": {
                "country": "Česko",
                "level": 8,
                "city": "Doubrava",
                "part": null,
                "district": "okres Karviná",
                "name": "Doubrava",
                "id": "-437919",
                "cohesion": "Moravskoslezsko",
                "region": "Moravskoslezský kraj"
            },
            "origin": "iDnes reality",
            "price": 240250,
            "priceNote": "Tržní odhad 1/2 podílu :480.500 Kč",
            "priceText": "240 250 Kč",
            "realEstateType": "Land",
            "url": "https://reality.idnes.cz/detail/prodej/pozemek/doubrava/630742dddf6a0c7abf43d516/"
        }
    ]
}`,
						}}
					/>
				</samp>

				<h2 className="h4">Popis výstupních parametrů</h2>
				<ul>
					<li>
						<strong>id</strong>: <var>string</var> - Vaše specifikované ID.
					</li>
					<li>
						<strong>updatedAt</strong>: <var>string</var> - Čas v ISO8601, ke kterému jsou data platná.
					</li>
					<li>
						<strong>realEstates</strong>:{' '}
						<var>
							Array&lt;<Link href="/docs/types/real-estate">RealEstate</Link>&gt;
						</var>{' '}
						- Pole objektů s nemovitostmi.
					</li>
				</ul>
			</Row>
		</>
	);
};

DocsReport.displayName = 'DocsReport';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs/report',
	[Locale.en]: '/docs/report',
});

export default DocsReport;
