import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const DocsAutocomplete: NextPage = () => {
	return (
		<>
			<PageSeo title="Dokumentace - Autocomplete" description="" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							<strong>Autocomplete</strong>
						</>
					}
					margin="sm"
				/>
				<h2 className="h4">Žádost a přidělení API klíče</h2>
				<p>API klíč je vázaný na registrovaného uživatele Visidoo.</p>
				<p>
					Pokud jste registraci ještě neprovedli, tak ji naleznete pod odkazem <strong>Přihlášení</strong> v horním menu a
					následně odkazem <strong>Chci se registrovat</strong> v zobrazeném modalu. Dále postupujte podle instrukcí.
				</p>
				<p>
					Po registraci zašlete žádost o přidělení API klíče pro registrovaný e-mail na adresu{' '}
					<a href="mailto:<EMAIL>"><EMAIL></a>. API klíč vám bude co nejdříve vygenerován.
				</p>

				<h2 className="h4">Základní informace</h2>
				<ul>
					<li>
						<strong>HTTP metoda</strong>: <code>GET</code>
					</li>
					<li>
						<strong>Endpoint</strong>: <code>https://api.visidoo.com/v1/autocomplete</code>
					</li>
					<li>
						<strong>MIME typ</strong>: <code>application/json</code>
					</li>
					<li>
						<strong>Autorizace</strong>:{' '}
						<code>
							Authorization: Bearer{' '}
							<var>
								[
								<Link href="/muj-profil" target="_blank">
									váš přidělený api klíč
								</Link>
								]
							</var>
						</code>
					</li>
					<li>
						<strong>Query parametry</strong>:
						<ul>
							<li>
								<strong>query</strong> - Vyhledáváná oblast, minimálně 2 znaky
							</li>
						</ul>
					</li>
				</ul>

				<h2 className="h4">Příklad odpovědi</h2>
				<samp>
					<pre
						dangerouslySetInnerHTML={{
							__html: `{
    "suggestions": [
        {
            "country": "Česko",
            "level": 8,
            "city": "Brno",
            "part": null,
            "district": "okres Brno-město",
            "name": "Brno",
            "fullName": "Brno, okres Brno-město, Jihomoravský kraj, Jihovýchod, Česko",
            "id": "-438171",
            "cohesion": "Jihovýchod",
            "region": "Jihomoravský kraj"
        },
        {
            "country": "Česko",
            "level": 10,
            "city": "Brno",
            "part": "Město Brno",
            "district": "okres Brno-město",
            "name": "Město Brno",
            "id": "-434760",
            "cohesion": "Jihovýchod",
            "region": "Jihomoravský kraj"
        },
        {
            "country": "Česko",
            "level": 10,
            "city": "Brno",
            "part": "Staré Brno",
            "district": "okres Brno-město",
            "name": "Staré Brno",
            "id": "-432423",
            "cohesion": "Jihovýchod",
            "region": "Jihomoravský kraj"
        },
        {
            "country": "Česko",
            "level": 10,
            "city": "Brno",
            "part": "Obřany",
            "district": "okres Brno-město",
            "name": "Obřany",
            "id": "-429748",
            "cohesion": "Jihovýchod",
            "region": "Jihomoravský kraj"
        },
        {
            "country": "Česko",
            "level": 10,
            "city": "Brno",
            "part": "Kníničky",
            "district": "okres Brno-město",
            "name": "Kníničky",
            "fullName": "Kníničky, Brno, okres Brno-město, Jihomoravský kraj, Jihovýchod, Česko",
            "id": "-436052",
            "cohesion": "Jihovýchod",
            "region": "Jihomoravský kraj"
        }
    ]
}`,
						}}
					/>
				</samp>

				<h2 className="h4">Popis výstupních parametrů</h2>
				<ul>
					<li>
						<strong>suggestions</strong>:{' '}
						<var>
							<Link href="/dosc/types/geo">Geo</Link>[]
						</var>{' '}
						- Pole nalezených lokalit.
					</li>
				</ul>
			</Row>
		</>
	);
};

DocsAutocomplete.displayName = 'DocsAutocomplete';

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/docs/autocomplete',
	[Locale.en]: '/docs/autocomplete',
});

export default DocsAutocomplete;
