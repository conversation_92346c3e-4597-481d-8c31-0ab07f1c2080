import { fetchScraperData } from 'api/realEstate/fetchScraperData';
import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Accordion } from 'components/ui/core/Accordion';
import { AccordionItem } from 'components/ui/core/AccordionItem';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticProps, NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { ONE_DAY } from 'utils/constants';

type Props = {
	scrapers: {
		name: string;
		url: string;
	}[];
	count: number;
};

const FrequentlyAskedQuestions: NextPage<Props> = ({ scrapers, count }) => {
	return (
		<>
			<PageSeo title="FAQ: často kladen<PERSON>" />
			<Row>
				<ArticleIntro
					bg
					title={
						<>
							<strong>FAQ:</strong>
							<br /> často kladen<PERSON>
						</>
					}
					margin="sm"
				>
					<p className="caption">
						Dali jsem dohromady vaše nejčastější dotazy a rovnou připravili odpovědi. Možná vás zajímá něco jiného, tak se
						klidně <a href="mailto:<EMAIL>">ozvěte</a>.
					</p>
				</ArticleIntro>
				<Accordion margin="lg">
					<AccordionItem
						title="Jak to funguje?"
						content={
							<>
								<p>
									Naše služba vám podle vašich nastavených filtrů zasílá nejnovější realitní inzeráty, ihned po vložení
									realitním makléřem na námi hlídané realitní servery. Obratem Vám nový inzerát zasíláme do e&#8209;mailu
									a tím získáte konkurenční výhodu oproti ostatním zájemcům.
								</p>
								<p>
									<Link href="/jak-to-funguje">Více informací</Link>
								</p>
							</>
						}
					/>
					<AccordionItem
						title="V čem je to jiné než klasické nastavení hlídacích psů na jiných serverech?"
						content={
							<p>
								Jde hlavně o rychlost. U nás máte nový inzerát na vašem e&#8209;mailu do 1 minuty od jeho zveřejnění, a to
								ze všech {count} realitních serverů. Jen pro zajímavost, na inzertních serverech posílají nové inzeráty
								nejčastěji jednou za den a všechny naráz. To už bývá většinou pozdě a na danou nemovitost už je spousta
								zájemců.
							</p>
						}
					/>
					<AccordionItem
						title="Musím se někde registrovat?"
						content={
							<p>
								<strong>Nemusíte</strong>, stačí zadat pouze vaši e&#8209;mailovou adresu, nastavit si filtry a vyrat si
								jeden z tarifů. Nicméně pokud se zaregistrujete, pak budete mít možnost jednoduše spravovat filtry a
								předplatné v naší zákaznické sekci.
							</p>
						}
					/>
					<AccordionItem
						title="Jak často mi přijde nový inzerát do e&#8209;mailu?"
						content={
							<p>
								Záleží na nastavení vašich filtrů a zvolení lokality, platí že čím obecnější filtr, tím více nových
								inzerátů.
							</p>
						}
					/>
					<AccordionItem
						title="Jak rychle mi přijde uporoznění na nový inzerát po jeho zveřejnění?"
						content={
							<p>
								Tvrdíme, že <strong>do 1 minuty</strong>. Nicméně ve špičkách se může doba trošku protáhnout, nejvýš však na
								několik minut.
							</p>
						}
					/>
					<AccordionItem
						title="E&#8209;maily se mi stahují jednou za hodinu, proč?"
						content={
							<p>
								Nejspíš používáte nějakého e&#8209;mailového klienta, že? Prověřte prosím nastavení klienta, jak často
								stahuje e&#8209;maily ze serveru.
							</p>
						}
					/>
					<AccordionItem
						title="Chodí mi málo e&#8209;mailů"
						content={
							<ol>
								<li>
									E&#8209;maily vám přeskakují doručenou poštu a končí v některé ze složek pro promoakce či hromadné
									e&#8209;maily nebo v nejhoším případě skončí ve spamu. Zkuste se tam podívat a e&#8209;maily označit
									jako vyžadované.
								</li>
								<li>
									Je možné, že máte nastavený hodně specifický filtr. Podívejte se na předpoklad počtu e&#8209;mailů a
									zkuste ho případně více zobecnit.
								</li>
							</ol>
						}
					/>
					<AccordionItem
						title="Můžu si přes Visidoo domluvit schůzku? Nebo rezerovat nemovitost?"
						content={
							<p>
								Bohužel nemůžete. Naše služba není prodejcem nemovitostí ani nedisponuje kontaktními informacemi
								prodávajícího.
							</p>
						}
					/>
					{scrapers.length > 0 && (
						<AccordionItem
							title="Které servery za mě budete hlídat?"
							content={
								<p>
									Hlídáme celkem <strong>{count} realitních serverů</strong>, které se zaměřují na prodej a pronájem všech
									typů nemovitostí. Konkrétně se jedná o tyto:{' '}
									{scrapers
										.slice(0, -1)
										.map(({ name }) => name)
										.join(', ')}{' '}
									a {scrapers[scrapers.length - 1].name}
								</p>
							}
						/>
					)}
				</Accordion>

				<h2 className="h3">Platby</h2>
				<Accordion margin="lg">
					<AccordionItem
						title="Kolik Visidoo stojí?"
						content={
							<p>
								Aktuální ceník tarifů naleznete na stránce s <Link href="/premium-ucet">tarify</Link>. Služba je na 7 dní
								zdarma formou trial verze, kteréholiv z tarifů.
							</p>
						}
					/>
					<AccordionItem
						title="Je možné se kdykoliv odhlásit?"
						content={
							<p>
								Ano, předplatné můžete kdykoliv zrušit. Stačí v <Link href="/muj-profil">profilu</Link> kliknout na tlačítko{' '}
								<strong>Změnit nebo zrušit tarif</strong>. Předplatné zůstane aktivní až do konce již zaplaceného období.
								Pokud si přejete rovnou zrušit i všechna e-mailová upozornění, je potřeba ručně deaktivovat aktivní filtry.
							</p>
						}
					/>
					<AccordionItem
						title="Jakou platební bránu používáte?"
						content={
							<p>
								Používáme platební bránu <strong>Stripe</strong>, jednu z nejrozšířenějších a nejdůvěryhodnějších platebních
								platforem na světě. Stripe zajišťuje bezpečné zpracování plateb a ochranu vašich údajů v souladu s
								nejvyššími bezpečnostními standardy (včetně certifikace PCI-DSS).
							</p>
						}
					/>
					<AccordionItem
						title="Je možné platit jinak přes platební bránou?"
						content={
							<p>
								Ano, v odůvodněných případech je možné platit na základě vystavené faktury. V takovém případě prosím
								kontaktujte naši zákaznickou podporu na e-mailu <a href="mailto:<EMAIL>"><EMAIL></a>.
							</p>
						}
					/>
					<AccordionItem
						title="Jak zrušit předplatné?"
						content={
							<p>
								Zrušení předplatného provedete jednoduše ve svém profilu pomocí tlačítka Změnit nebo zrušit tarif. Služba
								zůstane aktivní do konce již zaplaceného období. Pokud zároveň nechcete nadále dostávat žádná upozornění,
								doporučujeme také deaktivovat všechny aktivní filtry.
							</p>
						}
					/>
					<AccordionItem
						title="Jak změním tarif?"
						content={
							<>
								<p>
									Změnu tarifu provedete ve svém <Link href="/muj-profil">profilu</Link> kliknutím na tlačítko Změnit nebo
									zrušit tarif. Následně si vyberete nový tarif a dokončíte změnu podle pokynů platební brány Stripe. Nový
									tarif se projeví ihned po úspěšném dokončení platby.
								</p>
								<p>
									Při <strong>navýšení tarifu</strong> vám bude účtována pouze poměrná částka do konce aktuálního období a
									vyšší tarif začne platit ihned.
								</p>
								<p>
									Při <strong>snížení tarifu</strong> se změna projeví až po skončení aktuálně zaplaceného předplatného.
								</p>
							</>
						}
					/>
				</Accordion>
			</Row>
			<CtaCommon />
		</>
	);
};
FrequentlyAskedQuestions.displayName = 'FrequentlyAskedQuestions';

export default FrequentlyAskedQuestions;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/casto-kladene-otazky',
	[Locale.en]: '/faq',
});

export const getStaticProps: GetStaticProps<Props> = async () => {
	const { data } = await fetchScraperData();

	return {
		revalidate: ONE_DAY / 1000,
		props: { scrapers: data.scrapers, count: data.count },
	};
};
