import { FilterForm } from 'components/ui/filter/FilterForm';
import { ReactElement } from 'react';
import { NextPageWithLayout } from 'types/next';

const NewFilterWidget: NextPageWithLayout = () => <FilterForm redirectPath="/widget/dekujeme" showExtra={true} />;

NewFilterWidget.displayName = 'NewFilterWidget';

NewFilterWidget.getLayout = (page: ReactElement) => page;

export default NewFilterWidget;
