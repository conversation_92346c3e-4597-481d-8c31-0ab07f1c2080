import { fetchFilterOffers } from 'api/filter/fetchFilterOffers';
import { PageSeo } from 'components/meta/PageSeo';
import { FilterDataContext } from 'components/search/FilterDataProvider';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Title } from 'components/ui/core/Title';
import { FilterBox } from 'components/ui/filter/FilterBox';
import { OfferBox } from 'components/ui/offer/OfferBox';
import { useRouter } from 'next/router';
import { ReactElement, useContext, useEffect, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { ApiResponse } from 'types/filter';
import { NextPageWithLayout } from 'types/next';
import { Offer, OfferResponse } from 'types/offer';

export const WidgetThankYou: NextPageWithLayout = () => {
	const router = useRouter();
	const { formatMessage } = useIntl();
	const { data } = useContext(FilterDataContext);
	const [lastOffers, setLastOffers] = useState<Offer[]>([]);
	const intl = useIntl();

	const filterActivatedIntlString = intl.formatMessage({
		description: 'thankPage-filterActive',
		defaultMessage: 'Váš nový filtr je aktivní',
	});

	useEffect(() => {
		if (!data) {
			router.push('/widget/novy-filtr');
			return;
		}

		fetchFilterOffers(data.id).then((result: ApiResponse<OfferResponse>) => {
			setLastOffers(result.data.realEstates);
		});
	}, [data, setLastOffers, router]);

	return (
		<>
			<PageSeo title={filterActivatedIntlString} />
			<ArticleIntro
				title={
					<>
						<FormattedMessage
							description={'thankPage-filterActive'}
							defaultMessage={'<strong>Váš nový filtr</strong> je aktivní'}
							values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
						/>
					</>
				}
				margin="sm"
			>
				<p>
					<FormattedMessage
						description={'thankPage-allSet'}
						defaultMessage={
							'Nyní již nemusíte kontrolovat realitní servery. Jakmile se objeví nová nemovitost, ihned Vás na ni upozorníme e\u2011mailem.'
						}
					/>
				</p>
			</ArticleIntro>

			{data && (
				<>
					<div className="u-mb-md">
						<FilterBox {...data} />
					</div>
					<div className="u-mb-xl u-text-center u-font-lg u-font-medium">
						<Title tagName="h2" margin="sm" decorated>
							<FormattedMessage
								description={'thankPage-likes'}
								defaultMessage={'Každý <strong>like</strong> se počítá!'}
								values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
							/>
						</Title>
						<p className="u-mb-sm">
							<FormattedMessage
								description={'thankPage-happyIfShare'}
								defaultMessage={
									'Budeme rádi, když nás nasdílite na svých sociálních sítích. {br}Podpoříte nás a můžeme tak dále naše služby pro vás zlepšovat.'
								}
								values={{ br: <br /> }}
							/>
						</p>
						<p>
							<a
								href={formatMessage({
									defaultMessage: 'https://www.facebook.com/sharer/sharer.php?u=https://www.visidoo.cz',
									description: 'thankPage-facebookShareLink',
								})}
								className="btn btn--facebook"
								target="_blank"
								rel="noreferrer"
							>
								<span className="btn__text">
									<FormattedMessage description={'thankPage-fbShare'} defaultMessage={'Sdílet na Facebooku'} />
								</span>
							</a>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<a
								href={formatMessage({
									defaultMessage:
										'https://twitter.com/intent/tweet?url=https://www.visidoo.cz&text=Hled%C3%A1te%20bydlen%C3%AD?%20Vyzkou%C5%A1ejte%20Visidoo.%0AHl%C3%ADd%C3%A1%20za%20v%C3%A1s%20realitn%C3%AD%20trh%20a%20upozor%C5%88ujeme%20na%20nejnov%C4%9Bj%C5%A1%C3%AD%20inzer%C3%A1ty.',
									description: 'thankPage-twitterShareLink',
								})}
								className="btn btn--twitter"
								target="_blank"
								rel="noreferrer"
							>
								<span className="btn__text">
									<FormattedMessage description={'thankPage-twShare'} defaultMessage={'Na Twitter'} />
								</span>
							</a>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<a
								href={formatMessage({
									defaultMessage: 'https://www.linkedin.com/shareArticle?mini=true&url=https://www.visidoo.cz',
									description: 'thankPage-linkedinShareLink',
								})}
								className="btn btn--linkedin"
								target="_blank"
								rel="noreferrer"
							>
								<span className="btn__text">
									<FormattedMessage description={'thankPage-linkedinShare'} defaultMessage={'Na LinkedIn'} />
								</span>
							</a>
						</p>
					</div>
					{lastOffers.length > 0 && (
						<section className="c-crossroad u-mb-md">
							<Title tagName="h2" margin="md" align="center">
								<FormattedMessage description={'thankPage-lastOffers'} defaultMessage={'Poslední nalezené nabídky'} />
							</Title>
							<div className="c-crossroad__list grid">
								{lastOffers.map((offer) => (
									<div className="c-crossroad__item grid__cell grid__cell--eq size--6-12@sm size--4-12@md" key={offer.id}>
										<OfferBox offer={offer} />
									</div>
								))}
							</div>
						</section>
					)}
				</>
			)}
		</>
	);
};

WidgetThankYou.displayName = 'WidgetThankYou';

WidgetThankYou.getLayout = (page: ReactElement) => page;

export default WidgetThankYou;
