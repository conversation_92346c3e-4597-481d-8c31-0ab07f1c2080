import { fetchFilterById } from 'api/filter/fetchFilterById';
import { PageSeo } from 'components/meta/PageSeo';
import { Button } from 'components/ui/core/Button';
import { Row } from 'components/ui/core/Row';
import { Title } from 'components/ui/core/Title';
import { FilterBox } from 'components/ui/filter/FilterBox';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticPaths, GetStaticProps, NextPage } from 'next';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FormattedMessage, useIntl } from 'react-intl';
import { Filter, FilterState } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { ONE_DAY } from 'utils/constants';

type Query = {
	filterId: string;
};

type Props = {
	filter: Filter;
};

const LpFilterUnavailable: NextPage<Props> = ({ filter }) => {
	const { formatMessage } = useIntl();
	const router = useRouter();
	return (
		<>
			<PageSeo
				title={formatMessage({
					defaultMessage: 'V<PERSON>š filtr bohužel nelze aktivovat',
					description: 'lpUnavailable-title',
				})}
			/>
			<Row>
				<Title tagName="h1" margin="sm" decorated>
					<strong>
						<FormattedMessage defaultMessage="Váš filtr " description="lpUnavailable-yourFilter" />
					</strong>{' '}
					<FormattedMessage defaultMessage="bohužel nelze aktivovat" description="lpUnavailable-isDeactivated" />!
				</Title>
				<p className="caption u-text-center u-mb-sm">
					Váš účet má aktivní <strong>dva vyhledávací filtry</strong>.
					<br />
					Pro využívání více aktivních filtrů současně je potřeba přejít na <strong>Premium verzi</strong>.
					<br /> Aktivací této verze navíc získáte mnoho <Link href="/premium-ucet">dalších výhod</Link>.
				</p>
				<p className="u-text-center u-mb-md">
					<Button href="/premium" text="Aktivovat Premium verzi" size="lg" />
				</p>
				{/*<FilterSatisfactionCta />*/}
				<div className="u-mb-md">{!router.isFallback && <FilterBox {...filter} state={FilterState.ACTIVE} />}</div>
			</Row>
		</>
	);
};
LpFilterUnavailable.displayName = 'LpFilterUnavailable';

export default LpFilterUnavailable;

export const getStaticProps: GetStaticProps<Props, Query> = async ({ params }) => {
	if (!params) return { notFound: true };
	const { filterId } = params;
	if (!filterId) return { notFound: true };

	let filter: Filter;
	try {
		const { data } = await fetchFilterById(filterId);
		filter = data.filter;
	} catch {
		return { notFound: true };
	}

	return {
		revalidate: ONE_DAY / 1000,
		props: { filter },
	};
};

export const getStaticPaths: GetStaticPaths = async () => {
	return {
		paths: [],
		fallback: true,
	};
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/lp-filter-unavailable/[filterId]',
	[Locale.en]: '/lp-filter-unavailable/[filterId]',
});
