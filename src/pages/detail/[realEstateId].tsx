import { fetchRealEstateGet } from 'api/realEstate/fetchRealEstateGet';
import { LayoutDetail } from 'components/ui/layout/LayoutDetail';
import { OfferDetail } from 'components/ui/offer/OfferDetail';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticPaths, GetStaticProps } from 'next';
import { useRouter } from 'next/router';
import { ReactElement } from 'react';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { NextPageWithLayout } from 'types/next';
import { Offer } from 'types/offer';
import { ONE_DAY } from 'utils/constants';
import { DetailPage } from 'components/ui/offer/DetailPage';

type Query = {
	realEstateId: string;
};

type Props = {
	realEstate?: Offer;
};

const RealEstateDetail: NextPageWithLayout<Props> = ({ realEstate }) => {
	const router = useRouter();
	if (router.isFallback || !realEstate) return null;

	return (
		<DetailPage realEstate={realEstate} offerVariant="public">
			<OfferDetail {...realEstate} />
		</DetailPage>
	);
};

RealEstateDetail.displayName = 'RealEstateDetail';

export default RealEstateDetail;

export const getStaticProps: GetStaticProps<Props, Query> = async ({ params }) => {
	if (!params) return { notFound: true };
	const { realEstateId } = params;
	if (!realEstateId) return { notFound: true };

	let realEstate: Offer;
	try {
		const { data } = await fetchRealEstateGet(realEstateId);
		realEstate = data.realEstate;
	} catch {
		return { notFound: true };
	}

	return {
		revalidate: ONE_DAY / 1000,
		props: { realEstate },
	};
};

RealEstateDetail.getLayout = (page: ReactElement) => <LayoutDetail>{page}</LayoutDetail>;

export const getStaticPaths: GetStaticPaths = async () => {
	return {
		paths: [],
		fallback: true,
	};
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/detail/[realEstateId]',
	[Locale.en]: '/detail/[realEstateId]',
});
