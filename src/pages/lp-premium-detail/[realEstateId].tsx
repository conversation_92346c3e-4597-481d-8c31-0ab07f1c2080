import { fetchRealEstateGet } from 'api/realEstate/fetchRealEstateGet';
import { fetchScraperData } from 'api/realEstate/fetchScraperData';
import { PremiumDialogsProvider } from 'components/dialog/PremiumDialogsProvider';
import { PageSeo } from 'components/meta/PageSeo';
import { PremiumTariffsCompare } from 'components/premium/PremiumTariffsCompare';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Button } from 'components/ui/core/Button';
import { Row } from 'components/ui/core/Row';
import { RealEstateLocation } from 'components/ui/filter/RealEstateLocation';
import { FooterSeparator } from 'components/ui/FooterSeparator';
import { OfferPrice } from 'components/ui/offer/OfferPrice';
import { OfferPriceM2 } from 'components/ui/offer/OfferPriceM2';
import { OfferTitle } from 'components/ui/offer/OfferTitle';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticPaths, GetStaticProps, NextPage } from 'next';
import { FormattedMessage, useIntl } from 'react-intl';
import { ScraperData } from 'types/admin';
import { RealEstateType } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { Offer, OfferType } from 'types/offer';
import { ONE_DAY } from 'utils/constants';

type Query = {
	realEstateId: string;
};

type Props = {
	realEstate?: Offer;
	scrapers: ScraperData[];
};

const LpPremiumDetail: NextPage<Props> = ({ realEstate, scrapers = [] }) => {
	const { formatMessage } = useIntl();

	return (
		<>
			<PageSeo
				title={formatMessage({
					defaultMessage: 'Pro zobrazení nemovitosti potřebujete premium',
					description: 'lpPremiumDetail-title-head',
				})}
				description={formatMessage({
					defaultMessage: 'Pro zobrazení nemovitosti si musíte vybrat jeden z prémiových tarifů',
					description: 'lpPremiumDetail-mainText',
				})}
			/>
			<Row lg>
				<ArticleIntro
					bg
					margin="xs"
					title={
						<FormattedMessage
							description="lpPremiumDetail-title"
							defaultMessage="Pro zobrazení nemovitosti si musíte vybrat jeden z <strong>prémiových tarifů</strong>"
							values={{
								strong: (...chunks: string[]) => <strong>{chunks}</strong>,
							}}
						/>
					}
				>
					<p className="caption">Kdo ví, třeba již tato nabídka bude vaše vysněné bydlení...</p>
				</ArticleIntro>
			</Row>

			{realEstate && (
				<Row>
					<div className="b-product-lg u-mb-lg">
						<div className="b-product-lg__img">
							<img src={realEstate.imageUrl} loading="lazy" decoding="async" alt="" width="350" height="250" />
						</div>
						<div className="b-product-lg__content">
							<h1 className="b-product-lg__title h5">
								<OfferTitle {...realEstate} showLink={false} />
							</h1>
							<p className="b-product-lg__address">
								<RealEstateLocation location={realEstate.location} />
							</p>
							<p className="b-product-lg__details">
								<OfferPrice {...realEstate} />
								{realEstate.adType === OfferType.SALE &&
									(realEstate.realEstateType === RealEstateType.APARTMENT ||
										realEstate.realEstateType === RealEstateType.LAND) && <OfferPriceM2 {...realEstate} />}
							</p>
						</div>
					</div>
				</Row>
			)}

			<PremiumDialogsProvider scrapers={scrapers}>
				<PremiumTariffsCompare count={scrapers.length} />
			</PremiumDialogsProvider>

			<Row>
				<div className="u-mb-lg u-text-center">
					<Button size="lg" variant="outline" href="/premium-ucet">
						Více informací o tarifech
					</Button>
				</div>
			</Row>

			<FooterSeparator />
		</>
	);
};

LpPremiumDetail.displayName = 'LpPremiumDetail';

export default LpPremiumDetail;

export const getStaticProps: GetStaticProps<Props, Query> = async ({ params }) => {
	if (!params) return { notFound: true };
	const { realEstateId } = params;
	if (!realEstateId) return { notFound: true };

	let realEstate: Offer;
	try {
		const { data } = await fetchRealEstateGet(realEstateId);
		realEstate = data.realEstate;
	} catch {
		return { notFound: true };
	}

	let scrapers: ScraperData[];
	try {
		const { data } = await fetchScraperData();
		scrapers = data.scrapers;
	} catch {
		scrapers = [];
	}

	return {
		revalidate: ONE_DAY / 1000,
		props: { realEstate, scrapers },
	};
};

export const getStaticPaths: GetStaticPaths = async () => {
	return {
		paths: [],
		fallback: true,
	};
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/lp-premium-detail/[realEstateId]',
	[Locale.en]: '/lp-premium-detail/[realEstateId]',
});
