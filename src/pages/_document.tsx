import Document, { Document<PERSON>ontext, Head, Html, Main, NextScript } from 'next/document';

export default class CustomDocument extends Document {
	static async getInitialProps(ctx: DocumentContext) {
		const initialProps = await Document.getInitialProps(ctx);
		return { ...initialProps };
	}

	render() {
		return (
			<Html lang="cs" className="no-js">
				<Head>
					{/*
						Upcomming line is HACK because of this https://github.com/vercel/next.js/issues/12984.
						It must stay here in _document.tsx. Otherwise it doesn't work.
						In react issues is proposal for <Fragment> with dangerouslySetInnerHTML.
						We will fix it in future :)
					*/}
					<style
						dangerouslySetInnerHTML={{
							__html: `
								</style>
								<link
									rel="preload"
									as="style"
									href="https://use.typekit.net/aqv1eah.css"
									onload="this.rel='stylesheet';"
								/>
								<style>`,
						}}
					></style>
					<script defer src="https://unpkg.com/@superkoders/cookie@2.3.1/dist/js/cookie.js"></script>
					<link rel="stylesheet" href="https://unpkg.com/@superkoders/cookie@2.3.1/dist/css/cookie.css"></link>
				</Head>
				<body>
					<Main />
					<NextScript />
				</body>
			</Html>
		);
	}
}
