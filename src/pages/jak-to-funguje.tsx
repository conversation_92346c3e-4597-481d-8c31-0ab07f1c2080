import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { FormattedMessage, useIntl } from 'react-intl';
import { PageSeo } from 'components/meta/PageSeo';
import { Row } from 'components/ui/core/Row';
import { Workflow } from 'components/ui/crossroad/Workflow';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const HowItWorks: NextPage = () => {
	const intl = useIntl();
	const howItWorksIntlString = intl.formatMessage({ description: 'howItWorks-title', defaultMessage: 'Jak to funguje' });

	return (
		<>
			<PageSeo title={howItWorksIntlString} />
			<Row>
				<ArticleIntro title={howItWorksIntlString} bg margin="sm">
					<p className="caption">
						<FormattedMessage
							description={'howItWorks-mainText'}
							defaultMessage={
								'<PERSON><PERSON><PERSON> cílem je udělat nástroj co nejjednodušš<PERSON> a uživatelsky přívětivý. Ce<PERSON> funkčnost jsme shrnuli do 4 kroků.'
							}
						/>
					</p>
				</ArticleIntro>
				<Workflow />
			</Row>
			<CtaCommon />
		</>
	);
};
HowItWorks.displayName = 'HowItWorks';

export default HowItWorks;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/jak-to-funguje',
	[Locale.en]: '/how-it-works',
});
