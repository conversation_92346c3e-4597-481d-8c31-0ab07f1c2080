import { fetchScraperData } from 'api/realEstate/fetchScraperData';
import { ThankYouPageContents } from 'components/dekujeme/ThankYouPageContents';
import { FilterDataContext } from 'components/search/FilterDataProvider';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticProps, NextPage } from 'next';
import { useRouter } from 'next/router';
import { useContext, useEffect } from 'react';
import { ScraperData } from 'types/admin';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { ONE_DAY } from 'utils/constants';

type Props = {
	scrapers: ScraperData[];
};

const ThankYou: NextPage<Props> = ({ scrapers }) => {
	const router = useRouter();
	const { data } = useContext(FilterDataContext);
	// const data = dummyFilterData;
	const { isSubscribed } = router.query;

	// const [questionnaire, setQuestionnaire] = useState<Questionnaire | null>(null);
	// const [questionnaireLoading, setQuestionnaireLoading] = useState(false);

	useEffect(() => {
		if (!data) {
			router.replace('/');
		}
	}, [data, router]);

	// useEffect(() => {
	// 	const createQuestionnaire = async (filterData: Filter) => {
	// 		setQuestionnaireLoading(true);
	// 		try {
	// 			if (isValidFilterForQuestionnaire(filterData.adType)) {
	// 				const questionnaireData = await fetchQuestionnaireGet(filterData.email);
	//
	// 				if (questionnaireData.data.questionnaire) {
	// 					setQuestionnaire(questionnaireData.data.questionnaire);
	// 				}
	// 			}
	// 		} catch (error) {
	// 			console.error(error);
	// 		} finally {
	// 			setQuestionnaireLoading(false);
	// 		}
	// 	};
	//
	// 	if (data) {
	// 		createQuestionnaire(data);
	// 	}
	// }, [data]);F

	if (!data) return null;

	// return <ThankYouPageContents data={data} questionnaire={questionnaire} questionnaireLoading={questionnaireLoading} />;
	return (
		<ThankYouPageContents
			data={data}
			questionnaire={null}
			questionnaireLoading={false}
			scrapers={scrapers}
			isSubscribed={isSubscribed === 'true'}
		/>
	);
};
ThankYou.displayName = 'ThankYou';

export default ThankYou;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/dekujeme',
	[Locale.en]: '/thank-you',
});

export const getStaticProps: GetStaticProps<Props> = async () => {
	const { data } = await fetchScraperData();

	return {
		revalidate: ONE_DAY / 1000,
		props: { scrapers: data.scrapers },
	};
};

// Keep this for now, if needed for testing purposes
// const dummyFilterData = {
// 	// adType: 'Sale',
// 	adType: 'Rent',
// 	agent: [],
// 	area: {
// 		gte: 0,
// 		lte: null,
// 	},
// 	createdAt: '2023-01-10T12:53:54.342Z',
// 	disposition: [],
// 	email: '<EMAIL>',
// 	estimate: {
// 		range: 'Year',
// 		value: 0,
// 	},
// 	exactMatch: false,
// 	id: '23075c77d4ee719d2d349c2c0cbb3dda68fa41a6',
// 	houseType: [],
// 	landArea: {
// 		gte: 0,
// 		lte: null,
// 	},
// 	landType: [],
// 	lastNotificationAt: null,
// 	notificationsSent: 0,
// 	ownershipType: [],
// 	price: {
// 		gte: 0,
// 		lte: null,
// 	},
// 	radius: 0,
// 	realEstateType: 'Apartment',
// 	state: 'Active',
// 	terraceType: [],
// 	updatedAt: '2023-01-10T12:53:54.342Z',
// 	webhook: '',
// };
