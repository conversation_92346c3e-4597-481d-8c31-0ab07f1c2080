import { PageSeo } from 'components/meta/PageSeo';
import { ArticleAnnotation } from 'components/ui/blog/ArticleAnnotation';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import Image from 'next/image';
import { FormattedMessage, useIntl } from 'react-intl';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const OurStory: NextPage = () => {
	const intl = useIntl();

	const historyTitleFormattedMessage = intl.formatMessage({ description: 'historyPage-title', defaultMessage: '<PERSON><PERSON><PERSON> p<PERSON>' });
	const motivationFormattedMessage = intl.formatMessage({
		description: 'historyPage-motivation',
		defaultMessage: 'Zajímá vás proč to děláme nebo co bylo naš<PERSON> motivac<PERSON>?',
	});

	return (
		<>
			<PageSeo title={historyTitleFormattedMessage} />
			<Row lg>
				<ArticleAnnotation
					title={historyTitleFormattedMessage}
					imgSrc="/img/illust/aboutus.jpg"
					annot={motivationFormattedMessage}
				/>
			</Row>
			<Row>
				<div className="u-mb-lg">
					<h2>
						<FormattedMessage description={'historyPage-heading1'} defaultMessage={'Jak to všechno začalo'} />
					</h2>
					<p>
						<span className="img-right">
							<Image src="/img/illust/cut.jpg" alt="" quality="100" width="400" height="266" />
						</span>
						<FormattedMessage
							description={'historyPage-p1'}
							defaultMessage={
								'Už tomu bude pár let, co jsme (my kamarádi a znamí především z osobního života) začali pokukovat po nemovitostech, nejdříve spíše na investici, kdy se jednalo o různá menší pole nebo pozemky, následně vlastní bydlení a poté opět především menší investice.'
							}
						/>
					</p>
					<p>
						<FormattedMessage
							description={'historyPage-p2'}
							defaultMessage={
								'Vyhledávání vhodných nemovitostí bylo dosti časově náročné, což asi každý, kdo se o nemovitosti alespoň trošku zajímá, již poznal. Hlídací psi na jednotlivých realitních serverech práci sice zjednodušili, ale měli svá velká úskalí.'
							}
						/>
					</p>
					<h2>
						<FormattedMessage description={'historyPage-heading2'} defaultMessage={'Čas a zase čas'} />
					</h2>
					<p>
						<FormattedMessage
							description={'historyPage-p3'}
							defaultMessage={
								'Úskalí hlídacích psů jsme odhalili po několika uniklých nemovitostech, které, bohužel, opravdu stály za to. Jakým způsobem nám unikly? Přes veškeré úsilí a strávený čas jsme se o nich dozvěděli s několika-hodinovým zpožděním. Víte, že například byt k pronájmu získá prvního zájemce do <strong>23 minut</strong> a obvykle zmizí z trhu <strong>do sedmi hodin</strong>? Překvapující, že? My jsme to v té době také neviděli. Čím je nemovitost atraktivnější a má lepší poměr cena/výkon, tím rychleji mizí a je prodána již během prvních prohlídek. Pokud jdete na řadu druhý den, máte smůlu, jelikož nemovitost je již často rezervována.'
							}
							values={{
								strong: (...chunks: string[]) => <strong>{chunks}</strong>,
							}}
						/>
					</p>
					<h2>
						<FormattedMessage description={'historyPage-heading3'} defaultMessage={'Automatizace jako řešení'} />
					</h2>
					<p>
						<FormattedMessage
							description={'historyPage-p4'}
							defaultMessage={
								'Jednoho dne nás napadlo, jak na vyhledávání vyzrát a vyřešit zmíněné problémy současně. Někteří z nás pochází z technického oboru a automatizace a programování je pro ně zábavou i obživou. Začali jsme jednoduchým programem, který kontroloval vybrané portály a upozorňoval nás emailem na nové inzeráty. Jelikož emailového klienta jsme měli, jako již dnes hodně lidí, v telefonu, dozvěděli jsme se <strong>o nových nemovitostech na trhu doslova do pár minut</strong>. Postupně jsme přidávali nové funkce, další portály a celý program optimalizovali tak, abychom se o něj nemuseli starat. Fungovalo to skvěle a o nových inzerovaných nemovitostech jsme dozvídali mezi prvními a dokázali velice rychle reagovat. Občas byl sám prodejce překvapený, že jsme mu volali tak rychle.'
							}
							values={{
								strong: (...chunks: string[]) => <strong>{chunks}</strong>,
							}}
						/>
					</p>
					<h2>
						<FormattedMessage description={'historyPage-heading4'} defaultMessage={'Proč to děláme'} />
					</h2>
					<p>
						<FormattedMessage
							description={'historyPage-p5'}
							defaultMessage={
								'Pro přečtení výše popsaného příběhu Vás možná napadne otázka, když máme tuto konkurenční výhodu, proč se o ni chceme podělit s ostatními? Naší vizí je <strong>pomoci obyčejným lidem v boji s překupníky</strong>, kteří nemovitosti s dobrou cenovkou rychle koupí a následně draze prodají lidem hledajícím bydlení. Proto Vám chceme dát možnosti, které v současné době mají pouze profesionálové orientující se na realitním trhu a používající drahé nástroje. My již vlastní bydlení máme vyřešené a věříme, že se to díky našemu nástroji podaří také Vám.'
							}
							values={{
								strong: (...chunks: string[]) => <strong>{chunks}</strong>,
							}}
						/>
					</p>
					<br />
				</div>
			</Row>
			<CtaCommon />
		</>
	);
};

OurStory.displayName = 'OurStory';

export default OurStory;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/nas-pribeh',
	[Locale.en]: '/our-story',
});
