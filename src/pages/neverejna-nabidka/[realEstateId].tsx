import { fetchRealEstateGet } from 'api/realEstate/fetchRealEstateGet';
import { LayoutDetail } from 'components/ui/layout/LayoutDetail';
import { OfferDetailNonPublic } from 'components/ui/offer/OfferDetailNonPublic';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticPaths, GetStaticProps } from 'next';
import { useRouter } from 'next/router';
import { ReactElement } from 'react';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { NextPageWithLayout } from 'types/next';
import { Offer } from 'types/offer';
import { ONE_DAY } from 'utils/constants';
import { OfferNonPublicContactsBox } from 'components/ui/offer/OfferNonPublicContactsBox';
import { DetailPage } from 'components/ui/offer/DetailPage';

type Query = {
	realEstateId: string;
};

type Props = {
	realEstate?: Offer;
};

const RealEstateDetailNonPublic: NextPageWithLayout<Props> = ({ realEstate }) => {
	const router = useRouter();
	if (router.isFallback || !realEstate) return null;

	return (
		<DetailPage realEstate={realEstate} offerVariant="private">
			<OfferDetailNonPublic {...realEstate} />
			<OfferNonPublicContactsBox realEstate={realEstate} />
		</DetailPage>
	);
};

RealEstateDetailNonPublic.displayName = 'RealEstateDetailNonPublic';

export default RealEstateDetailNonPublic;

export const getStaticProps: GetStaticProps<Props, Query> = async ({ params }) => {
	if (!params) return { notFound: true };
	const { realEstateId } = params;
	if (!realEstateId) return { notFound: true };

	let realEstate: Offer;
	try {
		const { data } = await fetchRealEstateGet(realEstateId);
		realEstate = data.realEstate;
	} catch {
		return { notFound: true };
	}

	return {
		revalidate: ONE_DAY / 1000,
		props: { realEstate },
	};
};

RealEstateDetailNonPublic.getLayout = (page: ReactElement) => <LayoutDetail>{page}</LayoutDetail>;

export const getStaticPaths: GetStaticPaths = async () => {
	return {
		paths: [],
		fallback: true,
	};
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/neverejna-nabidka/[realEstateId]',
	[Locale.en]: '/non-public-offer/[realEstateId]',
});
