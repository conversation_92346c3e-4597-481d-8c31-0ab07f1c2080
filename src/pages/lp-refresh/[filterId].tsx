import { fetchFilterById } from 'api/filter/fetchFilterById';
import { PageSeo } from 'components/meta/PageSeo';
import { Row } from 'components/ui/core/Row';
import { Title } from 'components/ui/core/Title';
import { FilterBox } from 'components/ui/filter/FilterBox';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticPaths, GetStaticProps, NextPage } from 'next';
import { useRouter } from 'next/router';
import { FormattedMessage, useIntl } from 'react-intl';
import { Filter, FilterState } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { ONE_DAY } from 'utils/constants';

type Query = {
	filterId: string;
};

type Props = {
	filter: Filter;
};

const LpRefresh: NextPage<Props> = ({ filter }) => {
	const { formatMessage } = useIntl();
	const router = useRouter();
	return (
		<>
			<PageSeo
				title={formatMessage({
					defaultMessage: 'V<PERSON>š filtr jsme prodloužili!',
					description: 'lpRefresh-title',
				})}
			/>
			<Row>
				<Title tagName="h1" margin="sm" decorated>
					<strong>
						<FormattedMessage defaultMessage="Váš filtr " description="lpRefresh-yourFilter" />
					</strong>{' '}
					<FormattedMessage defaultMessage="jsme prodloužili" description="lpRefresh-isDeactivated" />!
				</Title>
				{/*<FilterSatisfactionCta />*/}
				<div className="u-mb-md">{!router.isFallback && <FilterBox {...filter} state={FilterState.ACTIVE} />}</div>
			</Row>
		</>
	);
};
LpRefresh.displayName = 'LpRefresh';

export default LpRefresh;

export const getStaticProps: GetStaticProps<Props, Query> = async ({ params }) => {
	if (!params) return { notFound: true };
	const { filterId } = params;
	if (!filterId) return { notFound: true };

	let filter: Filter;
	try {
		const { data } = await fetchFilterById(filterId);
		filter = data.filter;
	} catch {
		return { notFound: true };
	}

	return {
		revalidate: ONE_DAY / 1000,
		props: { filter },
	};
};

export const getStaticPaths: GetStaticPaths = async () => {
	return {
		paths: [],
		fallback: true,
	};
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/lp-refresh/[filterId]',
	[Locale.en]: '/lp-refresh/[filterId]',
});
