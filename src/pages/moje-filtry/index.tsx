import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { FilterDataContext } from 'components/search/FilterDataProvider';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Button } from 'components/ui/core/Button';
import { Message } from 'components/ui/core/Message';
import { Row } from 'components/ui/core/Row';
import { FilterBoard } from 'components/ui/filter/FilterBoard';
import { FilterBox } from 'components/ui/filter/FilterBox';
import { FilterFormDialog } from 'components/ui/filter/FilterFormDialog';
import { FilterSatisfactionCtaDialog } from 'components/ui/filter/FilterSatisfactionCtaDialog';
import { IsBlockedNotification } from 'components/user/IsBlockedNotification';
import { useUser } from 'components/user/UserProvider';
import { usePrivatePage } from 'hooks/usePrivatePage';
import { useUserFilterCount } from 'hooks/useUserFilterCount';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useContext, useEffect, useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Filter, FilterState } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { UserRole } from 'types/user';
import { filterQueryString } from 'utils/filterQueryString';

const MyFilters: NextPage = () => {
	const { filters } = useContext(FilterDataContext);
	const [editedFilter, setEditedFilter] = useState<Filter>();
	const [isDialogVisible, setDialogVisible] = useState(false);
	const router = useRouter();
	const intl = useIntl();
	const { isPayingUser, payload } = useUser();
	const filterToScrollToRef = useRef<HTMLDivElement>(null);
	const { filterId, addFilter, selectedFilterId } = router.query;
	const { active, max, isFull } = useUserFilterCount();
	const [ctaDialogOpen, setCtaDialogOpen] = useState<string>();

	const addFilterIntlString = intl.formatMessage({ description: 'myFilters-addFilter', defaultMessage: 'Přidat nový filtr' });
	const mySearchFiltersIntlString = intl.formatMessage({
		description: 'myFilters-mySearchFilters',
		defaultMessage: 'Moje filtry pro vyhledávání',
	});

	const openDialog = () => setDialogVisible(true);
	const closeDialog = () => {
		setDialogVisible(false);
		setEditedFilter(undefined);
	};

	const handleEdit = (filter: Filter) => {
		setEditedFilter(filter);
		openDialog();
	};

	usePrivatePage();

	useEffect(() => {
		if (typeof addFilter !== 'undefined') {
			setDialogVisible(true);
		}
	}, [addFilter]);

	useEffect(() => {
		if (!editedFilter && filterId && filters.length) {
			const newPath = filterQueryString(router.asPath, ['emailId', 'filterId']);
			if (router.asPath !== newPath) {
				router.replace(router.route, newPath);
			}

			const found = filters.find((filter) => filter.id === filterId);
			if (found) {
				setEditedFilter(found);
			}
		}
	}, [filters, filterId, editedFilter, router]);

	useEffect(() => {
		if (!filterToScrollToRef.current || filters.length === 0) return;
		filterToScrollToRef.current.scrollIntoView({ behavior: 'smooth' });
	}, [filterToScrollToRef, filters.length]);

	const renderFilters = (filters: Filter[], isLoading?: boolean) => (
		<>
			<div className="u-mb-sm">
				{payload.role == UserRole.USER ? (
					<Message icon="warning" variant={'error'} noclose iconMiddle>
						<span className="grid grid--y-xs grid--middle grid--space-between">
							<span className="grid__cell size--auto">
								<strong>Nemáte aktivní žádný tarif</strong>
							</span>
							<span className="grid__cell size--auto">
								<Button href="/premium-ucet">Aktivujte si některý z tarifů</Button>
							</span>
						</span>
					</Message>
				) : (
					<Message
						icon={active < max ? 'filter' : 'warning'}
						variant={active < max ? 'ok' : active > max ? 'error' : 'warning'}
						noclose
						iconMiddle={max !== Infinity ? false : true}
					>
						<span className="grid grid--y-xs grid--middle grid--space-between">
							<span className="grid__cell size--auto">
								<strong>
									Máte {active}{' '}
									{active < 5 && active > 0 ? (active < 2 ? 'aktivní filtr' : 'aktivní filtry') : 'aktivních filtrů'}.
								</strong>
								<br />
								{max !== Infinity &&
									`Ve vašem tarifu můžete mít ${max} ${
										max < 5 && max > 0 ? (max < 2 ? 'aktivní filtr' : 'aktivní filtry') : 'aktivních filtrů'
									}.`}
							</span>
							<span className="grid__cell size--auto">
								<Button iconBefore="plus" text={addFilterIntlString} onClick={openDialog} disabled={isFull} />
							</span>
						</span>
					</Message>
				)}
			</div>

			{filters.length === 0 && !isLoading && (
				<Message icon="notification">
					<FormattedMessage description={'myFilters-noCategoryFilters'} defaultMessage={'Kategorie neobsahuje žádné filtry'} />
				</Message>
			)}
			{!isPayingUser && filters.filter((filter) => filter.state === FilterState.ACTIVE).length >= 2 && (
				<Message icon="warning" variant="warning">
					Dosáhli jste limitu pro množství filtrů zdarma, aktivujte si <Link href="/premium-ucet">Premium účet</Link> pro
					neomezené množství filtrů.
				</Message>
			)}
			{filters.map((filter) => (
				<div className="c-products__item" key={filter.id} ref={filter.id === selectedFilterId ? filterToScrollToRef : null}>
					<FilterBox {...filter} onFilterEdit={handleEdit} setCtaDialogOpen={setCtaDialogOpen} />
				</div>
			))}
		</>
	);

	return (
		<>
			<PageSeo title={mySearchFiltersIntlString} />
			<Row lg>
				<ArticleIntro
					title={
						<>
							<FormattedMessage
								description={'myFilters-mySearchFiltersTitle'}
								defaultMessage={'<strong>Moje filtry</strong> pro vyhledávání'}
								values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
							/>
						</>
					}
					margin="sm"
				/>
			</Row>

			<IsBlockedNotification />

			<Row>
				<FilterBoard renderFilters={renderFilters} />
			</Row>
			<CtaCommon />
			<FilterFormDialog filterValues={editedFilter} close={closeDialog} isOpen={isDialogVisible} />
			<FilterSatisfactionCtaDialog
				isOpen={typeof ctaDialogOpen === 'string'}
				close={() => setCtaDialogOpen(undefined)}
				filterId={ctaDialogOpen ?? ''}
			/>
		</>
	);
};

MyFilters.displayName = 'MyFilters';

export default MyFilters;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/moje-filtry',
	[Locale.en]: '/my-filters',
});
