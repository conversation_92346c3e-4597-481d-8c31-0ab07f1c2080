import { fetchFilterById } from 'api/filter/fetchFilterById';
import { fetchFilterOffers } from 'api/filter/fetchFilterOffers';
import { PageSeo } from 'components/meta/PageSeo';
import { BlockLoader } from 'components/ui/core/BlockLoader';
import { Message } from 'components/ui/core/Message';
import { NextPrev } from 'components/ui/core/NextPrev';
import { Row } from 'components/ui/core/Row';
import { Title } from 'components/ui/core/Title';
import { FilterBox } from 'components/ui/filter/FilterBox';
import { OfferBox } from 'components/ui/offer/OfferBox';
import { useNextPrev } from 'hooks/useNextPrev';
import { usePrivatePage } from 'hooks/usePrivatePage';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticPaths, GetStaticProps, NextPage } from 'next';
import { useCallback, useEffect, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Filter } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { Offer } from 'types/offer';
import { ONE_DAY } from 'utils/constants';

type Query = {
	filterId: string;
};

type Props = {
	filter?: Filter;
};

const MyFiltersDetail: NextPage<Props> = ({ filter }) => {
	usePrivatePage();
	const { formatMessage } = useIntl();
	const [realEstatesLoading, setRealEstatesLoading] = useState(false);
	const [realEstates, setRealEstates] = useState<Offer[]>([]);
	const [page, onPageChange] = useNextPrev();

	const fetchRealEstates = useCallback(async () => {
		if (filter?.id) {
			try {
				const response = await fetchFilterOffers(filter.id, page);
				setRealEstates(response.data.realEstates);
			} catch (e) {
				console.error(e);
			}
			setRealEstatesLoading(false);
		}
	}, [filter?.id, page]);

	useEffect(() => {
		setRealEstatesLoading(true);
		fetchRealEstates();
	}, [fetchRealEstates]);

	if (!filter) return null;

	return (
		<>
			<PageSeo
				title={formatMessage({
					description: 'myFiltersDetail-title',
					defaultMessage: 'Detail filtru',
				})}
			/>
			<Row>
				<div className="u-mb-md">
					<FilterBox {...filter} isFilterDetailPage />
				</div>
			</Row>
			<Row>
				<BlockLoader loading={realEstatesLoading}>
					{realEstates.length > 0 ? (
						<section className="c-crossroad u-mb-md">
							<Title tagName="h2" margin="sm" align="center">
								<FormattedMessage
									description={'thankPage-lastOffers'}
									defaultMessage={'Poslední nalezené nabídky pro tento filtr'}
								/>
							</Title>
							<div className="c-crossroad__list grid u-mb-xs">
								{realEstates.map((realEstate) => (
									<div
										className="c-crossroad__item grid__cell grid__cell--eq size--6-12@sm size--4-12@md"
										key={realEstate.id}
									>
										<OfferBox offer={realEstate} />
									</div>
								))}
							</div>
							<NextPrev page={page} onChange={onPageChange} showNext={realEstates.length >= 21} />
						</section>
					) : (
						<Message icon="notification">Žádné záznamy</Message>
					)}
				</BlockLoader>
			</Row>
		</>
	);
};

export default MyFiltersDetail;

export const getStaticProps: GetStaticProps<Props, Query> = async ({ params }) => {
	if (!params) return { notFound: true };
	const { filterId } = params;
	if (!filterId) return { notFound: true };

	let filter: Filter;
	try {
		const { data } = await fetchFilterById(filterId);
		filter = data.filter;
	} catch {
		return { notFound: true };
	}

	return {
		revalidate: ONE_DAY / 1000,
		props: { filter },
	};
};

export const getStaticPaths: GetStaticPaths = async () => {
	return {
		paths: [],
		fallback: true,
	};
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/moje-filtry/[filterId]',
	[Locale.en]: '/my-filters/[filterId]',
});
