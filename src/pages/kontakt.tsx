import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { Support } from 'components/ui/boxes/Support';
import { Row } from 'components/ui/core/Row';
import { FooterSeparator } from 'components/ui/FooterSeparator';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import Image from 'next/image';
import { FormattedMessage, useIntl } from 'react-intl';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const PersonalInformation: NextPage = () => {
	const intl = useIntl();

	const contactsTitleIntlString = intl.formatMessage({ description: 'contactsPage-title', defaultMessage: 'Kontakt' });
	const niceToMeetIntlString = intl.formatMessage({ description: 'contactsPage-niceToMeet', defaultMessage: 'Rádi vás poznáváme' });

	return (
		<>
			<PageSeo title={contactsTitleIntlString} />
			<Row>
				<ArticleIntro title={niceToMeetIntlString} bg margin="sm">
					<p className="caption">
						<FormattedMessage
							description={'contactsPage-needAdvice'}
							defaultMessage={'Potřebujete poradit s nastavením filtrů, administrací nebo s námi chcete spolupracovat?'}
						/>
					</p>
				</ArticleIntro>
				<div className="u-mb-xl u-text-center">
					<h2 className="title title--semicircle u-mb-sm">
						<FormattedMessage description={'contactsPage-support'} defaultMessage={'Podpora'} />
					</h2>
					<p className="u-font-lg u-mb-xs">
						<FormattedMessage description={'contactsPage-checkFAQ'} defaultMessage={'Většinu odpovědí najdete nejrychleji v'} />{' '}
						<Link href="/casto-kladene-otazky">
							<FormattedMessage description={'contactsPage-FAQ'} defaultMessage={'často kladených dotazech'} />
						</Link>
						.
					</p>
					<Support />
				</div>
				<div className="u-mb-xl u-text-center">
					<h2 className="title title--semicircle u-mb-sm">
						<FormattedMessage description={'contactsPage-workWithUs'} defaultMessage={'Spolupracujte s námi'} />
					</h2>
					<p className="u-font-lg u-mb-xs">
						<FormattedMessage
							description={'contactsPage-mission'}
							defaultMessage={'Jsme Visidoo, pomáháme lidem s hledáním bydlení.'}
						/>
					</p>
					<p>
						<FormattedMessage
							description={'contactsPage-cooperation'}
							defaultMessage={
								'Náš nástroj rádi rozšíříme o nové realitní portály či navazující služby. {br}Myslíte, že můžeme pomáhat spolu, tak nám napište na <a><EMAIL></a>'
							}
							values={{
								br: <br />,
								a: (...chunks: string[]) => <a href="mailto:<EMAIL>">{chunks}</a>,
							}}
						/>
					</p>
				</div>
				<div className="u-mb-lg u-text-center">
					<div className="grid grid--center">
						<div className="grid__cell size--5-12@md">
							<div className="u-mb-xs">
								<Image
									src="/img/illust/budova.jpg"
									alt="Budova PI2"
									quality="100"
									layout="fixed"
									width="300"
									height="345"
									priority
								/>
							</div>
							<p className="h5">Visidoo s.r.o.</p>
							<p>
								Hněvkovského 30/65,
								<br />
								617 00 Brno
							</p>
							<p>
								E-mail: <a href="mailto:<EMAIL>"><EMAIL></a>
							</p>
							<p>IČ: 14340119 • DIČ: CZ14340119</p>
						</div>
					</div>
				</div>
			</Row>

			<FooterSeparator />
		</>
	);
};
PersonalInformation.displayName = 'PersonalInformation';

export default PersonalInformation;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/kontakt',
	[Locale.en]: '/contact-us',
});
