import { fetchFilterById } from 'api/filter/fetchFilterById';
import { PageSeo } from 'components/meta/PageSeo';
import { Row } from 'components/ui/core/Row';
import { FilterBox } from 'components/ui/filter/FilterBox';
import { FilterSatisfactionCta } from 'components/ui/filter/FilterSatisfactionCta';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticPaths, GetStaticProps, NextPage } from 'next';
import { useRouter } from 'next/router';
import { FormattedMessage, useIntl } from 'react-intl';
import { Filter, FilterState } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { ONE_DAY } from 'utils/constants';

type Query = {
	filterId: string;
};

type Props = {
	filter: Filter;
};

const LpUnsubscribe: NextPage<Props> = ({ filter }) => {
	const { formatMessage } = useIntl();
	const router = useRouter();

	return (
		<>
			<PageSeo
				title={formatMessage({
					defaultMessage: 'Váš filtr jsme deaktivovali!',
					description: 'lpUnsubscribe-title',
				})}
			/>
			<Row>
				<section className="u-mb-md">
					<p className="message message--ok message--noclose u-mb-md">
						<span className="message__icon">
							<span className="icon-svg">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="12.781"
									viewBox="0 0 16 12.781"
									className="icon-svg__svg"
								>
									<path
										d="M233,104.318l2.113-2.145,3.4,3.443,8.377-8.5L249,99.263,238.508,109.9,233,104.318h0Z"
										transform="translate(-233 -97.125)"
									/>
								</svg>
							</span>
						</span>
						<span>
							<strong>
								<FormattedMessage defaultMessage="Váš filtr " description="lpUnsubscribe-yourFilter" />
							</strong>{' '}
							<FormattedMessage defaultMessage="jsme deaktivovali" description="lpUnsubscribe-isDeactivated" />!
						</span>
					</p>
				</section>
			</Row>

			<Row>
				<FilterSatisfactionCta />
				<div className="u-mb-md">{!router.isFallback && <FilterBox {...filter} state={FilterState.DISABLED} />}</div>
			</Row>
		</>
	);
};
LpUnsubscribe.displayName = 'LpUnsubscribe';

export default LpUnsubscribe;

export const getStaticProps: GetStaticProps<Props, Query> = async ({ params }) => {
	if (!params) return { notFound: true };
	const { filterId } = params;
	if (!filterId) return { notFound: true };

	let filter: Filter;
	try {
		const { data } = await fetchFilterById(filterId);
		filter = data.filter;
	} catch {
		return { notFound: true };
	}

	return {
		revalidate: ONE_DAY / 1000,
		props: { filter },
	};
};

export const getStaticPaths: GetStaticPaths = async () => {
	return {
		paths: [],
		fallback: true,
	};
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/lp-unsubscribe/[filterId]',
	[Locale.en]: '/lp-unsubscribe/[filterId]',
});
