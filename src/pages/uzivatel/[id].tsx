import { PageSeo } from 'components/meta/PageSeo';
import { Row } from 'components/ui/core/Row';
import { SimpleUserDetail } from 'components/user/SimpleUserDetail';
import { useAdvancedPage } from 'hooks/useAdvancedPage';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const AdministrationUser: NextPage = () => {
	const id = useRouter().query.id;

	useAdvancedPage();

	return (
		<>
			<PageSeo title="Detail uživatele | Administrace" />
			<Row>{id && <SimpleUserDetail id={Array.isArray(id) ? id[0] : id} />}</Row>
		</>
	);
};

AdministrationUser.displayName = 'AdministrationUser';

export default AdministrationUser;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/uzivatel/[id]',
	[Locale.en]: '/user/[id]',
});
