import { Link } from 'components/Link';
import { PageSeo } from 'components/meta/PageSeo';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Row } from 'components/ui/core/Row';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { FormattedMessage, useIntl } from 'react-intl';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { NO_LOCALE_VERSION_MESSAGE } from 'utils/constants';

const HowItWorks: NextPage = () => {
	const router = useRouter();
	const intl = useIntl();

	const { query } = router;
	const isLocale404 = query?.r === NO_LOCALE_VERSION_MESSAGE;

	const pageNotFoundIntlString = intl.formatMessage({ description: 'errorPage-pageNotFound', defaultMessage: '<PERSON>ránka nenalezena' });

	return (
		<>
			<PageSeo title={pageNotFoundIntlString} />
			<Row>
				<h1 className="title title--semicircle u-mb-lg">{pageNotFoundIntlString}</h1>
				<div className="u-mb-lg">
					<div className="c-workflow__inner">
						<div className="c-workflow__img-wrapper">
							<div className="c-workflow__img">
								<Image src="/img/illust/404.jpg" alt="" width="300" height="350" layout="fixed" quality="100" priority />
							</div>
						</div>
						<div className="c-workflow__content u-mb-last-0">
							<h2 className="c-workflow__title h3">
								{isLocale404 ? (
									<FormattedMessage
										description={'errorPage-noLocaleVersion'}
										defaultMessage={'Tato stránka není dostupná ve zvoleném jazyce'}
									/>
								) : (
									<FormattedMessage
										description={'errorPage-noProperty'}
										defaultMessage={'Zde na vás žádná nemovitost nečeká'}
									/>
								)}
							</h2>
							<p className="c-workflow__annot">
								<FormattedMessage
									description={'errorPage-wrongAddress'}
									defaultMessage={
										'Pravděpodobně jste našli zastaralý odkaz nebo zadali špatnou adresu. Žádný strach, stačí se vrátit'
									}
								/>{' '}
								<Link href="/">
									<FormattedMessage description={'errorPage-toHome'} defaultMessage={'na úvodní stránku'} />
								</Link>{' '}
								<FormattedMessage
									description={'errorPage-topNavigation'}
									defaultMessage={'nebo můžete to zkusit použít horní navigaci.'}
								/>
							</p>
						</div>
					</div>
				</div>
			</Row>
			<CtaCommon />
		</>
	);
};
HowItWorks.displayName = 'HowItWorks';

export default HowItWorks;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/404',
	[Locale.en]: '/404',
});
