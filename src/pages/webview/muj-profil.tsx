import { BridgeProvider } from 'components/webview/BridgeProvider';
import { BridgeLoader } from 'components/webview/BridgeLoader';
import { Row } from 'components/ui/core/Row';
import { WebviewLayout } from 'components/ui/layout/WebviewLayout';
import { ReactElement } from 'react';
import { UserEditForm } from 'components/user/UserEditForm';
import { UserDeleteForm } from 'components/user/UserDeleteForm';
import { NextPageWithLayout } from 'types/next';
import { Locale } from 'i18n/supportedLocales';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const WebviewMyProfile: NextPageWithLayout = () => {
	return (
		<BridgeProvider>
			<BridgeLoader>
				<Row>
					<UserEditForm />
					<UserDeleteForm />
				</Row>
			</BridgeLoader>
		</BridgeProvider>
	);
};

WebviewMyProfile.displayName = 'WebviewMyProfile';

WebviewMyProfile.getLayout = (page: ReactElement) => <WebviewLayout>{page}</WebviewLayout>;

export default WebviewMyProfile;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/webview/muj-profil',
	[Locale.en]: '/webview/my-profile',
});
