import { BridgeProvider, useBridge } from 'components/webview/BridgeProvider';
import { BridgeLoader } from 'components/webview/BridgeLoader';
import { Row } from 'components/ui/core/Row';
import { FilterForm } from 'components/ui/filter/FilterForm';
import { WebviewLayout } from 'components/ui/layout/WebviewLayout';
import { FC, ReactElement } from 'react';
import { NextPageWithLayout } from 'types/next';
import { Locale } from 'i18n/supportedLocales';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const WebviewNewFilter: NextPageWithLayout = () => {
	return (
		<BridgeProvider>
			<BridgeLoader>
				<Row>
					<WebviewNewFilterOperator />
				</Row>
			</BridgeLoader>
		</BridgeProvider>
	);
};

WebviewNewFilter.displayName = 'WebviewNewFilter';

WebviewNewFilter.getLayout = (page: ReactElement) => <WebviewLayout>{page}</WebviewLayout>;

export default WebviewNewFilter;

const WebviewNewFilterOperator: FC = () => {
	const { postMessage } = useBridge();

	return <FilterForm filterValues={{}} onFilterAdded={() => postMessage('ADDFILTER_DONE')} />;
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/webview/novy-filtr',
	[Locale.en]: '/webview/new-filter',
});
