import { Message } from 'components/ui/core/Message';
import { Row } from 'components/ui/core/Row';
import { FilterBoard } from 'components/ui/filter/FilterBoard';
import { FilterBox } from 'components/ui/filter/FilterBox';
import { FilterFormDialog } from 'components/ui/filter/FilterFormDialog';
import { WebviewLayout } from 'components/ui/layout/WebviewLayout';
import { BridgeLoader } from 'components/webview/BridgeLoader';
import { BridgeProvider } from 'components/webview/BridgeProvider';
import { Locale } from 'i18n/supportedLocales';
import { ReactElement, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { Filter } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { NextPageWithLayout } from 'types/next';

const WebviewMyFilters: NextPageWithLayout = () => {
	const [editedFilter, setEditedFilter] = useState<Filter>();

	const renderFilters = (filters: Filter[], isLoading?: boolean) => (
		<>
			{filters.length === 0 && !isLoading && (
				<Message icon="notification">
					<FormattedMessage description={'myFilters-noCategoryFilters'} defaultMessage={'Kategorie neobsahuje žádné filtry'} />
				</Message>
			)}
			{filters.map((filter) => (
				<div className="c-products__item" key={filter.id}>
					<FilterBox {...filter} onFilterEdit={setEditedFilter} />
				</div>
			))}
		</>
	);

	return (
		<BridgeProvider>
			<BridgeLoader>
				<Row>
					<FilterBoard renderFilters={renderFilters} />
				</Row>
				<FilterFormDialog filterValues={editedFilter} close={() => setEditedFilter(undefined)} isOpen={!!editedFilter} />
			</BridgeLoader>
		</BridgeProvider>
	);
};

WebviewMyFilters.displayName = 'WebviewNewFilter';

WebviewMyFilters.getLayout = (page: ReactElement) => <WebviewLayout>{page}</WebviewLayout>;

export default WebviewMyFilters;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/webview/moje-filtry',
	[Locale.en]: '/webview/my-filters',
});
