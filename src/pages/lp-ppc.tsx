import { fetchScraperData } from 'api/realEstate/fetchScraperData';
import { PageSeo } from 'components/meta/PageSeo';
import { BlockquoteSreality } from 'components/ui/boxes/BlockquoteSreality';
import { BlockquoteWatchdog } from 'components/ui/boxes/BlockquoteWatchdog';
import { CtaLandingPage } from 'components/ui/boxes/CtaLandingPage';
import { LandingPageHighlights } from 'components/ui/boxes/LandingPageHighlights';
import { Row } from 'components/ui/core/Row';
import { FilterForm } from 'components/ui/filter/FilterForm';
import { Locale } from 'i18n/supportedLocales';
import { decode } from 'js-base64';
import { GetStaticProps, NextPage } from 'next';
import { useRouter } from 'next/router';
import { useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Filter } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { ONE_DAY } from 'utils/constants';

type Props = {
	count: number;
};

const LpPpc: NextPage<Props> = ({ count }) => {
	const { formatMessage } = useIntl();
	const { filter } = useRouter().query;

	const filterValues = useMemo<Filter | undefined>(() => {
		if (!filter) return;
		return JSON.parse(decode(Array.isArray(filter) ? filter[0] : filter));
	}, [filter]);

	return (
		<>
			<PageSeo
				title={formatMessage({
					defaultMessage: 'Nenechte si ujít ty nejzajímavější nemovitosti',
					description: 'lpPpc-meta-title',
				})}
				description={formatMessage({
					defaultMessage: 'Nové nabídky z realitních serverů do minuty ve vašem e-mailu',
					description: 'lpPpc-meta-description',
				})}
			/>
			<Row lg>
				<div className="mother__bg">
					<header className="b-intro b-intro--full b-intro--lp u-mb-md u-mb-last-0">
						<h1 className="title h1 b-intro__title">
							<FormattedMessage
								defaultMessage="Nenechte si ujít ty <strong>nejzajímavější {br}nemovitosti</strong>"
								description="lpPpc-title"
								values={{
									strong: (...chunks: string[]) => <strong>{chunks}</strong>,
									br: <br className="u-hide@lgDown" />,
								}}
							/>
						</h1>
					</header>
					<div className="grid grid--y-0">
						<div className="grid__cell size--5-12@lg order--2@lg">
							<div className="f-filter f-filter--lp u-mb-md">
								<div className="f-filter__inner">
									<h2 className="h5 u-mb-xs">
										<FormattedMessage
											defaultMessage={'Nové nabídky z realitních serverů {br}do\u00a0minuty ve vašem e\u2011mailu'}
											description="lpPpc-filterTitle"
											values={{
												br: <br className="u-hide@lgDown" />,
											}}
										/>
									</h2>
									{typeof filterValues !== 'undefined' ? (
										<div>
											<FilterForm filterValues={filterValues} />
										</div>
									) : (
										<FilterForm />
									)}
								</div>
							</div>
						</div>
						<div className="grid__cell size--6-12@md size--3-5-12@lg order--1@lg">
							<BlockquoteWatchdog />
						</div>
						<div className="grid__cell size--6-12@md size--3-5-12@lg order--3@lg">
							<BlockquoteSreality />
						</div>
					</div>
				</div>

				<LandingPageHighlights count={count} />
			</Row>

			<CtaLandingPage />
		</>
	);
};

LpPpc.displayName = 'LpPpc';

export default LpPpc;

export const getStaticProps: GetStaticProps<Props> = async () => {
	const { data } = await fetchScraperData();

	return {
		revalidate: ONE_DAY / 1000,
		props: { scrapers: data.scrapers, count: data.count },
	};
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/lp-ppc',
	[Locale.en]: '/lp-ppc',
});
