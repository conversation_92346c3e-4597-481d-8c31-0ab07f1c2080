import { fetchRealEstateGet } from 'api/realEstate/fetchRealEstateGet';
import { fetchScraperData } from 'api/realEstate/fetchScraperData';
import { PageSeo } from 'components/meta/PageSeo';
import { BlockquoteSreality } from 'components/ui/boxes/BlockquoteSreality';
import { BlockquoteWatchdog } from 'components/ui/boxes/BlockquoteWatchdog';
import { CtaLandingPage } from 'components/ui/boxes/CtaLandingPage';
import { LandingPageHighlights } from 'components/ui/boxes/LandingPageHighlights';
// import { Icon } from 'components/ui/core/Icon';
import { Row } from 'components/ui/core/Row';
import { FilterForm } from 'components/ui/filter/FilterForm';
import { RealEstateLocation } from 'components/ui/filter/RealEstateLocation';
import { OfferPrice } from 'components/ui/offer/OfferPrice';
import { OfferPriceM2 } from 'components/ui/offer/OfferPriceM2';
import { OfferTitle } from 'components/ui/offer/OfferTitle';
import { Locale } from 'i18n/supportedLocales';
// import { useShortLocale } from 'i18n/useShortLocale';
import { GetStaticPaths, GetStaticProps, NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { RealEstateType } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { Offer, OfferType } from 'types/offer';
import { trackFacebookLandingPage } from 'utils/analytics';
import { ONE_DAY } from 'utils/constants';
// import { getRelativeTimeString } from 'utils/getRelativeTimeString';

type Query = {
	realEstateId: string;
};

type Props = {
	scrapers: {
		name: string;
		url: string;
	}[];
	count: number;
	realEstate?: Offer;
};

const LpFacebook: NextPage<Props> = ({ count, realEstate }) => {
	const { formatMessage } = useIntl();
	const router = useRouter();
	// const shortLocale = useShortLocale();

	useEffect(() => {
		if (!router.isFallback && realEstate) {
			trackFacebookLandingPage(realEstate.id, realEstate.price);
		}
	}, [realEstate, router.isFallback]);

	if (router.isFallback || !realEstate) return null;

	const filterValues = {
		adType: realEstate.adType,
		realEstateType: realEstate.realEstateType,
		// locationId: realEstate.location?.id,
	};

	return (
		<>
			<PageSeo
				title={formatMessage({
					defaultMessage: 'Nenechte si ujít další podobné nemovitosti jako první',
					description: 'lpFacebook-title',
				})}
				description={formatMessage({
					defaultMessage: 'Nové nabídky z realitních serverů do minuty ve vašem e-mailu',
					description: 'lpFacebook-description',
				})}
			/>
			<div className="row-main">
				<div className="b-product-lg u-mb-md">
					<div className="b-product-lg__img">
						<a href={realEstate.url} target="_blank" rel="noopener noreferrer">
							<img src={realEstate.imageUrl} loading="lazy" decoding="async" alt="" width="350" height="250" />
						</a>
					</div>
					<div className="b-product-lg__content">
						<h1 className="b-product-lg__title h5">
							<OfferTitle {...realEstate} />
						</h1>
						<p className="b-product-lg__address">
							<RealEstateLocation location={realEstate.location} />
						</p>
						<p className="b-product-lg__details">
							<OfferPrice {...realEstate} />
							{realEstate.adType === OfferType.SALE &&
								(realEstate.realEstateType === RealEstateType.APARTMENT ||
									realEstate.realEstateType === RealEstateType.LAND) && <OfferPriceM2 {...realEstate} />}
						</p>
						{/* <div className="grid grid--y-0 grid--middle grid--space-between">
							<div className="grid__cell size--auto@mdUp">
								<p>
									<a href={realEstate.url} className="btn" target="_blank" rel="noopener noreferrer">
										<span className="btn__text">
											<FormattedMessage
												defaultMessage="Přejít na stránky prodejce"
												description="lpFacebook-GoToSeller"
											/>
										</span>
									</a>
								</p>
							</div>
							<div className="grid__cell size--auto@mdUp u-hide@mdDown">
								<p className="u-color-grey u-font-sm">
									<FormattedMessage defaultMessage="Nalezeno" description="lpFacebook-found" />{' '}
									{getRelativeTimeString(realEstate.createdAt, shortLocale)}{' '}
									<FormattedMessage defaultMessage="na" description="lpFacebook-foundAt" />{' '}
									<span className="flag flag--grey u-align-middle">
										<span className="flag__inner">
											<span className="flag__icon">
												<Icon name="pin-2" />
											</span>
											<span className="flag__text">{realEstate.origin}</span>
										</span>
									</span>
									.
								</p>
							</div>
						</div> */}
					</div>
				</div>
			</div>
			<Row lg>
				<div className="mother__bg">
					<header className="b-intro b-intro--full b-intro--lp u-mb-md u-mb-last-0">
						<h1 className="title h1 b-intro__title">
							<FormattedMessage
								defaultMessage="Nenechte si ujít <strong>další podobné {br}nemovitosti</strong>"
								description="lpPpc-title"
								values={{
									strong: (...chunks: string[]) => <strong>{chunks}</strong>,
									br: <br className="u-hide@lgDown" />,
								}}
							/>
						</h1>
					</header>
					<div className="grid grid--y-0">
						<div className="grid__cell size--5-12@lg order--2@lg">
							<div className="f-filter f-filter--lp u-mb-md">
								<div className="f-filter__inner">
									<h2 className="h5 u-mb-xs u-hide@mdDown">
										<FormattedMessage
											defaultMessage={'Nové nabídky z realitních serverů {br}do\u00a0minuty ve vašem e\u2011mailu'}
											description="lpFacebook-filterTitle"
											values={{
												br: <br className="u-hide@lgDown" />,
											}}
										/>
									</h2>
									<FilterForm filterValues={filterValues} />
								</div>
							</div>
						</div>
						<div className="grid__cell size--6-12@md size--3-5-12@lg order--1@lg">
							<BlockquoteWatchdog />
						</div>
						<div className="grid__cell size--6-12@md size--3-5-12@lg order--3@lg">
							<BlockquoteSreality />
						</div>
					</div>
				</div>

				<LandingPageHighlights count={count} />
			</Row>

			<CtaLandingPage />
		</>
	);
};

LpFacebook.displayName = 'LpFacebook';

export default LpFacebook;

export const getStaticProps: GetStaticProps<Props, Query> = async ({ params }) => {
	if (!params) return { notFound: true };
	const { realEstateId } = params;
	if (!realEstateId) return { notFound: true };

	let realEstate: Offer;
	try {
		const { data } = await fetchRealEstateGet(realEstateId);
		realEstate = data.realEstate;
	} catch {
		return { notFound: true };
	}

	const {
		data: { scrapers, count },
	} = await fetchScraperData();

	return {
		revalidate: ONE_DAY / 1000,
		props: { scrapers, count, realEstate },
	};
};

export const getStaticPaths: GetStaticPaths = async () => {
	return {
		paths: [],
		fallback: true,
	};
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/lp-fb/[realEstateId]',
	[Locale.en]: '/lp-fb/[realEstateId]',
});
