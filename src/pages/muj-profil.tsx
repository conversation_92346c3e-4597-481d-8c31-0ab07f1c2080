import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Row } from 'components/ui/core/Row';
import { ApiKeyHighlight } from 'components/user/ApiKeyHighlight';
import { IsBlockedNotification } from 'components/user/IsBlockedNotification';
import { UserDeleteForm } from 'components/user/UserDeleteForm';
import { UserEditForm } from 'components/user/UserEditForm';
import { useUser } from 'components/user/UserProvider';
import { usePrivatePage } from 'hooks/usePrivatePage';
import { Locale } from 'i18n/supportedLocales';
import { NextPage } from 'next';
import { FormattedMessage, useIntl } from 'react-intl';
import { GetLocaleUrls } from 'types/getLocaleUrls';

const MyProfile: NextPage = () => {
	usePrivatePage();
	const user = useUser();
	const intl = useIntl();
	const myProfileIntlString = intl.formatMessage({ description: 'myProfile-seoTitle', defaultMessage: 'Můj profil' });

	return (
		<>
			<PageSeo title={myProfileIntlString} />
			<Row lg>
				<ArticleIntro
					title={
						<>
							<FormattedMessage
								description={'myProfile-pageTitle'}
								defaultMessage={'<strong>Můj</strong> profil'}
								values={{ strong: (...chunks: string[]) => <strong>{chunks}</strong> }}
							/>
						</>
					}
					margin="sm"
				/>
			</Row>

			<IsBlockedNotification />

			<Row>
				<UserEditForm />
			</Row>

			{user.payload.apiAccess && user.payload.apiAccess.length > 0 && (
				<Row>
					<ApiKeyHighlight />
				</Row>
			)}

			<Row>
				<UserDeleteForm />
			</Row>

			<CtaCommon />
		</>
	);
};

MyProfile.displayName = 'MyProfile';

export default MyProfile;

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/muj-profil',
	[Locale.en]: '/my-profile',
});
