import { fetchUserOffers } from 'api/user/fetchUserOffers';
import clsx from 'clsx';
import { PageSeo } from 'components/meta/PageSeo';
import { PrincipleAnimation } from 'components/ui/animation/PrincipleAnimation';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Icon } from 'components/ui/core/Icon';
import { Row } from 'components/ui/core/Row';
import { Title } from 'components/ui/core/Title';
import { Workflow } from 'components/ui/crossroad/Workflow';
import { FilterForm } from 'components/ui/filter/FilterForm';
import { OfferBox } from 'components/ui/offer/OfferBox';
import { useHomePageNotifications } from 'hooks/useHomepageNotifications';
import { Locale } from 'i18n/supportedLocales';
import { decode } from 'js-base64';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Filter } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { Offer } from 'types/offer';

const Index: NextPage = () => {
	const { filter } = useRouter().query;

	const filterValues = useMemo<Filter | undefined>(() => {
		if (!filter) return;
		return JSON.parse(decode(Array.isArray(filter) ? filter[0] : filter));
	}, [filter]);
	const [lastOffers, setLastOffers] = useState<Offer[]>([]);
	const { formatMessage } = useIntl();

	useHomePageNotifications();

	useEffect(() => {
		fetchUserOffers().then((result) => {
			setLastOffers(result.data.realEstates);
		});
	}, [setLastOffers]);

	return (
		<>
			<PageSeo
				title={formatMessage({ defaultMessage: 'Hlídací pes pro realitní inzeráty', description: 'homepage-SEOTitle' })}
				description={formatMessage({
					defaultMessage: 'Do 1 minuty pošleme nejnovější inzerát e-mailem',
					description: 'homepage-SEODescription',
				})}
			/>

			<section className={clsx('b-annot')}>
				<div className="b-annot__bg" />
				<div className="row-main-lg">
					<div className="b-annot__inner">
						<div className="b-annot__title">
							<div className="b-intro u-mb-sm u-mb-last-0">
								<h1 className="title h1 b-intro__title">
									<FormattedMessage
										description={'homepage-title'}
										defaultMessage={'<strong>Hlídací pes</strong> pro <strong>realitní inzeráty</strong>'}
										values={{
											strong: (...chunks: string[]) => <strong>{chunks}</strong>,
										}}
									/>
								</h1>
								<div className="b-intro__caption">
									<p className="caption">
										Hlídáme za vás realitní nabídky a okamžitě pošleme upozornění e&#8209;mailem.
										<br /> Nastavte si vašeho realitního hlídacího psa:
									</p>
								</div>
							</div>
						</div>
						<div className="b-annot__animation">
							<PrincipleAnimation />
						</div>
						<div className="b-annot__form">
							<div className="f-filter f-filter--hp">
								<div className="f-filter__inner">
									{typeof filterValues !== 'undefined' ? (
										<div>
											<FilterForm filterValues={filterValues} />
										</div>
									) : (
										<FilterForm />
									)}
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>

			<Row lg>
				<section className="b-highlights u-mb-xl">
					<h2 className="u-vhide">
						<FormattedMessage description={'homepage-hiddenH2'} defaultMessage={'Naše hodnoty'} />
					</h2>
					<div className="b-highlights__list u-mb-last-0">
						<div className="b-highlights__item">
							<div className="b-highlights__inner u-mb-last-0">
								<span className="b-highlights__icon">
									<Icon name="highlight-1" />
								</span>
								<h3 className="b-highlights__title">
									<FormattedMessage defaultMessage={'Hlídáme za vás realitní servery'} description={'homepage-h3'} />
								</h3>
								<span className="b-highlights__text">
									<FormattedMessage
										description={'homepage-dontWaste'}
										defaultMessage={'Netravte hodiny hledáním nabídek. Náš hlídací pes to udělá za vás'}
									/>
								</span>
							</div>
						</div>
						<div className="b-highlights__item">
							<div className="b-highlights__inner u-mb-last-0">
								<span className="b-highlights__icon">
									<Icon name="highlight-2" />
								</span>
								<h3 className="b-highlights__title">Nové nabídky do&nbsp;1&nbsp;minuty</h3>
								<span className="b-highlights__text">
									<FormattedMessage
										description={'homepage-newOffers'}
										defaultMessage={'Rychlost přináší výsledky. Buďte rychlejší než ostatní'}
									/>
								</span>
							</div>
						</div>
						<div className="b-highlights__item">
							<div className="b-highlights__inner u-mb-last-0">
								<span className="b-highlights__icon">
									<Icon name="highlight-4" />
								</span>
								<h3 className="b-highlights__title">
									<FormattedMessage description={'homepage-noSpam'} defaultMessage={'Žádný spam a duplicitní nabídky'} />
								</h3>
								<span className="b-highlights__text">
									<FormattedMessage
										description={'homepage-noSpamDesc'}
										defaultMessage={'Z nalezených inzerátů chytře odfiltrujeme stejné nabídky a spamy'}
									/>
								</span>
							</div>
						</div>
						<div className="b-highlights__item">
							<div className="b-highlights__inner u-mb-last-0">
								<span className="b-highlights__icon">
									<Icon name="highlight-3" />
								</span>
								<h3 className="b-highlights__title">Vyzkoušejte zdarma na&nbsp;7&nbsp;dní</h3>
								<span className="b-highlights__text">
									<FormattedMessage
										description={'homepage-forFreeDesc'}
										defaultMessage={'Otestujte monitoring  nemovitostí se všemi funkcemi bez závazků'}
									/>
								</span>
							</div>
						</div>
					</div>
				</section>
			</Row>

			<Row>
				{lastOffers.length > 0 && (
					<section className="c-crossroad u-mb-xl">
						<Title tagName="h2" margin="sm" align="center">
							<FormattedMessage description={'homepage-newestOffers'} defaultMessage={'Nejnovější nalezené nabídky'} />
						</Title>
						<div className="c-crossroad__list grid grid--y-lg">
							{lastOffers.map((offer) => (
								<div className="c-crossroad__item grid__cell grid__cell--eq size--6-12@sm size--4-12@md" key={offer.id}>
									<OfferBox offer={offer} />
								</div>
							))}
						</div>
					</section>
				)}

				<Workflow title={formatMessage({ defaultMessage: 'Jak Visidoo funguje?' })} />
			</Row>

			<CtaCommon />
		</>
	);
};

Index.displayName = 'Index';

export default Index;

// TODO: No name should be needed for the index page
export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/',
	[Locale.en]: '/',
});
