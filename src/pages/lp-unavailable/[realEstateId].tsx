import { fetchRealEstateGet } from 'api/realEstate/fetchRealEstateGet';
import { fetchSimilarRealEstates } from 'api/realEstate/fetchSimilarRealEstates';
import { PageSeo } from 'components/meta/PageSeo';
import { ArticleIntro } from 'components/ui/blog/ArticleIntro';
import { CtaCommon } from 'components/ui/boxes/CtaCommon';
import { Row } from 'components/ui/core/Row';
import { Title } from 'components/ui/core/Title';
import { RealEstateLocation } from 'components/ui/filter/RealEstateLocation';
import { OfferBox } from 'components/ui/offer/OfferBox';
import { OfferPrice } from 'components/ui/offer/OfferPrice';
import { OfferPriceM2 } from 'components/ui/offer/OfferPriceM2';
import { OfferTitle } from 'components/ui/offer/OfferTitle';
import { Locale } from 'i18n/supportedLocales';
import { GetStaticPaths, GetStaticProps, NextPage } from 'next';
import { FormattedMessage, useIntl } from 'react-intl';
import { RealEstateType } from 'types/filter';
import { GetLocaleUrls } from 'types/getLocaleUrls';
import { Offer, OfferType } from 'types/offer';
import { ONE_DAY } from 'utils/constants';

type Query = {
	realEstateId: string;
};

type Props = {
	realEstate?: Offer;
	similarRealEstates?: Offer[];
};

const LpUnavailable: NextPage<Props> = ({ realEstate, similarRealEstates = [] }) => {
	const { formatMessage } = useIntl();

	return (
		<>
			<PageSeo
				title={formatMessage({
					defaultMessage: 'Nabídka již není aktivní',
					description: 'lpUnavailable-title-head',
				})}
				description={formatMessage({
					defaultMessage: 'Prodejce bohužel nabídku stáhl z internetu.',
					description: 'lpUnavailable-mainText',
				})}
			/>
			<Row lg>
				<ArticleIntro
					bg
					margin="xs"
					title={
						<FormattedMessage
							description="lpUnavailable-title"
							defaultMessage="Nabídka již <strong>není aktivní</strong>"
							values={{
								strong: (...chunks: string[]) => <strong>{chunks}</strong>,
							}}
						/>
					}
				>
					<p className="caption">
						<FormattedMessage
							description="lpUnavailable-mainText"
							defaultMessage="Prodejce bohužel nabídku stáhl z internetu."
						/>
						<br />

						{similarRealEstates.length > 0 && (
							<span className="u-font-light">
								<FormattedMessage
									description="lpUnavailable-similarRealEstates"
									defaultMessage="Ale nezoufejte, pokusili jsme se pro vás najít alternativní nabídky se stejnými parametry, které jsou stále aktivní."
								/>
							</span>
						)}
					</p>
				</ArticleIntro>
			</Row>

			<Row>
				{similarRealEstates.length > 0 && (
					<section className="c-crossroad u-mb-md">
						<Title tagName="h2" margin="xs" align="center">
							<FormattedMessage description={'unavailable-other-offers'} defaultMessage={'Alternativní nabídky'} />
						</Title>
						<div className="c-crossroad__list grid grid--y-lg">
							{similarRealEstates.map((offer) => (
								<div className="c-crossroad__item grid__cell grid__cell--eq size--6-12@sm size--4-12@md" key={offer.id}>
									<OfferBox offer={offer} />
								</div>
							))}
						</div>
					</section>
				)}

				{realEstate && (
					<>
						<Title tagName="h2" margin="xs" align="center">
							<FormattedMessage description={'unavailable-original-offer'} defaultMessage={'Původní nabídka'} />
						</Title>
						<div className="b-product-lg b-product-lg--old u-mb-lg">
							<div className="b-product-lg__img">
								<a href={realEstate.url} target="_blank" rel="noopener noreferrer">
									<img src={realEstate.imageUrl} loading="lazy" decoding="async" alt="" width="350" height="250" />
								</a>
							</div>
							<div className="b-product-lg__content">
								<h1 className="b-product-lg__title h5">
									<OfferTitle {...realEstate} />
								</h1>
								<p className="b-product-lg__address">
									<RealEstateLocation location={realEstate.location} />
								</p>
								<p className="b-product-lg__details">
									<OfferPrice {...realEstate} />
									{realEstate.adType === OfferType.SALE &&
										(realEstate.realEstateType === RealEstateType.APARTMENT ||
											realEstate.realEstateType === RealEstateType.LAND) && <OfferPriceM2 {...realEstate} />}
								</p>
							</div>
						</div>
					</>
				)}
			</Row>

			<CtaCommon />
		</>
	);
};

LpUnavailable.displayName = 'LpUnavailable';

export default LpUnavailable;

export const getStaticProps: GetStaticProps<Props, Query> = async ({ params }) => {
	if (!params) return { notFound: true };
	const { realEstateId } = params;
	if (!realEstateId) return { notFound: true };

	let realEstate: Offer;
	try {
		const { data } = await fetchRealEstateGet(realEstateId);
		realEstate = data.realEstate;
	} catch {
		return { notFound: true };
	}

	let similarRealEstates: Offer[] = [];
	try {
		const { data } = await fetchSimilarRealEstates(realEstateId);
		similarRealEstates = data.realEstates;
	} catch (e) {
		console.error(e);
	}

	return {
		revalidate: ONE_DAY / 1000,
		props: { realEstate, similarRealEstates },
	};
};

export const getStaticPaths: GetStaticPaths = async () => {
	return {
		paths: [],
		fallback: true,
	};
};

export const getLocaleUrls: GetLocaleUrls = () => ({
	[Locale.cs]: '/lp-unavailable/[realEstateId]',
	[Locale.en]: '/lp-unavailable/[realEstateId]',
});
