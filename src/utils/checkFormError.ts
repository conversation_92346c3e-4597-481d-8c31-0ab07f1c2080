type Errors = Record<string, any>;

export function checkFormError(inputName: string, errors: Errors) {
	const isCompositeName = inputName.includes('.');
	const hasError = isCompositeName ? handleCompositeErrorField(inputName, errors) : errors[inputName] != null;
	return hasError;
}

const handleCompositeErrorField = (inputName: string, errors: Errors) => {
	return inputName.split('.').reduce((o, i) => o?.[i], errors) != null;
};
