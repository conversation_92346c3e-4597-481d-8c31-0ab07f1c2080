import { GeoPoint } from 'types/offer';
import { formatKm } from './formats';

export const calculateDistanceBetweenPoints = (geoPoint1: GeoPoint, geoPoint2: GeoPoint) => {
	if (!geoPoint1 || !geoPoint2) return null;

	const [lon1, lat1] = geoPoint1;
	const [lon2, lat2] = geoPoint2;
	const earthRadius = 6371;
	const degreesLat = degreesToRadius(lat2 - lat1);
	const degreesLon = degreesToRadius(lon2 - lon1);

	const a =
		Math.sin(degreesLat / 2) * Math.sin(degreesLat / 2) +
		Math.cos(degreesToRadius(lat1)) * Math.cos(degreesToRadius(lat2)) * Math.sin(degreesLon / 2) * Math.sin(degreesLon / 2);
	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

	const distanceInKm = earthRadius * c;
	return formatKm(parseFloat(distanceInKm.toFixed(1)));
};

const degreesToRadius = (deg: number) => {
	return deg * (Math.PI / 180);
};
