import { UserState } from 'types/user';

const doAnalytics = () => process.env.NEXT_PUBLIC_DEVELOPMENT !== 'true' && process.env.NEXT_PUBLIC_GTM_TRACKING_ID;

const getDataLayer = () => (window.dataLayer = window.dataLayer ?? []);

export const pageview = (url: string) => {
	if (doAnalytics()) {
		getDataLayer().push({ event: 'Pageview', pagePath: url, pageTitle: document.title });
	}
};

export const trackEvent = (data: { category: string; action: string; label: string; value?: number }) => {
	if (doAnalytics()) {
		getDataLayer().push({ event: 'GAEvent', ...data });
	}
};

export const identifyUser = (loggedIn: boolean, userState?: UserState) => {
	if (doAnalytics()) {
		if (loggedIn) {
			getDataLayer().push({ userState: 'LoggedIn' });
		} else if (userState) {
			getDataLayer().push({ userState });
		}
	}
};

export const trackFilterCount = (count: number) => {
	if (doAnalytics()) {
		getDataLayer().push({ userActiveFilterCount: count });
	}
};

export const trackFacebookLandingPage = (id: string, price: number) => {
	if (doAnalytics()) {
		getDataLayer().push({ event: 'FBlandingPage', content_type: 'home_listing', content_ids: [id], value: price, currency: 'CZK' });
	}
};
