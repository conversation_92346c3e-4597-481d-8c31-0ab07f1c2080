import { parse, ParsedUrlQuery, stringify } from 'querystring';

export const filterQueryString = (fullPath: string, keys: string[]) => {
	const [path, query] = fullPath.split('?');
	if (!query) return path;

	const params = parse(query);

	const newParams = Object.keys(params).reduce((acc: ParsedUrlQuery, key) => {
		if (!keys.includes(key)) acc[key] = params[key];
		return acc;
	}, {});
	const newQuery = stringify(newParams);

	if (newQuery.length === 0) return path;
	return `${path}?${newQuery}`;
};
