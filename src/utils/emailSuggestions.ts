const EMAIL_CLIENT_SUGGESTIONS = [
	'atlas.cz',
	'azet.sk',
	'centrum.cz',
	'centrum.sk',
	'email.cz',
	'gmail.com',
	'hotmail.com',
	'icloud.com',
	'mail.ru',
	'outlook.com',
	'post.cz',
	'seznam.cz',
	'tiscali.cz',
	'volny.cz',
	'yahoo.com',
];
const MAX_NUMBER_OF_EDITS = 2;

type SuggestionScore = {
	client: string;
	score: number;
};

const getEditDistance = (a: string, b: string) => {
	if (a.length === 0) return b.length;
	if (b.length === 0) return a.length;

	const matrix = [];

	// increment along the first column of each row
	for (let i = 0; i <= b.length; i++) {
		matrix[i] = [i];
	}

	// increment each column in the first row
	for (let j = 0; j <= a.length; j++) {
		matrix[0][j] = j;
	}

	// Fill in the rest of the matrix
	for (let i = 1; i <= b.length; i++) {
		for (let j = 1; j <= a.length; j++) {
			if (b.charAt(i - 1) == a.charAt(j - 1)) {
				matrix[i][j] = matrix[i - 1][j - 1];
			} else {
				matrix[i][j] = Math.min(
					matrix[i - 1][j - 1] + 1, // substitution
					Math.min(
						matrix[i][j - 1] + 1, // insertion
						matrix[i - 1][j] + 1,
					),
				); // deletion
			}
		}
	}

	return matrix[b.length][a.length];
};

const parseEmail = (emailEntered?: string) => {
	const [username, emailClient] = emailEntered ? emailEntered.split('@') : ['', ''];
	return { username, emailClient };
};

export const getEmailSuggestions = (emailEntered: string) => {
	const { username, emailClient } = parseEmail(emailEntered);
	if (!emailClient) return [];

	const scoreResults = calculateSuggestionsScores(emailClient);
	const suggestions = filterSuggestions(scoreResults, username, emailClient);
	return suggestions;
};

const calculateSuggestionsScores = (enteredEmailClient: string): SuggestionScore[] => {
	const scoreResults = EMAIL_CLIENT_SUGGESTIONS.map((suggestion) => ({
		client: suggestion,
		score: getEditDistance(enteredEmailClient, suggestion),
	}));

	return scoreResults;
};

const filterSuggestions = (scoreResults: SuggestionScore[], username: string, emailClientEntered: string) => {
	const sortedResults = sortScoreResults(scoreResults);
	const [closestMatch] = sortedResults;
	const closestScore = closestMatch.score < MAX_NUMBER_OF_EDITS ? closestMatch.score : MAX_NUMBER_OF_EDITS;

	return sortedResults.reduce((acc: string[], curr) => {
		const isCloseEnough = curr.score > 0 && curr.score <= closestScore;
		if (isCloseEnough) {
			const emailToSuggest = `${username}@${curr.client}`;
			return curr.client[0].toLowerCase() === emailClientEntered[0].toLowerCase()
				? [emailToSuggest, ...acc]
				: [...acc, emailToSuggest];
		} else {
			return acc;
		}
	}, []);
};

const sortScoreResults = (scoreResults: SuggestionScore[]) => [...scoreResults].sort((a, b) => a.score - b.score);
