import { fetchQuestionnairePatch } from 'api/questionnaire/fetchQuestionnairePatch';
import { AddNotification } from 'types/common';
import { OfferType } from 'types/offer';
import { QuestionnaireFilterType, QuestionnaireInterest, QuestionnaireUserData } from 'types/questionnaire';
import { FIVE_SECONDS } from './constants';

export const QUESTIONNAIRE_NOTIFICATION_TIMEOUT = FIVE_SECONDS;

type Props = AddNotification & {
	interest: QuestionnaireInterest;
	questionnaireId: string;
};

export const addUserInterest = async ({ addNotification, interest, questionnaireId }: Props) => {
	try {
		const res = await fetchQuestionnairePatch({ interest }, questionnaireId);

		if (res.statusCode === 200) {
			addNotification({ message: '<PERSON><PERSON><PERSON> zájem byl uložen', timeout: QUESTIONNAIRE_NOTIFICATION_TIMEOUT });
		} else {
			addNotification({ message: res.message || 'Nastala chyba', timeout: QUESTIONNAIRE_NOTIFICATION_TIMEOUT });
		}
	} catch (error) {
		console.error(error);
		addNotification({ message: 'Nastala chyba', timeout: QUESTIONNAIRE_NOTIFICATION_TIMEOUT });
	}
};

export const isValidFilterForQuestionnaire = (filterType: OfferType): filterType is QuestionnaireFilterType => {
	return filterType === OfferType.SALE || filterType === OfferType.RENT;
};

export const getBlankQuestionnaireUserDetails = (userEmail: string | undefined): QuestionnaireUserData => {
	return {
		userData: {
			email: userEmail ?? '',
			firstName: '',
			lastName: '',
			telephone: '',
		},
	};
};
