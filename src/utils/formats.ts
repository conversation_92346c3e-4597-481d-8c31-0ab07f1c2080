import { SupportedLocale } from 'i18n/supportedLocales';
import { Unit } from 'types/units';

let locale = 'cs';

export const setFormatLocale = (newLocale: string) => (locale = newLocale);

export const resetFormatLocale = () => (locale = 'cs');

export const formatNumber = (value: number, minimumFractionDigits = 0, maximumFractionDigits = minimumFractionDigits) =>
	value.toLocaleString(locale, { minimumFractionDigits, maximumFractionDigits });

export const formatPercent = (value: number, minimumFractionDigits = 0, maximumFractionDigits = minimumFractionDigits) =>
	value.toLocaleString(locale, { style: 'percent', minimumFractionDigits, maximumFractionDigits });

export const formatCurrency = (value: number, currency: string, minimumFractionDigits = 0, maximumFractionDigits = 2) =>
	value.toLocaleString(locale, { style: 'currency', currency, minimumFractionDigits, maximumFractionDigits });

export const formatDate = (date: Date) => date.toLocaleDateString(locale);

export const formatDateTime = (date: Date) => date.toLocaleString(locale);

export const formatTime = (date: Date) => date.toLocaleTimeString(locale);

export const normalizeString = (string: string, keepSpaces = false) =>
	string
		.toLowerCase()
		.replace(/\s/g, keepSpaces ? ' ' : '-')
		.normalize('NFD')
		.replace(/[\u0300-\u036f]/g, '');

export const uppercaseFirst = (string: string, locale: SupportedLocale) => string[0].toLocaleUpperCase(locale) + string.substring(1);

const formatUnit = (unit: Unit) => (value: number) => {
	const withUnit = value.toLocaleString(locale, {
		style: 'unit',
		unit,
	});
	return withUnit.replace(' ', '\u00a0');
};

export const formatKm = formatUnit('kilometer');

export const formatMeter = formatUnit('meter');

export const formatSquareKm = (value: number) => `${formatKm(value)}²`;
