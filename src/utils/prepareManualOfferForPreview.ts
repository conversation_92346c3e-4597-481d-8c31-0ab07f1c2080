import { NonPublicOfferSchema, Offer } from 'types/offer';

export function prepareManualOfferForPreview(parsedOffer: NonPublicOfferSchema): Offer {
	const {
		imageUrl,
		description,
		ownershipType,
		conditionType,
		buildingType,
		terraceType,
		elevator,
		basement,
		floor,
		houseType,
		landWidth,
		parkingType,
		title,
		...rest
	} = parsedOffer.realEstate;

	const images: string[] = Array.isArray(imageUrl) ? imageUrl : imageUrl != null ? [imageUrl] : [];

	const extended = {
		description,
		ownershipType,
		conditionType,
		buildingType,
		terraceType,
		elevator,
		basement,
		floor,
		houseType,
		landWidth,
		parkingType,
		title,
		updatedAt: '',
	};

	const { contact } = parsedOffer;

	return {
		contact: { ...contact, id: '', createdAt: Date.now() },
		createdAt: Date.now(),
		extended,
		id: '',
		images,
		imageUrl: typeof imageUrl === 'string' ? imageUrl : '',
		...rest,
	} as unknown as Offer;
}
