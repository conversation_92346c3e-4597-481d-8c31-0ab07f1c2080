import { SupportedLocale } from 'i18n/supportedLocales';
import { IntlShape } from 'react-intl';
import { HouseType, LandType, RealEstateType } from 'types/filter';
import { OfferType } from 'types/offer';
import { formatNumber, uppercaseFirst } from 'utils/formats';

type TitleProps = {
	adType: OfferType;
	realEstateType: RealEstateType;
	landType?: LandType;
	area?: number;
	landArea?: number;
	disposition?: string;
};

export const getFiltersLocalizedStrings = (intl: IntlShape, shortLocale: SupportedLocale) => {
	const getLandSizeString = (landArea: number) => {
		const withlandSizeIntlString = intl.formatMessage(
			{
				description: 'offerTitle-withLandSize',
				defaultMessage: 's pozemkem {landSize}',
			},
			{
				landSize: `${formatNumber(landArea)} m² `,
			},
		);

		return withlandSizeIntlString;
	};

	const getAdLabelVerbs = () => ({
		[OfferType.AUCTION]: intl.formatMessage({ defaultMessage: 'dražit', description: 'auction-verb-infinitive' }),
		[OfferType.OTHER]: intl.formatMessage({ defaultMessage: 'nabídnout', description: 'makeOffer-verb-infinitive' }),
		[OfferType.RENT]: intl.formatMessage({ defaultMessage: 'pronajmout', description: 'rent-verb-infinitive' }),
		[OfferType.SALE]: intl.formatMessage({ defaultMessage: 'koupit', description: 'sale-verb-infinitive' }),
		[OfferType.ROOMMATES]: intl.formatMessage({ defaultMessage: 'spolubydlet', description: 'roommates-verb-infinitive' }),
	});

	const getRealEstateLabelsFirstFall = () => ({
		[RealEstateType.APARTMENT]: intl.formatMessage({ defaultMessage: 'byt', description: 'nominative-apartment' }),
		[RealEstateType.HOUSE]: intl.formatMessage({ defaultMessage: 'dům', description: 'nominative-house' }),
		[RealEstateType.LAND]: intl.formatMessage({ defaultMessage: 'pozemek', description: 'nominative-land' }),
		[RealEstateType.COMMERCIAL]: intl.formatMessage({ defaultMessage: 'komerční objekt', description: 'nominative-commercial' }),
		[RealEstateType.OTHER]: intl.formatMessage({ defaultMessage: 'nemovitost', description: 'nominative-otherProperty' }),
	});

	const getHouseTypeLabelsFirstFall = () => ({
		[HouseType.TERRACED]: intl.formatMessage({
			defaultMessage: 'řadový',
			description: 'nominative-houseType-terraced',
		}),
		[HouseType.DETACHED]: intl.formatMessage({
			defaultMessage: 'samostatný',
			description: 'nominative-houseType-detached',
		}),
		[HouseType.COTTAGE]: intl.formatMessage({
			defaultMessage: 'chalupa',
			description: 'nominative-houseType-cottage',
		}),
		[HouseType.HUT]: intl.formatMessage({
			defaultMessage: 'chata',
			description: 'nominative-houseType-hut',
		}),
	});

	const getLandTypeLabelsFirstFall = () => ({
		[LandType.BUILD_PLOT]: intl.formatMessage({
			defaultMessage: 'stavební',
			description: 'nominative-landType-buildPlot',
		}),
		[LandType.COMMERCIAL]: intl.formatMessage({
			defaultMessage: 'komerční',
			description: 'nominative-landType-commercial',
		}),
		[LandType.OTHER]: intl.formatMessage({
			defaultMessage: 'užitkový',
			description: 'nominative-landType-other',
		}),
	});

	const getRealEstateLabelsSecondFall = () => ({
		[RealEstateType.APARTMENT]: intl.formatMessage({ defaultMessage: 'bytu', description: 'genitiveIfPresent-apartment' }),
		[RealEstateType.HOUSE]: intl.formatMessage({ defaultMessage: 'domu', description: 'genitiveIfPresent-house' }),
		[RealEstateType.LAND]: intl.formatMessage({ defaultMessage: 'pozemku', description: 'genitiveIfPresent-land' }),
		[RealEstateType.COMMERCIAL]: intl.formatMessage({
			defaultMessage: 'komerčního objektu',
			description: 'genitiveIfPresent-commercial',
		}),
		[RealEstateType.OTHER]: intl.formatMessage({
			defaultMessage: 'nemovitosti',
			description: 'genitiveIfPresent-otherProperty',
		}),
	});

	const getHouseTypeLabelsSecondFall = () => ({
		[HouseType.TERRACED]: intl.formatMessage({
			defaultMessage: 'řadového',
			description: 'genitive-houseType-terraced',
		}),
		[HouseType.DETACHED]: intl.formatMessage({
			defaultMessage: 'samostatného',
			description: 'genitive-houseType-detached',
		}),
		[HouseType.COTTAGE]: intl.formatMessage({
			defaultMessage: 'chalupy',
			description: 'genitive-houseType-cottage',
		}),
		[HouseType.HUT]: intl.formatMessage({
			defaultMessage: 'chaty',
			description: 'genitive-houseType-hut',
		}),
	});

	const getLandTypeLabelsSecondFall = () => ({
		[LandType.BUILD_PLOT]: intl.formatMessage({
			defaultMessage: 'stavebního',
			description: 'genitiveIfPresent-landType-buildPlot',
		}),
		[LandType.COMMERCIAL]: intl.formatMessage({
			defaultMessage: 'komerčního',
			description: 'genitiveIfPresent-landType-commercial',
		}),
		[LandType.OTHER]: intl.formatMessage({
			defaultMessage: 'užitkového',
			description: 'genitiveIfPresent-landType-commercial',
		}),
	});

	const getOfferType = (adType: OfferType) => {
		const adLabelsNoun = {
			[OfferType.AUCTION]: intl.formatMessage({ defaultMessage: 'dražba', description: 'auction-noun' }),
			[OfferType.OTHER]: intl.formatMessage({ defaultMessage: 'nabídka', description: 'offerOther-noun' }),
			[OfferType.RENT]: intl.formatMessage({ defaultMessage: 'pronájem', description: 'rent-noun' }),
			[OfferType.SALE]: intl.formatMessage({ defaultMessage: 'prodej', description: 'sale-noun' }),
			[OfferType.ROOMMATES]: intl.formatMessage({ defaultMessage: 'spolubdlení', description: 'roommates-noun' }),
		};

		return adLabelsNoun[adType];
	};

	const getPropertyString = (
		adType: OfferType,
		realEstateType: RealEstateType,
		area?: number,
		landArea?: number,
		disposition?: string,
	) => {
		const realEstateSecondFalls = getRealEstateLabelsSecondFall();
		return intl.formatMessage(
			{
				description: 'offerTitle-house&apartment',
				defaultMessage: '{offerType} {realEstateType} {disposition} {buildingArea} {landSize}',
			},
			{
				offerType: getOfferType(adType),
				realEstateType: realEstateSecondFalls[realEstateType],
				disposition: disposition || '',
				buildingArea: area ? `${area} m²` : '',
				landSize: landArea ? getLandSizeString(landArea) : '',
			},
		);
	};

	const getLandString = (adType: OfferType, landType?: LandType, landArea?: number) => {
		const landTypeSecondFalls = getLandTypeLabelsSecondFall();

		const landIntlString = intl.formatMessage(
			{
				description: 'offerTitle-land',
				defaultMessage: '{offerType} {landType} pozemku {landSize}',
			},
			{
				offerType: getOfferType(adType),
				landType: landType ? landTypeSecondFalls[landType] : '',
				landSize: landArea ? `${formatNumber(landArea)} m²` : '',
			},
		);

		return landIntlString;
	};

	const generateTitle = ({ adType, realEstateType, landType, area, landArea, disposition }: TitleProps) => {
		switch (true) {
			case realEstateType === RealEstateType.LAND: {
				return getLandString(adType, landType, landArea);
			}

			default:
				return getPropertyString(adType, realEstateType, area, landArea, disposition);
		}
	};

	const generateLabel = ({
		adType,
		realEstateType,
		houseType,
		landType,
	}: {
		adType?: OfferType;
		realEstateType?: RealEstateType;
		houseType?: HouseType;
		landType?: LandType;
	}) => {
		const realEstateFirstFalls = getRealEstateLabelsFirstFall();
		const landTypeFirstFalls = getLandTypeLabelsFirstFall();
		const landTypeSecondFalls = getLandTypeLabelsSecondFall();
		const houseTypeSecondFalls = getHouseTypeLabelsSecondFall();
		const realEstateSecondFalls = getRealEstateLabelsSecondFall();
		const verbs = getAdLabelVerbs();

		if (adType && realEstateType) {
			if (houseType && realEstateType === RealEstateType.HOUSE) {
				if (houseType === HouseType.COTTAGE || houseType === HouseType.HUT) {
					return uppercaseFirst(`${getOfferType(adType)} ${houseTypeSecondFalls[houseType]}`, shortLocale);
				}

				return uppercaseFirst(
					`${getOfferType(adType)} ${houseTypeSecondFalls[houseType]} ${realEstateSecondFalls[realEstateType]}`,
					shortLocale,
				);
			}

			if (landType && realEstateType === RealEstateType.LAND) {
				return uppercaseFirst(
					`${getOfferType(adType)} ${landTypeSecondFalls[landType]} ${realEstateSecondFalls[realEstateType]}`,
					shortLocale,
				);
			}

			return uppercaseFirst(`${getOfferType(adType)} ${realEstateSecondFalls[realEstateType]}`, shortLocale);
		}

		if (realEstateType) {
			if (landType && realEstateType === RealEstateType.LAND) {
				return uppercaseFirst(`${landTypeFirstFalls[landType]} ${realEstateFirstFalls[realEstateType]}`, shortLocale);
			}
			return uppercaseFirst(realEstateFirstFalls[realEstateType], shortLocale);
		}

		if (adType) {
			return verbs[adType];
		}

		if (landType) {
			return landTypeFirstFalls[landType];
		}

		if (houseType) {
			return getHouseTypeLabelsFirstFall()[houseType];
		}

		return '';
	};

	return { generateTitle, generateLabel };
};
