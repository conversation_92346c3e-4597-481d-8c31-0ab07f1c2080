import { ApiResponse, FilterDeleteResponse } from 'types/filter';

export const fetchFilterDelete = async (filterId: string, token: string): Promise<ApiResponse<FilterDeleteResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/filter/${filterId}`;
	const response = await fetch(apiUrl, {
		method: 'DELETE',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
	});
	const data: ApiResponse<FilterDeleteResponse> = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
