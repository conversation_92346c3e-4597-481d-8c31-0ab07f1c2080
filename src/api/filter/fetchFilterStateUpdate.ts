import { ApiResponse, FilterResponse, FilterState } from 'types/filter';

export const fetchFilterStateUpdate = async (
	filterId: string,
	state: FilterState,
	token: string,
	isAdmin = false,
): Promise<ApiResponse<FilterResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}${isAdmin ? '/admin' : ''}/filter/${filterId}`;
	const response = await fetch(apiUrl, {
		method: 'PATCH',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify({ state }),
	});
	const data: ApiResponse<FilterResponse> = await response.json();

	if (!response.ok) {
		let message = 'Do<PERSON>lo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
