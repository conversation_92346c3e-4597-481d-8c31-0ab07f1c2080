import { ApiResponse, FilterRequest, FilterResponse } from 'types/filter';

export const fetchNewFilter = async (filterData: FilterRequest): Promise<ApiResponse<FilterResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/filter`;
	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify(filterData),
	});
	const data: ApiResponse<FilterResponse> = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
