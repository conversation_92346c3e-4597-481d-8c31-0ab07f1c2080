import { encode } from 'js-base64';
import { ApiResponse, EstimateData, EstimateRequest } from 'types/filter';

const cache: Record<string, EstimateData> = {};

export const fetchFilterEstimate = async (estimateRequest: EstimateRequest): Promise<ApiResponse<EstimateData>> => {
	const key = JSON.stringify(estimateRequest);
	if (cache[key]) return { statusCode: 200, message: 'Ok', data: cache[key] };

	const encodedRequest = encodeURIComponent(encode(JSON.stringify(estimateRequest)));
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/estimate/${encodedRequest}`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { 'Content-Type': 'application/json' },
	});
	const data: ApiResponse<EstimateData> = await response.json();

	if (data.data.estimate) {
		cache[key] = data.data;
	}

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komuni<PERSON> se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
