import { ApiResponse, AutocompleteData } from 'types/filter';
import { normalizeString } from 'utils/formats';

const cache: Record<string, AutocompleteData> = {};
let controller: AbortController;

export const fetchLocations = async (query: string): Promise<ApiResponse<AutocompleteData> | undefined> => {
	if (controller) controller.abort();
	controller = new AbortController();

	const key = normalizeString(query);
	if (cache[key]) return { statusCode: 200, message: 'Ok', data: cache[key] };

	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/autocomplete?query=${query}`;
	let response;
	try {
		response = await fetch(apiUrl, {
			method: 'GET',
			headers: { 'Content-Type': 'application/json' },
			signal: controller.signal,
		});
	} catch (e) {
		void e;
	}
	if (!response) return;

	const data: ApiResponse<AutocompleteData> = await response.json();

	if (data.data.suggestions) {
		cache[key] = data.data;
	}

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k ch<PERSON> při komuni<PERSON>ci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
