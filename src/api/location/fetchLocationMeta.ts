import { ApiResponse, GeoData } from 'types/filter';

export const fetchLocationMeta = async (id: string): Promise<ApiResponse<GeoData>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/${id}`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { 'Content-Type': 'application/json' },
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
