import { ApiResponse } from 'types/filter';
import { OfferResponse } from 'types/offer';

export const fetchPrivateRealEstates = async (locationIds: string[], page?: number): Promise<ApiResponse<OfferResponse>> => {
	const params = new URLSearchParams();
	params.set('locationIds', locationIds.join('|'));
	if (page) params.set('page', `${page}`);
	const querystring = params.toString();

	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/real-estate/private${querystring ? `?${querystring}` : ''}`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { 'Content-Type': 'application/json' },
	});
	const data: ApiResponse<OfferResponse> = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
