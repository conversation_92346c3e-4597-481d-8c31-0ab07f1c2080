import { ApiResponse } from 'types/filter';
import { RealEstateResponse } from 'types/offer';

export const fetchRealEstateGet = async (realEstateId: string): Promise<ApiResponse<RealEstateResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/real-estate/${realEstateId}`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { 'Content-Type': 'application/json' },
	});
	const data: ApiResponse<RealEstateResponse> = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
