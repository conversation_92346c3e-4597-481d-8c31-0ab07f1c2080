import { ScraperDataResponse } from 'types/admin';
import { ApiResponse } from 'types/filter';
import { ScraperLocale } from 'types/admin';

export const fetchScraperData = async (locale?: ScraperLocale): Promise<ApiResponse<ScraperDataResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/scraper${locale ? '/' + locale : ''}`;
	const response = await fetch(apiUrl, {
		method: 'GET',
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
