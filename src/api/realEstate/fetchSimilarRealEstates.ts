import { ApiResponse } from 'types/filter';
import { OfferResponse, OfferType } from 'types/offer';

export const fetchSimilarRealEstates = async (realEstateId: string, adType?: OfferType): Promise<ApiResponse<OfferResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/real-estate/${realEstateId}/real-estate${adType ? '?adType=' + adType : ''}`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { 'Content-Type': 'application/json' },
	});
	const data: ApiResponse<OfferResponse> = await response.json();

	if (!response.ok) {
		let message = 'Došlo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
