import { UserResponse } from 'types/admin';
import { ApiResponse } from 'types/filter';

export const fetchUser = async (token: string, id: string): Promise<ApiResponse<UserResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/advanced/user/${id}`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { Authorization: `Bearer ${token}` },
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
