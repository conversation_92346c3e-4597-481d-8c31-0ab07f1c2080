import { ApiResponse, FiltersResponse } from 'types/filter';

export const fetchUserFilters = async (token: string, id: string): Promise<ApiResponse<FiltersResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/advanced/user/${id}/filter`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { Authorization: `Bearer ${token}` },
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
