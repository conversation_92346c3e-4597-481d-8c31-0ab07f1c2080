import { AddTelephoneInputs } from 'types/telephone';
import { PlainApiResponse } from 'types/filter';
import { TelephoneSource } from 'types/user';

export const fetchTelephonePatch = async (data: AddTelephoneInputs, emailId: string, userId: string): Promise<PlainApiResponse> => {
	const telephoneSource: TelephoneSource | null = data.telephone.length > 0 ? TelephoneSource.LANDING_PAGE : null;
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/email/${emailId}/user/${userId}`;

	try {
		const response = await fetch(apiUrl, {
			method: 'PATCH',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ ...data, telephoneSource }),
		});
		const parsedResponse: PlainApiResponse = await response.json();

		return parsedResponse;
	} catch (error) {
		return { statusCode: 400, message: '<PERSON><PERSON><PERSON> k chybě při komuni<PERSON> se serverem', data: undefined };
	}
};
