import { RegisterInputs } from 'types/Auth';
import { PlainApiResponse } from 'types/filter';
import { TelephoneSource } from 'types/user';

export const fetchTelephonePost = async (userData: RegisterInputs): Promise<PlainApiResponse> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/telephone`;
	const telephoneSource: TelephoneSource = TelephoneSource.CTA_BOX;

	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
		},
		body: JSON.stringify({ ...userData, telephoneSource }),
	});
	const data: PlainApiResponse = await response.json();

	if (!response.ok) {
		let message = 'Do<PERSON>lo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
