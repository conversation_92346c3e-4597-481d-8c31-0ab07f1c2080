import { ApiResponse } from 'types/filter';

export const fetchReportContact = async (contactId: string): Promise<ApiResponse<undefined>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/contact/review/${contactId}`;
	const response = await fetch(apiUrl, {
		method: 'PATCH',
	});
	const data = await response.json();

	if (!response.ok) {
		let message = 'Do<PERSON>lo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
