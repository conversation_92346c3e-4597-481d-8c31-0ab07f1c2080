import { ApiResponse } from 'types/filter';
import { LeadPostInputs, LeadResponse } from 'types/lead';

export const fetchLeadPost = async (input: LeadPostInputs): Promise<ApiResponse<LeadResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/lead`;
	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
		},
		body: JSON.stringify(input),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
