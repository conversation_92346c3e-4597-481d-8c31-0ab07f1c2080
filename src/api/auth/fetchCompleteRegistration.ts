import { CompleteRegistrationInputs, LoginResponse } from 'types/Auth';
import { TelephoneSource } from 'types/user';

export const fetchCompleteRegistration = async (input: CompleteRegistrationInputs): Promise<LoginResponse> => {
	const telephoneSource: TelephoneSource | null = input.telephone.length > 0 ? TelephoneSource.REGISTRATION : null;
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/register`;
	const response = await fetch(apiUrl, {
		method: 'PATCH',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify({ ...input, telephoneSource }),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = 'Došlo k chybě při komunikaci se serverem';
		if (data.statusCode === 400) {
			message = 'Váš e-mail není ve správném formátu.';
		} else if (data.statusCode === 404) {
			message = '<PERSON><PERSON>lat<PERSON><PERSON> p<PERSON>, zkuste to prosím znovu.';
		}

		throw { message };
	}

	return data;
};
