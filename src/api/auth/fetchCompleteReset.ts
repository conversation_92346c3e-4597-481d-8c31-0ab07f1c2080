import { CompleteResetInputs, LoginResponse } from 'types/Auth';

export const fetchCompleteReset = async (input: CompleteResetInputs): Promise<LoginResponse> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/reset`;
	const response = await fetch(apiUrl, {
		method: 'PATCH',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify(input),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.statusCode === 400) {
			message = 'Váš e-mail není ve správném formátu.';
		} else if (data.statusCode === 404) {
			message = 'Neplatný požadavek, zkuste to prosím znovu.';
		}

		throw { message };
	}

	return data;
};
