import { LoginInputs, LoginResponse } from 'types/Auth';

export const fetchLogin = async (input: LoginInputs): Promise<LoginResponse> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/login`;
	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify(input),
	});
	const data: LoginResponse = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.statusCode === 400) {
			message = 'Váš e-mail není ve správném formátu.';
		} else if (data.statusCode === 403 || data.statusCode === 404) {
			message = 'Neplatné přihlašovací údaje';
		}

		throw { message };
	}

	return data;
};
