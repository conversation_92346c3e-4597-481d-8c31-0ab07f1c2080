import { RegisterInputs, RegisterResponse } from 'types/Auth';

export const fetchRegister = async (input: RegisterInputs): Promise<RegisterResponse> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/register`;
	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify(input),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.statusCode === 400) {
			message = 'Váš e-mail není ve správném formátu.';
		} else if (data.statusCode === 403) {
			message = 'Zadaný e-mail už existuje.';
		}

		throw { message };
	}

	return data;
};
