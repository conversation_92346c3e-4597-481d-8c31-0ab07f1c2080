import { ResetInputs, ResetResponse } from 'types/Auth';

export const fetchReset = async (input: ResetInputs): Promise<ResetResponse> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/reset`;
	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify(input),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.statusCode === 400) {
			message = 'Váš e-mail není ve správném formátu.';
		} else if (data.statusCode === 404) {
			message = 'Zadaný e-mail nebyl nalezen.';
		}

		throw { message };
	}

	return data;
};
