import { ServiceListResponse } from 'types/admin';

export const fetchServicesList = async (filterId?: string): Promise<ServiceListResponse> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/service${filterId ? '/' + filterId : ''}`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
		},
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
