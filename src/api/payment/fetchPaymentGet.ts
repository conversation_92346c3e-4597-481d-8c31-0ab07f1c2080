import type { PaymentResponse } from 'types/payment';

export const fetchPaymentGet = async (token: string): Promise<PaymentResponse> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/payment`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { Authorization: `Bearer ${token}` },
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON>lo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
