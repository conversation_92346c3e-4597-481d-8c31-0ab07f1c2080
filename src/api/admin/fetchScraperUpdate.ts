import { ScraperRequest, ScraperResponse } from 'types/admin';
import { ApiResponse } from 'types/filter';

export const fetchScraperUpdate = async (id: string, scraperData: ScraperRequest, token: string): Promise<ApiResponse<ScraperResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/scraper/${id}`;
	const response = await fetch(apiUrl, {
		method: 'PUT',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify(scraperData),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON>lo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
