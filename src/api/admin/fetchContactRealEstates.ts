import { ApiResponse } from 'types/filter';
import { OfferResponse } from 'types/offer';

export const fetchContactRealEstates = async (token: string, id: string): Promise<ApiResponse<OfferResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/contact/${id}/real-estate`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { Authorization: `Bearer ${token}` },
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
