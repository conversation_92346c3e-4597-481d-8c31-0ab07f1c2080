import { UsersResponse } from 'types/admin';
import { AllOrNothing } from 'types/common';
import { ApiResponse } from 'types/filter';

export const fetchUsers = async (
	token: string,
	query: string,
	page: number,
	telephoneSources?: string,
	roles?: string,
	roleExpiration?: string,
	userState?: string,
): Promise<ApiResponse<UsersResponse>> => {
	const telephoneSourceParam =
		telephoneSources && telephoneSources.length > 0 && telephoneSources !== AllOrNothing.NOTHING
			? `&telephoneSources=${telephoneSources}`
			: '';

	const rolesParam = roles && roles.length > 0 && roles !== AllOrNothing.NOTHING ? `&roles=${roles}` : '';
	const roleExpirationParam =
		roleExpiration && roleExpiration.length > 0 && roleExpiration !== AllOrNothing.NOTHING ? `&roleExpiration=${roleExpiration}` : '';
	const userStateParam = userState && userState.length > 0 && userState !== AllOrNothing.NOTHING ? `&userState=${userState}` : '';

	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/user?page=${page}${
		query ? `&query=${query}` : ''
	}${telephoneSourceParam}${rolesParam}${roleExpirationParam}${userStateParam}`;

	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { Authorization: `Bearer ${token}` },
	});
	const data = await response.json();

	if (!response.ok) {
		let message = 'Došlo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
