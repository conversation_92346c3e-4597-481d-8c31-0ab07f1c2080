import { EmailTestCopyRequest } from 'types/admin';
import { ApiResponse } from 'types/filter';

export const fetchEmailTestCopy = async (id: string, input: EmailTestCopyRequest, token: string): Promise<ApiResponse<undefined>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/email/${id}`;
	const response = await fetch(apiUrl, {
		method: 'PUT',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify(input),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
