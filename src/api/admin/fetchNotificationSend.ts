import { NotificationPostRequest } from 'types/admin';
import { ApiResponse } from 'types/filter';

export const fetchNotificationSend = async (input: NotificationPostRequest, token: string): Promise<ApiResponse<undefined>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/notification`;
	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify(input),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
