import { UserResponse, UserUpdateRequest } from 'types/admin';
import { ApiResponse } from 'types/filter';

export const fetchUserUpdate = async (id: string, userData: UserUpdateRequest, token: string): Promise<ApiResponse<UserResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/user/${id}`;
	const response = await fetch(apiUrl, {
		method: 'PATCH',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify(userData),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = 'Do<PERSON>lo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
