import { ContactsResponse, ContactType } from 'types/admin';
import { ApiResponse } from 'types/filter';

export const fetchContacts = async (
	token: string,
	contactType?: ContactType,
	query?: string,
	page?: number,
): Promise<ApiResponse<ContactsResponse>> => {
	const params = new URLSearchParams();
	if (contactType) params.set('contactType', contactType);
	if (query) params.set('query', query);
	if (page) params.set('page', `${page}`);
	const querystring = params.toString();

	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/contact${querystring ? `?${querystring}` : ''}`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { Authorization: `Bearer ${token}` },
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komuni<PERSON> se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
