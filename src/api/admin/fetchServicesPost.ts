import { ServiceListResponse } from 'types/admin';

export const fetchServicesPost = async (stringifiedSchema: string, token: string): Promise<ServiceListResponse> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/service`;
	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: stringifiedSchema,
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
