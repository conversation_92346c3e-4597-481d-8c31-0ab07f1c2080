import { NewsletterResponse } from 'types/admin';
import { ApiResponse } from 'types/filter';

export const fetchNewsletterPreview = async (token: string): Promise<ApiResponse<NewsletterResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/newsletter`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { Authorization: `Bearer ${token}` },
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
