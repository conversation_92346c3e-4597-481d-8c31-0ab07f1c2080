import { ApiResponse, FilterRequest, FilterResponse } from 'types/filter';

export const fetchFilterUpdate = async (
	filterId: string,
	filterData: FilterRequest,
	token: string,
): Promise<ApiResponse<FilterResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/filter/${filterId}`;
	const response = await fetch(apiUrl, {
		method: 'PUT',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify(filterData),
	});
	const data: ApiResponse<FilterResponse> = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON>lo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
