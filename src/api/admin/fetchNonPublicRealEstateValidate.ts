import { ManualUploadRequest, NonPublicRealEstateValidateResponse } from 'types/admin';

export const fetchNonPublicRealEstateValidate = async (
	jsonData: ManualUploadRequest,
	token: string,
): Promise<NonPublicRealEstateValidateResponse> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/real-estate/non-public/validate`;
	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: jsonData.stringifiedSchema,
	});

	const data: NonPublicRealEstateValidateResponse = await response.json();

	return data;
};
