import { ScraperRequest, ScraperResponse } from 'types/admin';
import { ApiResponse } from 'types/filter';

export const fetchScraperCreate = async (request: ScraperRequest, token: string): Promise<ApiResponse<ScraperResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/scraper`;
	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify(request),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
