import { ApiResponse } from 'types/filter';

export const fetchNonPublicRealEstatePost = async (stringifiedSchema: string, token: string): Promise<ApiResponse<undefined>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/real-estate/non-public`;
	const response = await fetch(apiUrl, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: stringifiedSchema,
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		return { statusCode: response.status, message, data: undefined };
	}

	return data;
};
