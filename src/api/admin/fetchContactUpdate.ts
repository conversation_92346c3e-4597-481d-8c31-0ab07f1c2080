import { ContactPatchRequest, UserResponse } from 'types/admin';
import { ApiResponse } from 'types/filter';

export const fetchContactUpdate = async (id: string, input: ContactPatchRequest, token: string): Promise<ApiResponse<UserResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/contact/${id}`;
	const response = await fetch(apiUrl, {
		method: 'PATCH',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify(input),
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
