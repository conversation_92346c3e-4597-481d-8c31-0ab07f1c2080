import { LoginResponse } from 'types/Auth';

export const fetchUserGet = async (token: string): Promise<LoginResponse> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/user`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
	});
	const data = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
