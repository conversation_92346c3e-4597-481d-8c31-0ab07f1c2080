import { EditUserInputs, LoginResponse } from 'types/Auth';
import { TelephoneSource } from 'types/user';

export const fetchUserPatch = async (userData: EditUserInputs, token: string): Promise<LoginResponse> => {
	const telephoneSource: TelephoneSource | null = userData.telephone.length > 0 ? TelephoneSource.REGISTRATION : null;
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/user`;
	const response = await fetch(apiUrl, {
		method: 'PATCH',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
		body: JSON.stringify({ ...userData, telephoneSource }),
	});
	const data: LoginResponse = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON>lo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
