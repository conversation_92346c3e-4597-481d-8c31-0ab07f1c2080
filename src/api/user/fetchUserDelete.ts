import { ApiResponse } from 'types/filter';

export const fetchUserDelete = async (token: string): Promise<ApiResponse<undefined>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/user`;
	const response = await fetch(apiUrl, {
		method: 'DELETE',
		headers: {
			Authorization: `Bearer ${token}`,
		},
	});
	const data: ApiResponse<undefined> = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
