import { ApiResponse } from 'types/filter';
import { ApiKeyResponse } from 'types/user';

export const fetchUserApiKeyGet = async (token: string): Promise<ApiResponse<ApiKeyResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/api-key`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: {
			Authorization: `Bearer ${token}`,
		},
	});
	const data: ApiResponse<ApiKeyResponse> = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
