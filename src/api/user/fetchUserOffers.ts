import { ApiResponse } from 'types/filter';
import { OfferResponse } from 'types/offer';

export const fetchUserOffers = async (): Promise<ApiResponse<OfferResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/real-estate`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: { 'Content-Type': 'application/json' },
	});
	const data: ApiResponse<OfferResponse> = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
