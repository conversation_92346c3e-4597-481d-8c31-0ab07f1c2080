import { ApiResponse, FiltersResponse } from 'types/filter';

export const fetchUserFilters = async (token: string): Promise<ApiResponse<FiltersResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/filter`;
	const response = await fetch(apiUrl, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
	});
	const data: ApiResponse<FiltersResponse> = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
