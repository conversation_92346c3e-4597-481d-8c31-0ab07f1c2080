import { ApiResponse } from 'types/filter';
import { Questionnaire, QuestionnairePatch } from 'types/questionnaire';

export const fetchQuestionnairePatch = async (
	patchData: QuestionnairePatch,
	questionnaireId: string,
): Promise<ApiResponse<Questionnaire>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/questionnaire/${questionnaireId}`;

	const response = await fetch(apiUrl, {
		method: 'PATCH',
		headers: {
			'Content-Type': 'application/json',
		},
		body: JSON.stringify(patchData),
	});

	const data: ApiResponse<Questionnaire> = await response.json();

	if (!response.ok) {
		let message = '<PERSON><PERSON><PERSON> k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
