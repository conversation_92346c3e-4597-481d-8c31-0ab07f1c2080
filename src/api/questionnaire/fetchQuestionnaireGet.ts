import { ApiResponse } from 'types/filter';
import { QuestionnaireResponse } from 'types/questionnaire';

export const fetchQuestionnaireGet = async (email: string): Promise<ApiResponse<QuestionnaireResponse>> => {
	const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/questionnaire/${email}`;
	const response = await fetch(apiUrl);
	const data = await response.json();

	if (!response.ok) {
		let message = 'Došlo k chybě při komunikaci se serverem';
		if (data.message) {
			message = data.message;
		}

		throw { message };
	}

	return data;
};
