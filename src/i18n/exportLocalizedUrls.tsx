import { writeFile } from 'fs/promises';
import glob from 'glob';
import { LocaleUrlMap } from 'types/getLocaleUrls';
import 'types/global';

export type Item = {
	name: string;
	urls: LocaleUrlMap;
};

export type LocalizedUrls = Item[];

const handler = async () => {
	await writeFile('src/i18n/localizedUrls.json', '[]', { encoding: 'utf-8' });

	const files = await new Promise<string[]>((resolve, reject) => {
		glob(`src/pages/**/[^_]*.tsx`, (err, res) => {
			if (err) {
				reject(err);
				return;
			}
			resolve(res);
		});
	});

	const map = await Promise.all(
		files.map(async (file) => {
			const { getLocaleUrls } = await import(file.replace('src/', '').replace('.tsx', ''));
			if (getLocaleUrls) {
				const urls = getLocaleUrls() as LocaleUrlMap;
				const [, name] = /pages(.*)\./.exec(file) ?? [];
				if (!name) return;

				return { name: name.replace('/index', ''), urls } as Item;
			}
		}),
	);

	const json = JSON.stringify(map.filter((item): item is Item => typeof item !== 'undefined'));
	await writeFile('src/i18n/localizedUrls.json', json, { encoding: 'utf-8' });
};

handler();
