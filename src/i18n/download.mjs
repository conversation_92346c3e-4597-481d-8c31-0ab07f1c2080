import fetch, { FormData } from 'node-fetch';
import { createWriteStream } from 'fs';
import { pipeline } from 'stream';
import { promisify } from 'util';
import dotenv from 'dotenv';
import localesData from './supportedLocales.js';

const { Locale: localesEnum } = localesData;
const locales = Object.values(localesEnum);

const streamPipeline = promisify(pipeline);
dotenv.config();

if (!process.env.POEDITOR_TOKEN && !process.env.POEDITOR_PROJECT_ID) {
	console.error('Error: You need to specity POEDITOR_TOKEN and POEDITOR_PROJECT_ID environment variables in .env file!');
} else {
	locales.forEach(async (locale) => {
		console.log(`Downloading language ${locale}...`);

		const form = new FormData();
		form.append('api_token', process.env.POEDITOR_TOKEN);
		form.append('id', process.env.POEDITOR_PROJECT_ID);
		form.append('type', 'json');
		form.append('language', locale);
		form.append('filters', ['translated']);

		const res = await fetch('https://api.poeditor.com/v2/projects/export', {
			method: 'post',
			body: form
		});

		const json = await res.json();

		if (json.result && json.result.url) {
			const response = await fetch(json.result.url);
			if (!response.ok) throw new Error(`unexpected response ${response.statusText}`);
			await streamPipeline(response.body, createWriteStream(`./src/i18n/translated/${locale}.json`));

			console.log(`Downloading language ${locale}...done`);
		} else {
			console.log(json);
		}
	});
}
