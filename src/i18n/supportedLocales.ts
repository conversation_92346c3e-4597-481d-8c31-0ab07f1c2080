import { MessageFormatElement } from 'react-intl';

export enum Locale {
	cs = 'cs',
	en = 'en',
}

export const defaultLocale = Locale.cs;

const supportedLocales = (Object.keys(Locale) as Array<keyof typeof Locale>).map((k) => Locale[k]);

export type SupportedLocale = typeof supportedLocales[number];

export const isValidLocale = (locale: unknown): locale is SupportedLocale => {
	return supportedLocales.includes(locale as SupportedLocale);
};

type LocaleLanguageMap = Record<SupportedLocale, string>;

export const localeLanguageMap: LocaleLanguageMap = {
	cs: 'Český',
	en: 'English',
};

type IntlMessages = Promise<Record<string, MessageFormatElement[]>>;

export const getLocaleMessages = (shortLocale: Locale): IntlMessages => {
	switch (shortLocale) {
		case 'en':
			return import('./compiled/en.json') as unknown as IntlMessages;
		default:
			return import('./compiled/cs.json') as unknown as IntlMessages;
	}
};
