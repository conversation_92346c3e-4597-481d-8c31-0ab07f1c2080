import fetch, { FormData, fileFromSync } from 'node-fetch';
import dotenv from 'dotenv';
import localesData from './supportedLocales.js';

const { defaultLocale } = localesData;

dotenv.config();

if (!process.env.POEDITOR_TOKEN && !process.env.POEDITOR_PROJECT_ID) {
	console.error('Error: You need to specity POEDITOR_TOKEN and POEDITOR_PROJECT_ID environment variables in .env file!');
} else {
	console.log(`Uploading default language ${defaultLocale}...`);
	const form = new FormData();
	const blob = fileFromSync(`./src/i18n/extracted/${defaultLocale}.json`);

	form.append('api_token', process.env.POEDITOR_TOKEN);
	form.append('id', process.env.POEDITOR_PROJECT_ID);
	form.append('updating', 'terms_translations');
	form.append('language', defaultLocale);
	form.append('sync_terms', '0');
	form.append('file', blob);

	fetch('https://api.poeditor.com/v2/projects/upload', {
		method: 'post',
		body: form
	})
		.then((res) => res.json())
		.then((data) => console.log(data))
		.catch((error) => console.error(error));
}
