import { commonInltMessages } from 'i18n/commonIntlMessages/common';
import { MessageDescriptor } from 'react-intl';
import { BuildingType, ConditionType, OwnershipType, TerraceType } from 'types/offer';

const {
	municipalOwnershipIntlString,
	directOwnershipIntlString,
	sharedOwnershipIntlString,
	cooperativeOwnershipIntlString,
	conditionGoodIntlString,
	conditionBadIntlString,
	conditionInProgressIntlString,
	conditionNewIntlString,
	buildingBrickIntlString,
	buildingOtherIntlString,
	buildingPanelIntlString,
	buildingStoneIntlString,
	buildingWoodIntlString,
	terraceIntlString,
	balconyIntlString,
	loggiaIntlString,
} = commonInltMessages;

type OwnerShipTypeUnion = `${OwnershipType}`;
export const ownershipTypeMessageMap: Record<OwnerShipTypeUnion, MessageDescriptor> = {
	Direct: directOwnershipIntlString,
	Cooperative: cooperativeOwnershipIntlString,
	Municipal: municipalOwnershipIntlString,
	Shared: sharedOwnershipIntlString,
};

type ConditionTypeUnion = `${ConditionType}`;
export const conditionTypeMessageMap: Record<ConditionTypeUnion, MessageDescriptor> = {
	Good: conditionGoodIntlString,
	New: conditionNewIntlString,
	'In Progress': conditionInProgressIntlString,
	Bad: conditionBadIntlString,
};

type BuildingTypeUnion = `${BuildingType}`;
export const buildingTypeMessageMap: Record<BuildingTypeUnion, MessageDescriptor> = {
	Brick: buildingBrickIntlString,
	Other: buildingOtherIntlString,
	Panel: buildingPanelIntlString,
	Stone: buildingStoneIntlString,
	Wood: buildingWoodIntlString,
};

type TerraceTypeUnion = `${TerraceType}`;
export const teraceTypeMessageMap: Record<TerraceTypeUnion, MessageDescriptor> = {
	Balcony: balconyIntlString,
	Loggia: loggiaIntlString,
	Terrace: terraceIntlString,
};
