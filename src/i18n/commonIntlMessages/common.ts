import { defineMessages } from 'react-intl';

export const commonInltMessages = defineMessages({
	seeOlderListingsIntlString: {
		defaultMessage: 'Zobrazit star<PERSON>',
		description: 'tips&Tricks-button',
	},
	ourTipsIntlString: {
		description: 'homepage-tips',
		defaultMessage: 'Na<PERSON>e rady, tipy \uff06 triky',
	},
	likeServiceIntlString: {
		description: 'myFilters-likeService',
		defaultMessage: '<PERSON>íbí se vám tato služba?',
	},
	shareWithFamilyIntlString: {
		description: 'myFilters-shareWithFamily',
		defaultMessage:
			'Sd<PERSON>lejte se svou rodinou nebo kamarád. Možná se jim také bude hodit, aby našli svou vysněnou nemovitost. Zároveň podpoříte náš projekt. Děkujeme.',
	},
	insertionIntlString: {
		description: 'insertionPage-titleSEO',
		defaultMessage: 'Inzerce',
	},
	payPerViewIntlString: {
		description: 'insertionSimplePage-payOnlyPerView',
		defaultMessage: 'P<PERSON><PERSON>te vždy jen za to co vaši potenciální zákazníci uvidí.',
	},
	fetchGenericErrorString: {
		description: 'fetch-genericError',
		defaultMessage: 'Došlo k chybě při komunikaci se serverem',
	},
	dispositionIntlString: {
		description: 'disposition-general',
		defaultMessage: 'Dispozice:',
	},
	areaIntlString: {
		description: 'area-general',
		defaultMessage: 'Podlahová plocha:',
	},
	landAreaIntlString: {
		description: 'landArea-general',
		defaultMessage: 'Velikost pozemku:',
	},
	floorNumberIntlString: {
		description: 'floorNumber-general',
		defaultMessage: 'Podlaží:',
	},
	ownershipTypeIntlString: {
		description: 'ownershipType-general',
		defaultMessage: 'Vlastnictví',
	},
	distanceBetweenPointsIntlString: {
		description: 'distanceBetweenPoints-general',
		defaultMessage: 'Vzdálenost vzdušnou čarou:',
	},
	directOwnershipIntlString: {
		description: 'directOwnership-general',
		defaultMessage: 'Osobní',
	},
	cooperativeOwnershipIntlString: {
		description: 'cooperativeOwnership-general',
		defaultMessage: 'Družstevní',
	},
	municipalOwnershipIntlString: {
		description: 'municipalOwnership-general',
		defaultMessage: 'Obecní',
	},
	sharedOwnershipIntlString: {
		description: 'sharedOwnership-general',
		defaultMessage: 'Společnosti',
	},
	insertedAtIntlString: {
		description: 'insertedAt-general',
		defaultMessage: 'Inzerováno:',
	},
	commonTelephoneIntlString: {
		description: 'telephone-common',
		defaultMessage: 'Telefonní číslo',
	},
	addTelephoneIntlString: {
		description: 'telephone-add',
		defaultMessage: 'Zadejte své telefonní číslo',
	},
	telephonePlaceholderIntlString: {
		description: 'telephone-placeholder',
		defaultMessage: '+420',
	},
	terraceIntlString: {
		description: 'terraceType-terrace',
		defaultMessage: 'Terasa',
	},
	balconyIntlString: {
		description: 'terraceType-balcony',
		defaultMessage: 'Balkon',
	},
	loggiaIntlString: {
		description: 'terraceType-loggia',
		defaultMessage: 'Lodžie',
	},
	conditionGoodIntlString: {
		description: 'conditionType-good',
		defaultMessage: 'Dobrý',
	},
	conditionNewIntlString: {
		description: 'conditionType-new',
		defaultMessage: 'Nový',
	},
	conditionInProgressIntlString: {
		description: 'conditionType-inProgress',
		defaultMessage: 'Projekt',
	},
	conditionBadIntlString: {
		description: 'conditionType-bad',
		defaultMessage: 'Špatný',
	},
	buildingBrickIntlString: {
		description: 'buildingType-brick',
		defaultMessage: 'Cihlová',
	},
	buildingOtherIntlString: {
		description: 'buildingType-other',
		defaultMessage: 'Jiná',
	},
	buildingPanelIntlString: {
		description: 'buildingType-panel',
		defaultMessage: 'Panelová',
	},
	buildingStoneIntlString: {
		description: 'buildingType-stone',
		defaultMessage: 'Kamenná',
	},
	buildingWoodIntlString: {
		description: 'buildingType-wood',
		defaultMessage: 'Dřevěná',
	},
	yesIntlString: {
		description: 'common-yes',
		defaultMessage: 'Ano',
	},
	noIntlString: {
		description: 'common-no',
		defaultMessage: 'Ne',
	},
	orIntlString: {
		description: 'common-or',
		defaultMessage: 'nebo',
	},
	anyChoiceIntlString: {
		description: 'anyChoice-common',
		defaultMessage: 'Nezáleží',
	},
	commonRequiredFieldIntlString: {
		description: 'fieldRequired-common',
		defaultMessage: 'Toto je povinná položka',
	},
	minimumAreaIntlString: {
		description: 'minimumArea-common',
		defaultMessage: 'Zadejte oblast vyšší než 0',
	},
	wantAllIntlString: {
		description: 'wantAll-common',
		defaultMessage: 'Chci všechny',
	},
});
