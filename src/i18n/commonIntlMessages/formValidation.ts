import { defineMessages } from 'react-intl';

export const formValidationInltMessages = defineMessages({
	requiredEmailIntlString: {
		description: 'loginForm-emailRequired',
		defaultMessage: 'Váš e-mail je povinná položka',
	},
	requiredPasswordIntlString: {
		description: 'loginForm-passwordRequired',
		defaultMessage: 'Va<PERSON>e he<PERSON>lo je povinná položka',
	},
	requiredNameIntlString: {
		description: 'registrationForm-nameRequired',
		defaultMessage: 'Vaše jméno je povinná polož<PERSON>',
	},
	requiredSurnameIntlString: {
		description: 'registrationForm-surnameRequired',
		defaultMessage: 'Vaše příjmení je povinná polož<PERSON>',
	},
	requiredTelephoneIntlString: {
		description: 'registrationForm-telephoneRequired',
		defaultMessage: '<PERSON><PERSON><PERSON> telefon je povinná položka',
	},
	invalidFormIntlString: {
		description: 'registrationForm-invalidForm',
		defaultMessage: 'Neplatný formulář',
	},
	selectAtLeastOneIntlString: {
		defaultMessage: 'Vyberte alespoň jednu z položek',
		description: 'selectIcon-selectOne',
	},
});
