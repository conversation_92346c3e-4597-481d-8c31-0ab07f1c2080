import { FilterDataContext } from 'components/search/FilterDataProvider';
import { useUser } from 'components/user/UserProvider';
import { useContext, useMemo } from 'react';
import { FilterState } from 'types/filter';
import { MINI_NUM_OF_FILTERS, PROFI_NUM_OF_FILTERS, STANDARD_NUM_OF_FILTERS } from 'utils/constants';

export const useUserFilterCount = () => {
	const { filters } = useContext(FilterDataContext);
	const { isProfi, isStandard, isMini } = useUser();

	return useMemo(() => {
		const total = filters.length;
		const active = filters.filter((filter) => filter.state === FilterState.ACTIVE).length;
		const disabled = filters.filter((filter) => filter.state === FilterState.DISABLED).length;
		const deleted = filters.filter((filter) => filter.state === FilterState.DELETED).length;
		const max = isProfi ? PROFI_NUM_OF_FILTERS : isStandard ? STANDARD_NUM_OF_FILTERS : isMini ? MINI_NUM_OF_FILTERS : 0;

		return {
			total,
			active,
			disabled,
			deleted,
			max,
			isFull: active >= max,
		};
	}, [filters, isMini, isProfi, isStandard]);
};
