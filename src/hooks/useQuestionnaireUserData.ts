import { useUser } from 'components/user/UserProvider';
import { useMemo } from 'react';
import { Questionnaire, QuestionnaireUserData } from 'types/questionnaire';

export const useQuestionnaireUserData = (email: string, questionnaire: Questionnaire | null) => {
	const {
		payload: { name: user<PERSON><PERSON><PERSON><PERSON><PERSON>, surname: user<PERSON><PERSON><PERSON><PERSON>, telephone: userTelephone },
	} = useUser();

	const questionnaireUserData: QuestionnaireUserData = useMemo(() => {
		return {
			userData: {
				email: email,
				firstName: questionnaire?.firstName ?? userFirstName ?? '',
				lastName: questionnaire?.lastName ?? userLastName ?? '',
				telephone: questionnaire?.telephone ?? userTelephone ?? '',
			},
		};
	}, [email, questionnaire?.firstName, questionnaire?.lastName, questionnaire?.telephone, userFirstName, userLastName, userTelephone]);

	return {
		questionnaireUserData,
	};
};
