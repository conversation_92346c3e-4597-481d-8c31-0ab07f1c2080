import { useRouter } from 'next/router';
import { useCallback, useEffect } from 'react';
import { pageview } from 'utils/analytics';

export const usePageview = () => {
	const router = useRouter();
	const callback = useCallback((url: string) => pageview(url), []);

	useEffect(() => {
		router.events.on('routeChangeComplete', callback);

		return () => {
			router.events.off('routeChangeComplete', callback);
		};
	}, [callback, router.events]);
};
