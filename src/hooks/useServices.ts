import { fetchServicesList } from 'api/services/fetchServicesList';
import { useEffect, useState } from 'react';
import { ServiceProvider, serviceProviders } from 'types/admin';

const isValidService = (serviceName: string): serviceName is ServiceProvider => serviceProviders.includes(serviceName as ServiceProvider);

export const useServices = (filterId?: string) => {
	const [servicesToShow, setServicesToShow] = useState<ServiceProvider[]>([]);

	useEffect(() => {
		if (filterId) {
			fetchServicesList(filterId).then((res) => {
				if (res.data.services) {
					const supportedServices: ServiceProvider[] = res.data.services
						.filter((service) => isValidService(service.name))
						.map((service) => service.name as ServiceProvider);

					setServicesToShow(supportedServices);
				}
			});
		}
	}, [filterId]);

	return { servicesToShow };
};
