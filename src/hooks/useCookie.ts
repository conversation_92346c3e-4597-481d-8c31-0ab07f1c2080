import { useEffect, useState } from 'react';
import { ONE_DAY } from 'utils/constants';

const getItem = (key: string) =>
	document.cookie.split('; ').reduce((total, currentCookie) => {
		const item = currentCookie.split('=');
		const storedKey = item[0];
		const storedValue = item[1];
		return key === storedKey ? decodeURIComponent(storedValue) : total;
	}, '');

export const useCookie = (key: string) => {
	const [cookie, setCookie] = useState<string>();

	useEffect(() => {
		setCookie(getItem(key));
	}, [key]);

	const updateCookie = (value: string, daysToExpire = 365) => {
		const expires = new Date(Date.now() + daysToExpire * ONE_DAY);
		document.cookie = `${key}=${value};${daysToExpire > 0 ? ` expires=${expires.toUTCString()}` : ''} path=/`;
		setCookie(value);
	};

	return [cookie, updateCookie] as const;
};
