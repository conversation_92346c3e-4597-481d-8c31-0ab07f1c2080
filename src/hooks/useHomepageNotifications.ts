import { useNotifications } from 'components/ui/notification/NotificationsProvider';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useIntl } from 'react-intl';
import { TEN_SECONDS } from 'utils/constants';

export const useHomePageNotifications = () => {
	const { unsubscribed, contactReview } = useRouter().query;
	const { addNotification } = useNotifications();
	const { formatMessage } = useIntl();

	useEffect(() => {
		if (unsubscribed === '1' || unsubscribed === '0') {
			addNotification(
				unsubscribed === '1'
					? {
							message: formatMessage({
								defaultMessage: 'Byli jste odhl<PERSON>',
								description: 'notification-unsubscribed-success',
							}),
							timeout: TEN_SECONDS,
					  }
					: {
							message: formatMessage({
								defaultMessage: '<PERSON>hu<PERSON>el se něco nepodařilo, zkuste to prosím později znovu nebo nás kontaktujte.',
								description: 'notification-unsubscribed-failure',
							}),
					  },
			);
		}
	}, [addNotification, formatMessage, unsubscribed]);

	useEffect(() => {
		if (contactReview) {
			addNotification({
				message: formatMessage({
					defaultMessage: 'Kontakt byl odeslán k nahlášení.',
					description: 'notification-contactReview',
				}),
				timeout: TEN_SECONDS,
			});
		}
	}, [addNotification, formatMessage, contactReview]);
};
