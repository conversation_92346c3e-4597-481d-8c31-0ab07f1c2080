import { useCallback, useEffect, useMemo, useState } from 'react';
import { getEmailSuggestions } from 'utils/emailSuggestions';

const CUSTOM_VALIDATION_ERROR = 'validate';

type Props = {
	needsSuggestions: boolean;
	currentEmailErrorType: string | undefined;
	userEmail: string;
	updateEmailValue: (value: string) => void;
};

export const useEmailSuggestions = ({ needsSuggestions, currentEmailErrorType, userEmail, updateEmailValue }: Props) => {
	const [canStartShowingSuggestions, setCanStartShowingSuggestions] = useState(false);
	const [emailClientSuggestions, setEmailClientSuggestions] = useState<string[]>([]);
	const thereAreSuggestions = canStartShowingSuggestions && emailClientSuggestions?.length > 0;
	const [showSuggestionsBox, setShowSuggestionsBox] = useState(thereAreSuggestions);
	const [customValidationRan, setCustomValidationRan] = useState(false);

	const handleSuggestionSelection = useCallback(
		(value: string) => {
			updateEmailValue(value);
			setShowSuggestionsBox(false);
			setEmailClientSuggestions([]);
		},
		[updateEmailValue],
	);

	useEffect(() => {
		if (needsSuggestions && currentEmailErrorType === CUSTOM_VALIDATION_ERROR) {
			setCanStartShowingSuggestions(true);
		}
	}, [needsSuggestions, currentEmailErrorType]);

	useEffect(() => {
		if (canStartShowingSuggestions && !customValidationRan) {
			setCustomValidationRan(true);
		}
	}, [canStartShowingSuggestions, customValidationRan]);

	useEffect(() => {
		if (canStartShowingSuggestions) {
			const suggestions = getEmailSuggestions(userEmail);
			setEmailClientSuggestions(suggestions);
		}
	}, [userEmail, canStartShowingSuggestions]);

	useEffect(() => {
		if (canStartShowingSuggestions) {
			setShowSuggestionsBox(thereAreSuggestions);
		}
	}, [canStartShowingSuggestions, thereAreSuggestions]);

	return useMemo(
		() => ({
			emailClientSuggestions,
			showSuggestionsBox,
			handleSuggestionSelection,
			customValidationRan,
		}),
		[customValidationRan, emailClientSuggestions, handleSuggestionSelection, showSuggestionsBox],
	);
};
