import { filterInltMessages } from 'components/ui/filter/filterIntlMessages';
import { useIntl } from 'react-intl';
import { SelectItem } from 'types/common';
import { RealEstateType } from 'types/filter';

const { landIntlString, houseIntlString, apartmentIntlString } = filterInltMessages;

type Props = {
	fullSet: boolean;
};

export const useSelectIcons = ({ fullSet }: Props) => {
	const { formatMessage } = useIntl();

	const APARTMENT = { icon: 'sofa', label: formatMessage(apartmentIntlString), value: RealEstateType.APARTMENT };
	const HOUSE = { icon: 'house-1', label: formatMessage(houseIntlString), value: RealEstateType.HOUSE };
	const LAND = { icon: 'signpost', label: formatMessage(landIntlString), value: RealEstateType.LAND };
	//const COMMERCIAL = { icon: 'building', label: 'Komerční', value: RealEstateType.COMMERCIAL },
	//const OTHER = { icon: 'garage', label: 'Ostatní', value: RealEstateType.OTHER },

	const baseSelectIcons: SelectItem[] = [APARTMENT, HOUSE];
	const extendedSelectItems: SelectItem[] = [...baseSelectIcons, LAND];

	return fullSet ? extendedSelectItems : baseSelectIcons;
};
