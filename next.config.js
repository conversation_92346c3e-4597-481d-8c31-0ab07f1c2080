// @ts-check
/* eslint-disable @typescript-eslint/no-var-requires */
const withBundleAnalyzer = require('@next/bundle-analyzer')({
	enabled: process.env.ANALYZE === 'true',
});
const localesData = require('./src/i18n/supportedLocales.js');
/**
 * @type {import('./src/i18n/exportLocalizedUrls').LocalizedUrls}
 */
const rewritesMap = require('./src/i18n/localizedUrls.json');

const { Locale: localesEnum, defaultLocale } = localesData;
const locales = Object.values(localesEnum);

/**
 * @type {import('next').NextConfig}
 **/
const nextConfig = {
	redirects: async () => [
		{
			source: '/',
			destination: '/lp-ppc',
			has: [
				{
					type: 'query',
					key: 'filter',
				},
			],
			permanent: true,
		},
		{
			source: '/test-byt-3-1-brno',
			destination:
				'/lp-ppc?filter=eyJhZFR5cGUiOiJTYWxlIiwicmVhbEVzdGF0ZVR5cGUiOiJBcGFydG1lbnQiLCJsb2NhdGlvbklkIjoiLTQzODE3MSIsImRpc3Bvc2l0aW9uIjpbIjQra2siLCIzKzEiXSwibGFuZFR5cGUiOltdLCJyYWRpdXMiOjAsImFyZWEiOnsiZ3RlIjowLCJsdGUiOm51bGx9LCJsYW5kQXJlYSI6eyJndGUiOjAsImx0ZSI6bnVsbH0sInByaWNlIjp7Imd0ZSI6MCwibHRlIjpudWxsfSwiZW1haWwiOiIiLCJleGFjdE1hdGNoIjpmYWxzZSwic3RhdGUiOiJBY3RpdmUiLCJjb25kaXRpb25zIjpmYWxzZX0',
			permanent: true,
		},
		{
			source: '/prodej-byt',
			destination:
				'/lp-ppc?filter=eyJhZFR5cGUiOiJTYWxlIiwicmVhbEVzdGF0ZVR5cGUiOiJBcGFydG1lbnQiLCJsb2NhdGlvbklkIjoiIiwiZGlzcG9zaXRpb24iOltdLCJsYW5kVHlwZSI6W10sInJhZGl1cyI6MCwiYXJlYSI6eyJndGUiOjAsImx0ZSI6bnVsbH0sImxhbmRBcmVhIjp7Imd0ZSI6MCwibHRlIjpudWxsfSwicHJpY2UiOnsiZ3RlIjowLCJsdGUiOm51bGx9LCJlbWFpbCI6IiIsImV4YWN0TWF0Y2giOmZhbHNlLCJzdGF0ZSI6IkFjdGl2ZSIsImNvbmRpdGlvbnMiOnRydWV9',
			permanent: true,
		},
		{
			source: '/prodej-dum',
			destination:
				'/lp-ppc?filter=eyJhZFR5cGUiOiJTYWxlIiwicmVhbEVzdGF0ZVR5cGUiOiJIb3VzZSIsImxvY2F0aW9uSWQiOiIiLCJkaXNwb3NpdGlvbiI6W10sImxhbmRUeXBlIjpbXSwicmFkaXVzIjowLCJhcmVhIjp7Imd0ZSI6MCwibHRlIjpudWxsfSwibGFuZEFyZWEiOnsiZ3RlIjowLCJsdGUiOm51bGx9LCJwcmljZSI6eyJndGUiOjAsImx0ZSI6bnVsbH0sImVtYWlsIjoiIiwiZXhhY3RNYXRjaCI6ZmFsc2UsInN0YXRlIjoiQWN0aXZlIiwiY29uZGl0aW9ucyI6dHJ1ZX0',
			permanent: true,
		},
		{
			source: '/prodej-pozemek',
			destination:
				'/lp-ppc?filter=eyJhZFR5cGUiOiJTYWxlIiwicmVhbEVzdGF0ZVR5cGUiOiJMYW5kIiwibG9jYXRpb25JZCI6IiIsImRpc3Bvc2l0aW9uIjpbXSwibGFuZFR5cGUiOltdLCJyYWRpdXMiOjAsImFyZWEiOnsiZ3RlIjowLCJsdGUiOm51bGx9LCJsYW5kQXJlYSI6eyJndGUiOjAsImx0ZSI6bnVsbH0sInByaWNlIjp7Imd0ZSI6MCwibHRlIjpudWxsfSwiZW1haWwiOiIiLCJleGFjdE1hdGNoIjpmYWxzZSwic3RhdGUiOiJBY3RpdmUiLCJjb25kaXRpb25zIjp0cnVlfQ',
			permanent: true,
		},
		{
			source: '/pronajem-byt',
			destination:
				'/lp-ppc?filter=eyJhZFR5cGUiOiJSZW50IiwicmVhbEVzdGF0ZVR5cGUiOiJBcGFydG1lbnQiLCJsb2NhdGlvbklkIjoiIiwiZGlzcG9zaXRpb24iOltdLCJsYW5kVHlwZSI6W10sInJhZGl1cyI6MCwiYXJlYSI6eyJndGUiOjAsImx0ZSI6bnVsbH0sImxhbmRBcmVhIjp7Imd0ZSI6MCwibHRlIjpudWxsfSwicHJpY2UiOnsiZ3RlIjowLCJsdGUiOm51bGx9LCJlbWFpbCI6IiIsImV4YWN0TWF0Y2giOmZhbHNlLCJzdGF0ZSI6IkFjdGl2ZSIsImNvbmRpdGlvbnMiOnRydWV9',
			permanent: true,
		},
		{
			source: '/pronajem-dum',
			destination:
				'/lp-ppc?filter=eyJhZFR5cGUiOiJSZW50IiwicmVhbEVzdGF0ZVR5cGUiOiJIb3VzZSIsImxvY2F0aW9uSWQiOiIiLCJkaXNwb3NpdGlvbiI6W10sImxhbmRUeXBlIjpbXSwicmFkaXVzIjowLCJhcmVhIjp7Imd0ZSI6MCwibHRlIjpudWxsfSwibGFuZEFyZWEiOnsiZ3RlIjowLCJsdGUiOm51bGx9LCJwcmljZSI6eyJndGUiOjAsImx0ZSI6bnVsbH0sImVtYWlsIjoiIiwiZXhhY3RNYXRjaCI6ZmFsc2UsInN0YXRlIjoiQWN0aXZlIiwiY29uZGl0aW9ucyI6dHJ1ZX0',
			permanent: true,
		},
		{
			source: '/pronajem-pozemek',
			destination:
				'/lp-ppc?filter=eyJhZFR5cGUiOiJSZW50IiwicmVhbEVzdGF0ZVR5cGUiOiJMYW5kIiwibG9jYXRpb25JZCI6IiIsImRpc3Bvc2l0aW9uIjpbXSwibGFuZFR5cGUiOltdLCJyYWRpdXMiOjAsImFyZWEiOnsiZ3RlIjowLCJsdGUiOm51bGx9LCJsYW5kQXJlYSI6eyJndGUiOjAsImx0ZSI6bnVsbH0sInByaWNlIjp7Imd0ZSI6MCwibHRlIjpudWxsfSwiZW1haWwiOiIiLCJleGFjdE1hdGNoIjpmYWxzZSwic3RhdGUiOiJBY3RpdmUiLCJjb25kaXRpb25zIjp0cnVlfQ',
			permanent: true,
		},
	],
	sassOptions: {
		prependData: `
			$breakpoints: ( sm: '480px', md: '750px', lg: '1000px', xl: '1200px' );
			$breakpointsVars: (
				smUp: 'min-width: 480px',
				smDown: 'max-width: 479.98px',
				mdUp: 'min-width: 750px',
				mdDown: 'max-width: 749.98px',
				lgUp: 'min-width: 1000px',
				lgDown: 'max-width: 999.98px',
				xlUp: 'min-width: 1200px',
				xlDown: 'max-width: 1199.98px',
			);
			$smUp: 'min-width: 480px';
			$smDown: 'max-width: 479.98px';
			$mdUp: 'min-width: 750px';
			$mdDown: 'max-width: 749.98px';
			$lgUp: 'min-width: 1000px';
			$lgDown: 'max-width: 999.98px';
			$xlUp: 'min-width: 1200px';
			$xlDown: 'max-width: 1199.98px';
		`,
	},
	i18n: {
		locales,
		defaultLocale,
		localeDetection: false,
	},

	async rewrites() {
		return rewritesMap.flatMap(({ name, urls }) => {
			const rewrites = [];
			for (const locale of Object.keys(urls)) {
				if (locale === defaultLocale) continue;
				if (name === urls[locale]) continue;
				const url = urls[locale].replace(/(\[(\.\.\.)?(.*?)])/g, (match, p1, p2, p3) => ':' + p3 + (p2 ? '*' : ''));

				rewrites.push({
					source: `/${locale}${url}`,
					destination: `/${locale}${name}`,
					locale: false,
				});
			}

			return rewrites;
		});
	},

	webpack: (config, { isServer }) => {
		if (!isServer) config.resolve.fallback.fs = false;

		config.resolve.alias = {
			...config.resolve.alias,
			classnames: 'clsx',
			'@formatjs/icu-messageformat-parser': '@formatjs/icu-messageformat-parser/no-parser',
		};

		return config;
	},
};

module.exports = withBundleAnalyzer(nextConfig);
