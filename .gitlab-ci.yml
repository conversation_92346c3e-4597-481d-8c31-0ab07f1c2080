stages:
    - deploy

variables:
    HUSKY: 0

default:
    image: sleavely/node-awscli:22.x
    tags:
        - x86

deploy_preview:
    stage: deploy
    except:
        - master
    script:
        - npm install --global vercel
        - vercel pull --yes --environment=preview --token=$VERCEL_TOKEN
        - vercel build --token=$VERCEL_TOKEN
        - vercel deploy --prebuilt  --token=$VERCEL_TOKEN --archive=split-tgz

deploy_production:
    stage: deploy
    only:
        - master
    script:
        - npm install --global vercel
        - vercel pull --yes --environment=production --token=$VERCEL_TOKEN
        - vercel build --prod --token=$VERCEL_TOKEN
        - vercel deploy --prebuilt --prod --token=$VERCEL_TOKEN --archive=split-tgz
