# Visidoo frontend

## Translations and POEditor flow

1. Make sure to have the `POEDITOR_TOKEN` and `POEDITOR_PROJECT_ID` env variables set up.
2. To extract all react-intl messages in the default language (cs) and automatically upload them to POEditor, use the `npm run i18n:upload` command. It automatically takes care of extraction, putting the extracted messages in the `./src/i18n/extracted/cs.json` file. The actual command then sends these extracted messages to POEditor via API.
3. Translate the messages to the relevant target languages inside POEditor itself (`https://poeditor.com` - filter by `Untranslated` to see the messages missing translations).
4. To download and compile all project translations, use the `npm run i18n:download` command. The command takes the POEditor translations via API and saves them to the relevant `./src/i18n/translated/${locale}.json` file. It automatically takes care of compiling these messages and save them to the relevant `./src/i18n/compiled/${locale}.json` file, from where they are served for the app.
