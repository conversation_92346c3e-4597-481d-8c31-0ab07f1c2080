{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "baseUrl": "src", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "preserve", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "moduleResolution": "node", "noEmit": true, "noUnusedLocals": true, "noUnusedParameters": true, "preserveConstEnums": true, "removeComments": false, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "es5", "incremental": true}, "exclude": ["node_modules"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.d.ts"]}